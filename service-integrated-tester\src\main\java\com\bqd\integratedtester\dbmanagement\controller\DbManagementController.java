package com.bqd.integratedtester.dbmanagement.controller;

import cn.hutool.core.util.ObjectUtil;
import com.bqd.base.response.Response;
import com.bqd.integratedtester.dbmanagement.service.DbManagementService;
import com.bqd.model.dbmanagement.DbConnectionInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 数据库信息管理
 * <AUTHOR>
 * @CreateTime 2025-04-11
 */
@RestController
@RequestMapping("/dbManagement")
public class DbManagementController {

    @Autowired
    private DbManagementService dbManagementService;

    /**
     * 查询数据库信息
     * @param dbConnectionInfo
     * @return
     */
    @PostMapping("/info/list")
    public Response list(@RequestBody(required = false) DbConnectionInfo dbConnectionInfo) {
        return Response.success(dbManagementService.list(ObjectUtil.isNull(dbConnectionInfo) ? new DbConnectionInfo() : dbConnectionInfo));
    }

    /**
     * 添加数据库信息
     * @param dbConnectionInfo
     * @return
     */
    @PostMapping("/info/add")
    public Response addInfo(@RequestBody DbConnectionInfo dbConnectionInfo) {
        dbManagementService.addInfo(dbConnectionInfo);
        return Response.success();
    }

    /**
     * 修改数据库信息
     * @param dbConnectionInfo
     * @return
     */
    @PostMapping("/info/update")
    public Response updateInfo(@RequestBody DbConnectionInfo dbConnectionInfo) {
        dbManagementService.updateInfo(dbConnectionInfo);
        return Response.success();
    }

    /**
     * 删除数据库信息
     * @param id
     * @return
     */
    @GetMapping("/info/delete/{id}")
    public Response deleteInfo(@PathVariable String id) {
        dbManagementService.deleteInfo(id);
        return Response.success();
    }

    /**
     * 测试数据库连接
     * @param id
     * @return
     */
    @GetMapping("/testDbConnection/{id}")
    public Response testDbConnection(@PathVariable String id) {
        dbManagementService.testDBConnection(id);
        return Response.success();
    }

    /**
     * 测试数据库连接
     * @param dbType
     * @param url
     * @param username
     * @param password
     * @return
     */
    @GetMapping("/testDbConnection")
    public Response testDbConnection(@RequestParam String dbType, @RequestParam String url, @RequestParam String username, @RequestParam String password) {
        dbManagementService.testDBConnection(dbType, url, username, password);
        return Response.success();
    }
}
