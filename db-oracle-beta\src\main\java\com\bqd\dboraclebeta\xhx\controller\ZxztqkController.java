package com.bqd.dboraclebeta.xhx.controller;

import com.bqd.dboraclebeta.xhx.mapper.XhxRbZxztqkMapper;
import com.bqd.model.xhxrb.XhxRbZxztqk;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-25
 */
@RestController
@RequestMapping("/xhxrb/zxztqk")
public class ZxztqkController {

    @Autowired
    private XhxRbZxztqkMapper xhxRbZxztqkMapper;

    @GetMapping("/selectAllDesc")
    public List<XhxRbZxztqk> selectAllDesc() {
        return xhxRbZxztqkMapper.selectAllDesc();
    }

    @GetMapping("/pcdInsertNames")
    public void pcdInsertNames() {
        xhxRbZxztqkMapper.pcdInsertNames();
    }

    @GetMapping("/pcdInsertTestsetTotal")
    public void pcdInsertTestsetTotal() {
        xhxRbZxztqkMapper.pcdInsertTestsetTotal();
    }

    @GetMapping("/pcdUpdateCaseCount")
    public void pcdUpdateCaseCount() {
        xhxRbZxztqkMapper.pcdUpdateCaseCount();
    }

    @GetMapping("/pcdUpdateExecuteCount")
    public void pcdUpdateExecuteCount() {
        xhxRbZxztqkMapper.pcdUpdateExecuteCount();
    }

    @GetMapping("/pcdUpdatePassedCount")
    public void pcdUpdatePassedCount() {
        xhxRbZxztqkMapper.pcdUpdatePassedCount();
    }

    @GetMapping("/pcdUpdateFailedCount")
    public void pcdUpdateFailedCount() {
        xhxRbZxztqkMapper.pcdUpdateFailedCount();
    }

    @GetMapping("/pcdUpdateBlockCount")
    public void pcdUpdateBlockCount() {
        xhxRbZxztqkMapper.pcdUpdateBlockCount();
    }

    @GetMapping("/pcdUpdateExecutingCount")
    public void pcdUpdateExecutingCount() {
        xhxRbZxztqkMapper.pcdUpdateExecutingCount();
    }

    @GetMapping("/pcdUpdateCancelCount")
    public void pcdUpdateCancelCount() {
        xhxRbZxztqkMapper.pcdUpdateCancelCount();
    }

    @GetMapping("/pcdUpdateTodoCount")
    public void pcdUpdateTodoCount() {
        xhxRbZxztqkMapper.pcdUpdateTodoCount();
    }

    @GetMapping("/pcdUpdateExecuteRate")
    public void pcdUpdateExecuteRate() {
        xhxRbZxztqkMapper.pcdUpdateExecuteRate();
    }

    @GetMapping("/pcdUpdatePassRate")
    public void pcdUpdatePassRate() {
        xhxRbZxztqkMapper.pcdUpdatePassRate();
    }

    @GetMapping("/pcdUpdateDayExecuteCount")
    public void pcdUpdateDayExecuteCount(@RequestParam String startTime, @RequestParam String endTime) {
        xhxRbZxztqkMapper.pcdUpdateDayExecuteCount(startTime, endTime);
    }

    @GetMapping("/pcdUpdateDayPassedCount")
    public void pcdUpdateDayPassedCount(@RequestParam String startTime, @RequestParam String endTime) {
        xhxRbZxztqkMapper.pcdUpdateDayPassedCount(startTime, endTime);
    }

    @GetMapping("/pcdUpdateDayFailedCount")
    public void pcdUpdateDayFailedCount(@RequestParam String startTime, @RequestParam String endTime) {
        xhxRbZxztqkMapper.pcdUpdateDayFailedCount(startTime, endTime);
    }

    @GetMapping("/pcdUpdatePlanProgress")
    public void pcdUpdatePlanProgress(@RequestParam String roundName) {
        xhxRbZxztqkMapper.pcdUpdatePlanProgress(roundName);
    }

    @GetMapping("/pcdUpdateProgressBias")
    public void pcdUpdateProgressBias() {
        xhxRbZxztqkMapper.pcdUpdateProgressBias();
    }

    @GetMapping("/pcdUpdateValidBugCount")
    public void pcdUpdateValidBugCount(@RequestParam String roundName, @RequestParam String systemPrefix) {
        xhxRbZxztqkMapper.pcdUpdateValidBugCount(roundName, systemPrefix);
    }

    @GetMapping("/pcdUpdateTotalBugRate")
    public void pcdUpdateTotalBugRate() {
        xhxRbZxztqkMapper.pcdUpdateTotalBugRate();
    }

}
