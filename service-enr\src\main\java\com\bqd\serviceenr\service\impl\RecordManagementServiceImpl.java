package com.bqd.serviceenr.service.impl;

import cn.hutool.core.util.PageUtil;
import com.bqd.model.common.PageDto;
import com.bqd.model.esbnetworkreplay.*;
import com.bqd.model.esbnetworkreplay.EsbRecordDetail;
import com.bqd.model.esbnetworkreplay.EsbRecordInfo;
import com.bqd.base.rpc.esbnetworkreplay.EsbRecordDetailMapper;
import com.bqd.base.rpc.esbnetworkreplay.EsbRecordInfoMapper;
import com.bqd.serviceenr.service.RecordManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-07-23
 */
@Service
public class RecordManagementServiceImpl implements RecordManagementService {

    @Autowired
    private EsbRecordInfoMapper esbRecordInfoMapper;
    @Autowired
    private EsbRecordDetailMapper esbRecordDetailMapper;

    /**
     * 根据条件查询记录信息
     * @param pageNo
     * @param pageSize
     * @param recordInfoDto
     */
    @Override
    public PageDto<EsbRecordInfo> fetchInfoByCondition(Integer pageNo, Integer pageSize, EsbRecordInfoDto recordInfoDto) {
        int totalCount = esbRecordInfoMapper.countByCondition(recordInfoDto);
        int pageCount = PageUtil.totalPage(totalCount, pageSize);
        if (pageCount < pageNo) {
            pageNo = pageCount;
        }
        int startRow = PageUtil.getStart(pageNo, pageSize) + 1;
        int endRow = PageUtil.getEnd(pageNo, pageSize);
        List<EsbRecordInfo> esbRecordInfoList = esbRecordInfoMapper.selectByCondition(startRow, endRow, recordInfoDto);
        return new PageDto<>(pageNo, pageSize, pageCount, totalCount, esbRecordInfoList);
    }

    /**
     * 根据列名获取该列数据（去重）
     * @param colNameList
     * @return
     */
    @Override
    public List<EsbRecordInfo> fetchInfoColData(List<String> colNameList) {
        return esbRecordInfoMapper.selectDistinctByColName(colNameList);
    }

    /**
     * 根据infoId和列名获取该条记录的列数据
     * @param recordId
     * @param colNameList
     * @return
     */
    @Override
    public EsbRecordDetail fetchDetailColData(String recordId, List<String> colNameList) {
        return esbRecordDetailMapper.selectByIdAndColName(recordId, colNameList);
    }

    /**
     * 根据接口id和开始结束时间删除记录
     * @param interfaceId
     * @param reqtStartTime
     * @param reqtEndTime
     */
    @Override
    public void deleteRecord(String interfaceId, String infoId, Long reqtStartTime, Long reqtEndTime) {
        EsbRecordInfoDto recordInfoDto = new EsbRecordInfoDto(reqtStartTime, reqtEndTime);
        recordInfoDto.setInterfaceId(interfaceId);
        recordInfoDto.setRecordId(infoId);
        List<EsbRecordInfo> esbRecordInfoList = esbRecordInfoMapper.selectByCondition(null, null, recordInfoDto);
        for (EsbRecordInfo esbRecordInfo : esbRecordInfoList) {
            esbRecordInfoMapper.deleteById(esbRecordInfo.getRecordId());
            esbRecordDetailMapper.deleteById(esbRecordInfo.getRecordId());
        }
    }

}
