package com.bqd.serviceenr.service.impl;

import cn.hutool.core.util.IdUtil;
import com.bqd.model.esbnetworkreplay.EsbReplaceField;
import com.bqd.model.esbnetworkreplay.EsbReplaceVariable;
import com.bqd.base.rpc.esbnetworkreplay.EsbReplaceFieldMapper;
import com.bqd.base.rpc.esbnetworkreplay.EsbReplaceVariableMapper;
import com.bqd.serviceenr.service.ReplaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-08-27
 */
@Service
public class ReplaceServiceImpl implements ReplaceService {

    @Autowired
    private EsbReplaceFieldMapper esbReplaceFieldMapper;
    @Autowired
    private EsbReplaceVariableMapper esbReplaceVariableMapper;

    @Override
    public List<EsbReplaceField> getReplaceField(String interfaceId) {
        return esbReplaceFieldMapper.selectByInterfaceId(interfaceId);
    }

    @Override
    public List<EsbReplaceVariable> getReplaceVariable() {
        return esbReplaceVariableMapper.selectAll();
    }

    @Override
    public void deleteReplaceField(String id) {
        esbReplaceFieldMapper.deleteById(id);
    }

    @Override
    public void addReplaceVaribale(EsbReplaceVariable esbReplaceVariable) {
        esbReplaceVariable.setId(IdUtil.simpleUUID());
        esbReplaceVariableMapper.insert(esbReplaceVariable);
    }

    @Override
    public void deleteReplaceVariable(String id) {
        esbReplaceVariableMapper.deleteById(id);
    }

    @Override
    public void addReplaceField(EsbReplaceField esbReplaceField) {
        esbReplaceField.setId(IdUtil.simpleUUID());
        esbReplaceFieldMapper.insert(esbReplaceField);
    }
}
