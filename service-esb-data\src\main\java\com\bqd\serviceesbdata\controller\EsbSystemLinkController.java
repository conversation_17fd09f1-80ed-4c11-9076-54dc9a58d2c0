package com.bqd.serviceesbdata.controller;

import com.bqd.base.response.Response;
import com.bqd.model.common.TreeDto;
import com.bqd.serviceesbdata.service.EsbSystemLinkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-01-06
 */
@RestController
@RequestMapping("/esl")
@Slf4j
public class EsbSystemLinkController {

    @Autowired
    private EsbSystemLinkService esbSystemLinkService;

    @GetMapping("/parseEsbServiceModel")
    public Response parseEsbServiceModel() {
        try {
            esbSystemLinkService.parseEsbServiceModel();
            return Response.success();
        } catch (Exception e) {
            log.error("解析ESB服务模型失败", e);
            return Response.fail("解析ESB服务模型失败");
        }
    }

    @GetMapping("/parseEsbSystemLink")
    public Response parseEsbSystemLink() {
        try {
            esbSystemLinkService.parseSystemLink();
            return Response.success();
        } catch (Exception e){
            log.error("解析ESB系统间调用关系失败", e);
            return Response.fail("解析ESB系统间调用关系失败");
        }
    }

    @GetMapping("/parseEslServerConfig")
    public Response parseEslServerConfig() {
        try {
            esbSystemLinkService.parseEslServerConfig();
            return Response.success();
        } catch (Exception e) {
            log.error("解析ESB配置项失败", e);
            return Response.fail("解析ESB配置项失败");
        }
    }

    @GetMapping("/getConfigList")
    public Response getConfigList() {
        try {
            List<TreeDto> configList = esbSystemLinkService.getConfigList();
            return Response.success(configList);
        } catch (Exception e) {
            log.error("获取配置项列表失败", e);
            return Response.fail("获取配置项列表失败");
        }
    }

    @GetMapping("/getESLTreeListByConfigId")
    public Response getServiceProviderListByConfigId(@RequestParam("configId") String configId){
        try {
            List<TreeDto> treeDtoList = esbSystemLinkService.getESLTreeListByConfigId(configId);
            return Response.success(treeDtoList);
        } catch (Exception e){
            log.error("获取服务提供方列表失败", e);
            return Response.fail("获取服务提供方列表失败");
        }
    }

    @GetMapping("/getEsbInterfaceList")
    public Response getESBInterfaceList() {
        try {
            List<TreeDto> esbInterfaceList = esbSystemLinkService.getESBInterfaceList();
            return Response.success(esbInterfaceList);
        } catch (Exception e) {
            log.error("获取ESB接口列表失败", e);
            return Response.fail("获取ESB接口列表失败");
        }
    }

    @GetMapping("/getESLTreeListByInterfaceId")
    public Response getESLTreeListByInterfaceId(@RequestParam("interfaceId") String interfaceId) {
        try {
            TreeDto treeDto = esbSystemLinkService.getESLTreeListByInterfaceId(interfaceId);
            return Response.success(treeDto);
        } catch (Exception e) {
            log.error("获取发布范围列表失败", e);
            return Response.fail("获取发布范围列表失败");
        }
    }

    @GetMapping("/getESLTreeListByServiceProvider")
    public Response getESLTreeListByServiceProvider(@RequestParam("serviceProvider") String serviceProvider) {
        try {
            List<TreeDto> treeDto = esbSystemLinkService.getESLTreeListByServiceProvider(serviceProvider);
            return Response.success(treeDto);
        } catch (Exception e){
            log.error("获取发布范围列表失败", e);
            return Response.fail("获取发布范围列表失败");
        }
    }

}
