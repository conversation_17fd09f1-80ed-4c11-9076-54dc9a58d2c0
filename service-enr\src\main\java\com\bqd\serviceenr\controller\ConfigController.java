package com.bqd.serviceenr.controller;

import com.bqd.base.response.Response;
import com.bqd.model.esbnetworkreplay.RequestResendFormDto;
import com.bqd.model.esbnetworkreplay.RecordDBEnv;
import com.bqd.model.esbnetworkreplay.RequestResendDBInfo;
import com.bqd.serviceenr.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-07-02
 */
@RestController
@RequestMapping("/config")
@Slf4j
public class ConfigController {

    @Autowired
    private ConfigService configService;

    /**
     * 获取录制环境数据库配置列表
     *
     * @return
     */
    @GetMapping("/getRecordDBEnv")
    public Response getRecordDBEnv() {
        return Response.success(configService.getRecordDBEnv());
    }

    /**
     * 添加录制环境数据库配置
     * @param recordDBEnv
     * @return
     */
    @PostMapping("/addRecordDBEnv")
    public Response addRecordDBEnv(@RequestBody RecordDBEnv recordDBEnv) {
        configService.addRecordDBEnv(recordDBEnv);
        return Response.success();
    }

    /**
     * 修改录制环境数据库配置
     * @param recordDBEnv
     * @return
     */
    @PostMapping("/updateRecordDBEnv")
    public Response updateRecordDBEnv(@RequestBody RecordDBEnv recordDBEnv) {
        configService.updateRecordDBEnv(recordDBEnv);
        return Response.success();
    }

    /**
     * 根据id删除录制环境数据库配置
     * @param id
     * @return
     */
    @GetMapping("/deleteRecordDBEnv")
    public Response deleteRecordDBEnv(@RequestParam("id") String id) {
        configService.deleteRecordDBEnv(id);
        return Response.success();
    }

    /**
     * 获取重发数据库列表
     * @return
     */
    @GetMapping("/getRequestResendDBList")
    public Response getRequestResendDBList() {
        return Response.success(configService.getRequestResendDBList());
    }

    @PostMapping("/addRequestResendDBInfo")
    public Response addRequestResendDBInfo(@RequestBody RequestResendDBInfo requestResendDBInfo) {
        configService.addRequestResendDBInfo(requestResendDBInfo);
        return Response.success();
    }

    @PostMapping("/updateRequestResendDBInfo")
    public Response updateRequestResendDBInfo(@RequestBody RequestResendDBInfo requestResendDBInfo) {
        configService.updateRequestResendDBInfo(requestResendDBInfo);
        return Response.success();
    }

    @GetMapping("/deleteRequestResendDBInfo")
    public Response deleteRequestResendDBInfo(@RequestParam("id") String id) {
        configService.deleteRequestResendDBEnv(id);
        return Response.success();
    }

    @GetMapping("/testDBConnection")
    public Response testDBConnection(@RequestParam("id") String id) {
        return Response.success(configService.testDBConnection(id));
    }

    @PostMapping("/requestResend")
    public Response requestResend(@RequestBody RequestResendFormDto requestResendFormDto) {
        configService.requestResend(requestResendFormDto);
        return Response.success();
    }

}
