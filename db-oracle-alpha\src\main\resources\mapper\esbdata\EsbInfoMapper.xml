<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbdata.mapper.EsbInfoMapper">

    <resultMap id="esbInfo" type="com.bqd.model.esbdata.EsbInfo">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
        <result column="SVC_CORR_ID" property="svcCorrId" javaType="java.lang.String"/>
        <result column="EVENT_TYPE_NM" property="eventTypeNM" javaType="java.lang.String"/>
        <result column="EVENT_TIME" property="eventTime" javaType="java.lang.String"/>
        <result column="URL" property="url" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO TB_ESB_INFO
            (ID, INTERFACE_ID, SVC_CORR_ID, EVENT_TYPE_NM, EVENT_TIME, URL)
        VALUES (#{id}, #{interfaceId,jdbcType=VARCHAR}, #{svcCorrId,jdbcType=VARCHAR}, #{eventTypeNM,jdbcType=VARCHAR},
                #{eventTime,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR})
    </insert>

    <select id="selectPagedByInterfaceId" resultMap="esbInfo">
        SELECT ROWNUM ROW_NUM, ID, INTERFACE_ID, SVC_CORR_ID, EVENT_TYPE_NM, EVENT_TIME, URL
        FROM (SELECT * FROM TB_ESB_INFO WHERE INTERFACE_ID = #{interfaceId} ORDER BY EVENT_TIME DESC)
        WHERE ROW_NUM BETWEEN #{startRow} AND #{endRow}
    </select>

    <select id="countByInterfaceId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM TB_ESB_INFO
        WHERE INTERFACE_ID = #{interfaceId}
    </select>

    <select id="selectByInterfaceId" resultMap="esbInfo">
        SELECT *
        FROM TB_ESB_INFO
        WHERE INTERFACE_ID = #{interfaceId}
    </select>

    <delete id="deleteById">
        DELETE
        FROM TB_ESB_INFO
        WHERE ID = #{id}
    </delete>

</mapper>
