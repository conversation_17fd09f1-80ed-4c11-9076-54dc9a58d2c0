package com.bqd.dboraclealpha.esbnetworkreplay.mapper;

import com.bqd.model.esbnetworkreplay.EsbCompareIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EsbCompareIgnoreMapper {
    EsbCompareIgnore selectIgnoredField(@Param("interfaceId") String interfaceId);

    List<EsbCompareIgnore> selectIgnoredFieldGlobal();

    void insert(EsbCompareIgnore esbCompareIgnore);

    void updateById(EsbCompareIgnore esbCompareIgnore);

    void deleteIgnoredFieldGlobal(@Param("ignoredField") String ignoredField);
}
