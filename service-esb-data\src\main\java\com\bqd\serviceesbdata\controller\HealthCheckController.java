package com.bqd.serviceesbdata.controller;

import com.bqd.base.response.Response;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-11-27
 */
@RestController
public class HealthCheckController {
    @GetMapping("/healthCheck")
    public Response healthCheck() {
        return Response.success("service-esb-data");
    }
}
