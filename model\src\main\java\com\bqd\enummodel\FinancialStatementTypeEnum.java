package com.bqd.enummodel;

import lombok.Getter;

@Getter
public enum FinancialStatementTypeEnum {

    FUND_STATUS("资金状况日报表_支行/资金状况日报表_分行", "fund-status"),
    FUND_STATUS_ALL("资金状况日报表_全行", "fund-status-all"),
    BALANCE_SHEET_PBOC("资产负债简报（人民银行口径）", "balance-sheet-pboc"),
    BALANCE_SHEET_BIR("资产负债简报（银保监口径）", "balance-sheet-bir"),
    RETAIL_DEPOSIT("零售--存款日报表/零售--存款日报表_含网点", "retail-deposit"),
    RETAIL_FINANCIAL_ASSET("零售--金融资产报表日报表", "retail-financial-asset"),
    RETAIL_CUSTOMER_AVG_ASSET("零售-客户月日均金融资产分层日报表", "retail-customer-avg-asset"),
    PERSONAL_LOAN("个贷--个人贷款日报表/个贷--个人普惠贷款日报表", "personal-loan"),
    BRANCH_DAILY_DEPOSITS("各分支机构日均存款情况表", "branch-daily-deposits"),
    INDICATOR_CHANGE("指标变化明细日报表", "indicator-change"),
    CREDIT_RISK_MONITORING("信用风险监测日报表", "credit-risk-monitoring"),
    MAJOR_CUSTOMER("全行大客户列表/分支行大客户列表", "major-customer");

    private final String label;
    private final String value;

    FinancialStatementTypeEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }
}
