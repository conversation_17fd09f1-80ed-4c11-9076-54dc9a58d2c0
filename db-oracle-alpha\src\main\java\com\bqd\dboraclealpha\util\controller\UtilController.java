package com.bqd.dboraclealpha.util.controller;

import com.bqd.dboraclealpha.util.mapper.UtilMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-29
 */
@RestController
@RequestMapping("/util")
public class UtilController {

    @Autowired
    private UtilMapper utilMapper;

    @GetMapping("/truncateTable")
    public void truncateTable(@RequestParam String tableName){
        utilMapper.truncateTable(tableName);
    }

}
