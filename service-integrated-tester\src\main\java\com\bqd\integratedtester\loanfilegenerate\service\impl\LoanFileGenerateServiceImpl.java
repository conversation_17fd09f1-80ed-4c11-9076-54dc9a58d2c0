package com.bqd.integratedtester.loanfilegenerate.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.base.rpc.dbinfo.DbConnectionInfoMapper;
import com.bqd.base.tools.DbTool;
import com.bqd.integratedtester.loanfilegenerate.service.LoanFileGenerateService;
import com.bqd.model.common.CommonReqtListDto;
import com.bqd.model.dbmanagement.DbConnectionInfo;
import com.bqd.model.loanfilegenerate.BusinessApply;
import com.bqd.model.loanfilegenerate.CustomerInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;

/**
 * @Description: 贷款文件生成服务实现类
 * <AUTHOR>
 * @CreateTime 2025-04-14
 */
@Service
public class LoanFileGenerateServiceImpl implements LoanFileGenerateService {

    private static final String BUSINESS_APPLY_QUERY_SQL = "SELECT * FROM BUSINESS_APPLY WHERE THIRDAPPLYNO = ?";
    private static final String CUSTOMER_INFO_QUERY_SQL = "SELECT CERTID FROM CUSTOMER_INFO WHERE CUSTOMERID = ?";

    @Value("${path.loan-file-generate}")
    private String loanFileGenerateWorkDir;

    @Autowired
    private DbConnectionInfoMapper dbConnectionInfoMapper;

    @Override
    public BusinessApply getBusinessApplyEntity(String loanId, String dbConnectionInfoId) {
        try {
            DbConnectionInfo dbConnectionInfo = new DbConnectionInfo();
            dbConnectionInfo.setId(dbConnectionInfoId);
            dbConnectionInfo = dbConnectionInfoMapper.selectByCondition(dbConnectionInfo).get(0);
            Connection connection = DbTool.getDbConnection(dbConnectionInfo.getDbType(), dbConnectionInfo.getUrl(), dbConnectionInfo.getUsername(), dbConnectionInfo.getPassword());

            PreparedStatement preparedStatement = connection.prepareStatement(BUSINESS_APPLY_QUERY_SQL);
            preparedStatement.setString(1, loanId);
            ResultSet resultSet = preparedStatement.executeQuery();
            if (!resultSet.next()) {
                throw new CustomizedException(StrUtil.format("loan_id: 【{}】未查询到记录", loanId));
            }
            return new BusinessApply(
                    resultSet.getString("SERIALNO"),
                    DateUtil.format(DateUtil.parse(resultSet.getString("OCCURDATE")), "yyyyMMdd"),
                    resultSet.getString("CUSTOMERID"),
                    resultSet.getString("CUSTOMERNAME"),
                    resultSet.getString("BUSINESSTYPE"),
                    resultSet.getString("PRODUCTID"),
                    resultSet.getString("SPECIFICSERIALNO"),
                    resultSet.getString("OCCURTYPE"),
                    resultSet.getString("REVOLVEFLAG"),
                    resultSet.getString("CONTRACTSIGNTYPE"),
                    resultSet.getString("CONTRACTARTIFICIALNO"),
                    resultSet.getString("BUSINESSCURRENCY"),
                    resultSet.getString("BUSINESSSUM"),
                    DateUtil.format(DateUtil.parse(resultSet.getString("MATURITYDATE")), "yyyyMMdd"),
                    resultSet.getString("DRAWDOWNTYPE"),
                    resultSet.getString("DIRECTION"),
                    resultSet.getString("PURPOSETYPE"),
                    resultSet.getString("PURPOSEDESCRIPTION"),
                    resultSet.getString("VOUCHTYPE"),
                    resultSet.getString("APPROVEORGID"),
                    resultSet.getString("APPROVEUSERID"),
                    resultSet.getString("APPROVEDATE"),
                    resultSet.getString("APPROVESTATUS"),
                    resultSet.getString("OPERATEORGID"),
                    resultSet.getString("OPERATEUSERID"),
                    resultSet.getString("OPERATEDATE"),
                    resultSet.getString("INPUTORGID"),
                    resultSet.getString("INPUTUSERID"),
                    resultSet.getString("INPUTDATE"),
                    resultSet.getString("UPDATEDATE"),
                    resultSet.getString("REMARK"),
                    resultSet.getString("TEMPSAVEFLAG"),
                    resultSet.getString("SYSTEMCHANNELFLAG"),
                    resultSet.getString("ACCOUNTINGORGID"),
                    resultSet.getString("PAYMENTTYPE"),
                    resultSet.getString("BUSINESSPRIORITY"),
                    resultSet.getString("BUSINESSTERMDAY"),
                    resultSet.getString("BATCHSERIALNO"),
                    resultSet.getString("BUSINESSTERMMONTH"),
                    resultSet.getString("SALESSTORE"),
                    resultSet.getString("SALESPERSON"),
                    resultSet.getString("ACTIVENO"),
                    resultSet.getString("INTERESTMODE"),
                    resultSet.getString("PAYMENTSOURCE"),
                    resultSet.getString("PREPAYMENTFLAG"),
                    resultSet.getString("USEWAYDESCRIPE"),
                    resultSet.getString("USECOMMPANYNAME"),
                    resultSet.getString("USECOMMPANYTYPE"),
                    resultSet.getString("USECOMMPANYCERTTYPE"),
                    resultSet.getString("INDUSTOR"),
                    resultSet.getString("CONTRACTSIGNDATE"),
                    resultSet.getString("GXBUSINESSSUM"),
                    resultSet.getString("GXCONTRACT"),
                    resultSet.getString("THIRDAPPLYNO"),
                    resultSet.getString("CHANNELNO"),
                    resultSet.getString("PHONENO"),
                    resultSet.getString("USECOMMPANYCERTID"),
                    resultSet.getString("PARTNERCODE"),
                    resultSet.getString("POLICYNO"),
                    resultSet.getString("INSCOMTYPE"),
                    resultSet.getString("INSCOMPANY"),
                    resultSet.getString("ISQDBANK"),
                    resultSet.getString("MARRIAGE"),
                    resultSet.getString("EDUCATIONAL"),
                    resultSet.getString("COMPANYNATURE"),
                    resultSet.getString("INDUSTRY"),
                    resultSet.getString("OCCUPATION"),
                    resultSet.getString("ADDRESS1"),
                    resultSet.getString("MOBILETELEPHONE"),
                    resultSet.getString("RULESTATUS"),
                    resultSet.getString("PUTOUTFLAG"),
                    resultSet.getString("REGISTRATIONTIME"),
                    resultSet.getString("FIRSTORDERAPPLICATIONTIME"),
                    resultSet.getString("AGE"),
                    resultSet.getString("FACERECOGNITIONSCORE"),
                    resultSet.getString("CREDITCARDNO"),
                    resultSet.getString("PARTNERSSCORE"),
                    resultSet.getString("ANTIFRAUDSCORE"),
                    resultSet.getString("PARTNERSRATING"),
                    resultSet.getString("PARTNERSAPPROVEFLAG"),
                    resultSet.getString("ISQUERYPBOC"),
                    resultSet.getString("CREDITAMOUNT"),
                    resultSet.getString("ISNEWCUSTOMER"),
                    resultSet.getString("CREDITTIME"),
                    resultSet.getString("HISTORYOVERDAYS"),
                    resultSet.getString("HISTORYOVERBUINESSSUM"),
                    resultSet.getString("LOANAPPROVENUM15D"),
                    resultSet.getString("CARDACCNUM18M"),
                    resultSet.getString("MAXREPAYMENTS24M"),
                    resultSet.getString("MAXOVERLOAN3M"),
                    resultSet.getString("NOTUSEDRATION3M"),
                    resultSet.getString("LOANENQUIRYNUM9M"),
                    resultSet.getString("OVERACCOUNT9M"),
                    resultSet.getString("LOANQUERYTIME"),
                    resultSet.getString("FIRSTCREDITCARDM"),
                    resultSet.getString("PAYOFFLOANSUM"),
                    resultSet.getString("EARLYOPACCUNCLEAREDLOANSUM"),
                    resultSet.getString("NORCCUSAGERATE"),
                    resultSet.getString("DCBADDEBTS"),
                    resultSet.getString("ACCURATEDCBADDEBT"),
                    resultSet.getString("DCFREEZE"),
                    resultSet.getString("DC3MAPP2TIMES"),
                    resultSet.getString("DC6MAPP3TIMES"),
                    resultSet.getString("DC12MAPP4TIMES"),
                    resultSet.getString("LOANSECONDARYNO"),
                    resultSet.getString("LOANSUSPICIOUSNO"),
                    resultSet.getString("LOANLOSSNO"),
                    resultSet.getString("LOAN3MAPP2TIMES"),
                    resultSet.getString("LOAN6MAPP3TIMES"),
                    resultSet.getString("LOAN12MAPP4TIMES"),
                    resultSet.getString("NORMDCNUM"),
                    resultSet.getString("ISENFORCEM"),
                    resultSet.getString("DCOVERSUM"),
                    resultSet.getString("LOANOVERDUEAMOUNT"),
                    resultSet.getString("ACCOUNTREPAYM"),
                    resultSet.getString("CRSQUERY3M"),
                    resultSet.getString("FIRSTCARDAGE"),
                    resultSet.getString("MAXLINELIMIT"),
                    resultSet.getString("PBOCCRSSCORE"),
                    resultSet.getString("LASTLOANTIME"),
                    resultSet.getString("LASTLOANAMOUNT"),
                    resultSet.getString("LASTLOANTERMS"),
                    resultSet.getString("LOANBALANCE"),
                    resultSet.getString("PAYPRINCIPALAMT"),
                    resultSet.getString("LOANTERMS"),
                    resultSet.getString("OVERDUETERMS12M"),
                    resultSet.getString("OVERDUEAMT12M"),
                    resultSet.getString("LOANAMT12M"),
                    resultSet.getString("LOCATION"),
                    resultSet.getString("CORELOANTYPE"),
                    resultSet.getString("ORIENTATION"),
                    resultSet.getString("BUSNAME"),
                    resultSet.getString("MAININDUSTRY"),
                    resultSet.getString("STORELEVEL"),
                    resultSet.getString("REGDATE"),
                    resultSet.getString("TURNOVER"),
                    resultSet.getString("IDCARDVALIDDATE"),
                    resultSet.getString("IDCARDADDRESS"),
                    resultSet.getString("ISINBLACKLISTTD"),
                    resultSet.getString("ISINBLACKLISTZH"),
                    resultSet.getString("ISINBLACKLISTQH"),
                    resultSet.getString("ISINBLACKLISTBR"),
                    resultSet.getString("ISCREDITCARDBADDEBT"),
                    resultSet.getString("ISACCOUNTBADDEBT"),
                    resultSet.getString("ISCREDITCARDOVERDUE"),
                    resultSet.getString("BSCORE"),
                    resultSet.getString("ISBUSUSER"),
                    resultSet.getString("THIRDAPPLYDATE"),
                    resultSet.getString("PCBGREENFLAG"),
                    resultSet.getString("RELATIVESERIALNO"),
                    resultSet.getString("RELATIVETHIRDAPPLYNO"),
                    resultSet.getString("THIRDREQUESTNO"),
                    resultSet.getString("MONTHLYINCOME"),
                    resultSet.getString("QUOTASTATUS"),
                    resultSet.getString("LBSADDRESS"),
                    resultSet.getString("PROVINCEFLAG"),
                    resultSet.getString("BUSINESSINCOME"),
                    resultSet.getString("COMPREHENSIVERATE"),
                    resultSet.getString("IDCARDVALIDSTARTDATE"),
                    resultSet.getString("LOANCONTRACTNO"),
                    resultSet.getString("QUOTANO"),
                    resultSet.getString("PROVINCEADDRESS"),
                    resultSet.getString("GUARANTOR"),
                    resultSet.getString("GUARANTRATIO"),
                    resultSet.getString("SESSIONID"),
                    resultSet.getString("TRANSACTIONID"),
                    resultSet.getString("TRANSACTIONTYPE"),
                    resultSet.getString("BANKCARD"),
                    resultSet.getString("FRONTID"),
                    resultSet.getString("BACKID"),
                    resultSet.getString("FACEPHOTOID"),
                    resultSet.getString("PROTOCALINFO"),
                    resultSet.getString("LOCATION_REASON"),
                    resultSet.getString("CREDITINCRCODE"),
                    resultSet.getString("CREDITINCRFLAG"),
                    resultSet.getString("POLICYTRANSFLAG"),
                    resultSet.getString("BALANCECOMCOSE"),
                    resultSet.getString("ORGLENDPARRATE"),
                    resultSet.getString("ISNEEDAUXINFO"),
                    resultSet.getString("AUXINFOQUERYURL"),
                    resultSet.getString("AUXEVENTTYPE"),
                    resultSet.getString("CALLBACKURL"),
                    resultSet.getString("RATE"),
                    resultSet.getString("CREDITREJECTIONREASON"),
                    resultSet.getString("CREDITREQUESTTIME"),
                    resultSet.getString("REPAYMETHOD"),
                    resultSet.getString("RISKTYPE"),
                    resultSet.getString("DEDUCTIBLE"),
                    resultSet.getString("SUGGESTAMTEFFECTIVETIME"),
                    resultSet.getString("SUGGESTAMTEXPIRETIME"),
                    resultSet.getString("SUGGESTRATEMAX"),
                    resultSet.getString("SUGGESTRATEMIN"),
                    resultSet.getString("SUGGESTTMPAMT"),
                    resultSet.getString("TMPAMTEFFECTIVETIME"),
                    resultSet.getString("TMPAMTEXPIRETIME"),
                    resultSet.getString("DAILYRATE"),
                    resultSet.getString("VERSIONFLAG"),
                    resultSet.getString("PCR_AUTH"),
                    resultSet.getString("ACTUALFUNDER"),
                    resultSet.getString("GUARANTAMT"),
                    resultSet.getString("ENABLECREDITAMT"),
                    resultSet.getString("ENABLEAMTEFFECTIVETIME"),
                    resultSet.getString("APPLYTYPE"),
                    resultSet.getString("CREDITTAG"),
                    resultSet.getString("ISNEWINDUSTRY"),
                    resultSet.getString("NEWINDUSTRYCAST"),
                    resultSet.getString("ADSTIME")
            );
        } catch (Exception e) {
            throw new CustomizedException(e);
        }
    }

    @Override
    public CustomerInfo getCustomerInfoEntity(String customerId, String dbConnectionInfoId) {
        try {
            DbConnectionInfo dbConnectionInfo = new DbConnectionInfo();
            dbConnectionInfo.setId(dbConnectionInfoId);
            dbConnectionInfo = dbConnectionInfoMapper.selectByCondition(dbConnectionInfo).get(0);
            Connection connection = DbTool.getDbConnection(dbConnectionInfo.getDbType(), dbConnectionInfo.getUrl(), dbConnectionInfo.getUsername(), dbConnectionInfo.getPassword());

            PreparedStatement preparedStatement = connection.prepareStatement(CUSTOMER_INFO_QUERY_SQL);
            preparedStatement.setString(1, customerId);
            ResultSet resultSet = preparedStatement.executeQuery();
            if (!resultSet.next()) {
                throw new CustomizedException(StrUtil.format("customerId: 【{}】未查询到记录", customerId));
            }
            CustomerInfo customerInfo = new CustomerInfo();
            customerInfo.setCertid(resultSet.getString("CERTID"));
            return customerInfo;
        } catch (Exception e) {
            throw new CustomizedException(e);
        }
    }

    @Override
    public void createDestFolder(String templateDir, String destFolderName) {
        //创建目标文件夹
        File destFolder = FileUtil.mkdir(loanFileGenerateWorkDir + File.separator + destFolderName);
        //将模板文件复制到目标文件夹中
        String templatePath = loanFileGenerateWorkDir + File.separator + templateDir;
        for (File file : FileUtil.ls(templatePath)) {
            FileUtil.copy(file, destFolder, true);
        }
    }

    @Override
    public void writeCsv(CommonReqtListDto<Map<String, String>> commonReqtListDto) {
        String destFolderName = commonReqtListDto.getParam1();
        String csvName = commonReqtListDto.getParam2();
        String csvPath = loanFileGenerateWorkDir + File.separator + destFolderName + File.separator + csvName;
        // 获取数据列表
        List<Map<String, String>> dataList = commonReqtListDto.getListParam1();

        // 创建 CSV 写入器
        CsvWriter writer = CsvUtil.getWriter(csvPath, StandardCharsets.UTF_8);

        // 获取表头（假设所有 Map 的 key 相同）
        if (!dataList.isEmpty()) {
            Map<String, String> firstRow = dataList.get(0);
            String[] headers = firstRow.keySet().toArray(new String[0]);

            // 写入表头
            writer.write(headers);

            // 写入数据
            for (Map<String, String> row : dataList) {
                String[] values = row.values().toArray(new String[0]);
                writer.write(values);
            }
        }
        writer.close();
    }

    /**
     * 下载生成的文件（将目标文件夹压缩为ZIP并返回输入流）
     * 
     * @param destFolderName 目标文件夹名称
     * @return 包含ZIP文件内容的输入流
     */
    @Override
    public InputStream download(String destFolderName) {
        String destFolderPath = loanFileGenerateWorkDir + File.separator + destFolderName;
        File zipFile = ZipUtil.zip(destFolderPath);
        FileUtil.del(destFolderPath);
        return FileUtil.getInputStream(zipFile);
    }

}
