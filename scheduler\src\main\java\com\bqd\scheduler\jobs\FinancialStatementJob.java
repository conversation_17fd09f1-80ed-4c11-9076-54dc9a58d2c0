package com.bqd.scheduler.jobs;

import com.bqd.base.response.Response;
import com.bqd.scheduler.service.ServiceIntegratedTesterService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
@Component
@Slf4j
public class FinancialStatementJob implements Job {

    @Autowired
    private ServiceIntegratedTesterService serviceIntegratedTesterService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("【定时任务-开始】清除协同办公报表缓存");
        Response response = serviceIntegratedTesterService.clearUploadCache();
        log.info("【定时任务-完成】清除协同办公报表缓存，返回：{}", response);
    }
}
