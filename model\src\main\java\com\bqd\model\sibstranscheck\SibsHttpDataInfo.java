package com.bqd.model.sibstranscheck;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-07-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SibsHttpDataInfo {
    private String id;
    private String contentType;
    private String httpType;
    private String seqNo;
    private String transTimestamp;
    private String interfaceId;
}
