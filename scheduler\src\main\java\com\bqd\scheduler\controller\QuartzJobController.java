package com.bqd.scheduler.controller;

import com.bqd.base.response.Response;
import com.bqd.model.scheduler.TriggerInfoDto;
import com.bqd.scheduler.service.QuartzJobService;
import org.quartz.TriggerKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
@RestController
@RequestMapping("/jobs")
public class QuartzJobController {

    @Autowired
    private QuartzJobService quartzJobService;

    @PostMapping("/list")
    public Response getJobsByCondition(@RequestBody TriggerInfoDto reqtTriggerInfoDto) {
        return Response.success(quartzJobService.getJobsByCondition(reqtTriggerInfoDto));
    }

    @PostMapping("/update")
    public Response updateTrigger(@RequestBody TriggerInfoDto triggerInfoDto) {
        quartzJobService.updateTrigger(triggerInfoDto);
        return Response.success();
    }

    @GetMapping("/status")
    public Response updateTriggerStatus(@RequestParam("triggerKey") String triggerKeyStr, @RequestParam("status") String status) {
        String[] split = triggerKeyStr.split("\\.");
        TriggerKey triggerKey = new TriggerKey(split[1], split[0]);
        quartzJobService.updateTriggerStatus(triggerKey, status);
        return Response.success();
    }

}
