package com.bqd.integratedtester.transactionchain.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.bqd.base.rpc.common.OracleAlphaUtilMapper;
import com.bqd.base.rpc.esbdata.EsbDictionaryMapper;
import com.bqd.base.rpc.transactionchain.*;
import com.bqd.integratedtester.transactionchain.service.WisdomService;
import com.bqd.integratedtester.transactionchain.tools.TransactionChainTools;
import com.bqd.model.esbdata.EsbDictionary;
import com.bqd.model.transactionchain.*;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-02
 */
@Service
@Slf4j
public class WisdomServiceImpl implements WisdomService {

    @Value("${path.transaction-chain.h5-wisdom-nets}")
    private String H5_WISDOM_NETS_PATH;
    @Value("${path.transaction-chain.wisdom-integrated-service}")
    private String WISDOM_INTEGRATED_SERVICE_PATH;

    @Autowired
    private OracleAlphaUtilMapper oracleAlphaUtilMapper;
    @Autowired
    private WisdomTradeNameMapper wisdomTradeNameMapper;
    @Autowired
    private WisdomOperationTypeMapper wisdomOperationTypeMapper;
    @Autowired
    private WisdomMvcMapper wisdomMvcMapper;
    @Autowired
    private WisdomBlMapper wisdomBlMapper;
    @Autowired
    private ZtServiceDubbovMapper ztServiceDubbovMapper;
    @Autowired
    private ZtServiceBlMapper ztServiceBlMapper;
    @Autowired
    private EsbDictionaryMapper esbDictionaryMapper;

    /**
     * 解析前端-前置
     */
    @Override
    public void parseFront() {
        oracleAlphaUtilMapper.truncateTable("WISDOM_TRADE_NAME");
        oracleAlphaUtilMapper.truncateTable("WISDOM_OPERATION_TYPE");
        List<File> indexFolderList = new ArrayList<>();
        for (File pagesFolder : FileUtil.ls(H5_WISDOM_NETS_PATH)) {
            getIndexPageFolder(pagesFolder, indexFolderList);
        }
        for (File indexFolder : indexFolderList) {
            String tradeNameId = UUID.randomUUID().toString();
            String tradeName = getTradeName(new File(indexFolder.getAbsolutePath() + File.separator + "index.html"));
            List<File> vueFileList = new ArrayList<>();
            getVueFileList(indexFolder, vueFileList);
            for (File vueFile : vueFileList) {
                String scriptString = StrUtil.subBetween(FileUtil.readUtf8String(vueFile), "<script>", "</script>");
                if (StrUtil.isBlank(scriptString)) {
                    continue;
                }
                List<String> scriptLineList = StrUtil.split(scriptString, System.lineSeparator());
                scriptLineList.stream().filter(StrUtil::isNotBlank).filter(line -> line.contains("operationType")).filter(line -> !line.trim().startsWith("//")).forEach(line -> {
                    line = StrUtil.replace(line, "\"", "'");
                    Pattern compile = Pattern.compile("'com\\.[^\\\"]*'");
                    Matcher matcher = compile.matcher(line);
                    while (matcher.find()) {
                        wisdomOperationTypeMapper.insert(new WisdomOperationType(UUID.randomUUID().toString(), tradeNameId, matcher.group().replaceAll("'", ""), vueFile.getName().replace(".vue", "")));
                    }
                });
            }
            wisdomTradeNameMapper.insert(new WisdomTradeName(tradeNameId, tradeName, indexFolder.getAbsolutePath().substring(H5_WISDOM_NETS_PATH.length() + 1)));
        }
    }

    /**
     * 获取index页面文件夹
     *
     * @param file
     * @param fileList
     */
    private void getIndexPageFolder(File file, List<File> fileList) {
        if (!FileUtil.isDirectory(file)) {
            return;
        }
        if (FileUtil.isDirectory(file) && Arrays.stream(file.listFiles()).anyMatch(f -> f.getName().equals("index.html"))) {
            fileList.add(file);
            return;
        }
        for (File f : file.listFiles()) {
            getIndexPageFolder(f, fileList);
        }
    }

    /**
     * 获取交易名
     *
     * @param file
     * @return
     */
    private String getTradeName(File file) {
        try {
            return Jsoup.parse(file).getElementsByTag("head").get(0).getElementsByTag("title").get(0).text();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取vue文件路径列表
     *
     * @param file
     * @param vueFileList
     */
    private void getVueFileList(File file, List<File> vueFileList) {
        if (!FileUtil.isDirectory(file) && file.getName().contains(".vue")) {
            vueFileList.add(file);
            return;
        }
        File[] files = file.listFiles();
        if (ObjectUtil.isNull(files)) {
            return;
        }
        for (File f : files) {
            getVueFileList(f, vueFileList);
        }
    }

    /**
     * 解析前置-中台
     */
    @Override
    public void parseIfpIntegratedService() {
        oracleAlphaUtilMapper.truncateTable("WISDOM_MVC");
        parseIfpIntegratedServiceXml();
        oracleAlphaUtilMapper.truncateTable("WISDOM_BL");
        parseIfpIntegratedServiceBl();
    }

    /**
     * 解析前置xml文件
     */
    private void parseIfpIntegratedServiceXml() {
        String MVC_XML_FILEPATH = WISDOM_INTEGRATED_SERVICE_PATH + File.separator + "WebContent" + File.separator + "WEB-INF" + File.separator + "conf" + File.separator + "al" + File.separator + "mvc";
        for (File mvcXmlFile : FileUtil.ls(MVC_XML_FILEPATH)) {
            try {
                Element rootElement = new SAXReader().read(mvcXmlFile).getRootElement();
                List<Element> mvcActionList = rootElement.elements("mvcAction");
                for (Element mvcAction : mvcActionList) {
                    String mvcId = mvcAction.attributeValue("id");
                    String bid = mvcAction.attributeValue("bid");
                    String description = getDescription(rootElement.asXML(), mvcId);
                    WisdomMvc wisdomMvc = new WisdomMvc(mvcId, bid, description, mvcXmlFile.getName());
                    wisdomMvcMapper.insert(wisdomMvc);
                }
            } catch (Exception e) {
                log.error("【交易链路查询-智慧网点-解析前置xml文件出错：】", e);
            }
        }
    }

    /**
     * 获取注释的描述
     *
     * @param xmlContent
     * @param mvcId
     * @return
     */
    private String getDescription(String xmlContent, String mvcId) {
        try {
            xmlContent = xmlContent.substring(0, xmlContent.indexOf("id=\"" + mvcId + "\""));
            xmlContent = xmlContent.substring(0, xmlContent.lastIndexOf("cl:mvcAction"));
            int desStart = xmlContent.lastIndexOf("<!--");
            int desEnd = xmlContent.lastIndexOf("-->");
            if (xmlContent.substring(desStart).contains("cl:mvcAction"))
                return "";
            return xmlContent.substring(desStart + 4, desEnd).trim();
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 解析前置bl文件
     */
    private void parseIfpIntegratedServiceBl() {
        String BL_FILEPATH = WISDOM_INTEGRATED_SERVICE_PATH + File.separator + "designSource" + File.separator + "bl";
        HashMap<String, String> blFileInfoMap = new HashMap<>();
        TransactionChainTools.getBlFileInfo(new File(BL_FILEPATH), "", blFileInfoMap);
        for (Map.Entry<String, String> entry : blFileInfoMap.entrySet()) {
            try {
                Element rootElement = new SAXReader().read(entry.getValue()).getRootElement();
                List<Element> dubboFlowActionList = rootElement.element("flow").elements("action").stream().filter(action -> action.attributeValue("type").contains("dubboFlowAction")).collect(Collectors.toList());
                for (Element dubboFlowAction : dubboFlowActionList) {
                    List<Element> propertyList = dubboFlowAction.element("conf").elements("property");
                    WisdomBl wisdomBl = new WisdomBl(entry.getKey(), "", "");
                    for (Element property : propertyList) {
                        if (property.attributeValue("name").equals("actionDesc")) {
                            wisdomBl.setActionDesc(property.attributeValue("value"));
                        }
                        if (property.attributeValue("name").equals("logicId")) {
                            wisdomBl.setLogicId(property.attributeValue("value"));
                        }
                    }
                    wisdomBlMapper.insert(wisdomBl);
                }
            } catch (Exception e) {
                log.error("【交易链路查询-智慧网点-解析前置bl文件出错：】", e);
            }
        }
    }

    /**
     * 根据交易名搜索
     */
    @Override
    public List<WisdomTradeNameSearchDto> searchTradeName(String tradeName) {
        //根据交易名称模糊查询
        List<WisdomTradeName> wisdomTradeNameList = wisdomTradeNameMapper.selectByLike(tradeName);
        List<WisdomTradeNameSearchDto> tradeNameSearchDtoList = new ArrayList<>();
        //遍历查出来的交易名称，查询其调用的前置
        for (WisdomTradeName wisdomTradeName : wisdomTradeNameList) {
            WisdomTradeNameSearchDto tradeNameSearchDto = new WisdomTradeNameSearchDto();
            tradeNameSearchDto.setTradeName(wisdomTradeName.getTradeName());
            tradeNameSearchDto.setFilePath(wisdomTradeName.getFilePath());
            //根据交易名称查询调用的前置
            List<WisdomOperationType> wisdomOperationTypeList = wisdomOperationTypeMapper.selectByTradeNameId(wisdomTradeName.getId());
            //定义前置服务信息列表
            List<WisdomTradeNameSearchDto.IntegrateServiceInfo> integrateServiceInfoList = new ArrayList<>();
            //遍历前置信息列表，根据operationType=mvcId条件，查询前置相关信息
            for (WisdomOperationType wisdomOperationType : wisdomOperationTypeList) {
                String mvcId = wisdomOperationType.getOperationType().replace("com.", "");
                WisdomMvc wisdomMvc = wisdomMvcMapper.selectByMvcId(mvcId);
                if (ObjectUtil.isNull(wisdomMvc)) {
                    continue;
                }
                //查询出的前置信息添加到前置服务信息列表
                integrateServiceInfoList.add(new WisdomTradeNameSearchDto.IntegrateServiceInfo(wisdomMvc.getMvcId(), wisdomMvc.getDescription(), wisdomMvc.getBId(), wisdomOperationType.getCallFrom(), null));
            }
            //前置信息列表添加到交易名对象中
            tradeNameSearchDto.setIntegrateServiceInfoList(integrateServiceInfoList);
            //遍历前置信息，根据BID查询调用中台的logicId
            for (WisdomTradeNameSearchDto.IntegrateServiceInfo integrateServiceInfo : integrateServiceInfoList) {
                //定义中台信息列表
                List<WisdomTradeNameSearchDto.ServiceInfo> serviceInfoList = new ArrayList<>();
                //根据前置BID查询调用的中台的logicId
                List<String> logicIdList = wisdomBlMapper.selectByBId(integrateServiceInfo.getBId()).stream().map(WisdomBl::getLogicId).collect(Collectors.toList());
                //遍历调用的中台的logicId
                for (String logicId : logicIdList) {
                    //根据logicId=serviceGroup进行中台信息查询
                    String serviceGroup = convertLogicIdToServiceGroup(logicId);
                    ZtServiceDubbov ztServiceDubbov = searchServiceGroup(serviceGroup);
                    if (ObjectUtil.isNull(ztServiceDubbov)) continue;
                    //查询出的中台信息添加到中台服务信息列表
                    serviceInfoList.add(new WisdomTradeNameSearchDto.ServiceInfo(ztServiceDubbov.getServiceName(), ztServiceDubbov.getServiceGroup(), ztServiceDubbov.getDescription(), ztServiceDubbov.getBeanRef(), null));
                }
                //中台信息列表添加到前置对象中
                integrateServiceInfo.setServiceInfoList(serviceInfoList);
                //遍历中台信息，根据serviceGroup查询esb信息
                for (WisdomTradeNameSearchDto.ServiceInfo serviceInfo : integrateServiceInfo.getServiceInfoList()) {
                    //定义ESB信息列表
                    List<WisdomTradeNameSearchDto.ESBInfo> esbInfoList = new ArrayList<>();
                    //查询中台调用的ESB信息
                    List<ZtServiceBl> ztServiceBlList = ztServiceBlMapper.selectByBeanRefAndServiceName(serviceInfo.getBeanRef(), serviceInfo.getServiceName());
                    //遍历中台调用的ESB信息，从ESB字典表中获取详细信息
                    for (ZtServiceBl ztServiceBl : ztServiceBlList) {
                        if (StrUtil.isBlank(ztServiceBl.getTrxCode())) {
                            continue;
                        }
                        EsbDictionary esbDictionary = esbDictionaryMapper.selectByInterfaceId(convertTrxCodeToEsbInterfaceId(ztServiceBl.getTrxCode()));
                        if (ObjectUtil.isNull(esbDictionary)) {
                            continue;
                        }
                        //添加到ESB信息列表
                        esbInfoList.add(new WisdomTradeNameSearchDto.ESBInfo(esbDictionary.getInterfaceId(), esbDictionary.getInterfaceName()));
                    }
                    //ESB信息列表添加到中台对象中
                    serviceInfo.setEsbInfoList(esbInfoList);
                }
            }
            //添加到交易名列表
            tradeNameSearchDtoList.add(tradeNameSearchDto);
        }
        return tradeNameSearchDtoList;
    }

    private ZtServiceDubbov searchServiceGroup(String serviceGroup){
        //先精准查询
        ZtServiceDubbov ztServiceDubbov = ztServiceDubbovMapper.selectByServiceGroup(serviceGroup);
        //若为空，则模糊查询
        if (ObjectUtil.isNull(ztServiceDubbov)){
            List<ZtServiceDubbov> ztServiceDubbovList = ztServiceDubbovMapper.selectByLikeServiceGroup(serviceGroup);
            //若无结果或结果大于1，则跳过
            if (ztServiceDubbovList.size() != 1){
                return null;
            }
            ztServiceDubbov = ztServiceDubbovList.get(0);
        }
        return ztServiceDubbov;
    }

    private String convertLogicIdToServiceGroup(String logicId) {
        final List<String> groupStartStrList = Arrays.asList("SC", "IC", "UC", "TAC", "AC", "CC", "PC", "MC", "TC", "BC", "TRA", "MT", "BAS`", "M0");
        for (String groupStartStr : groupStartStrList) {
            if (logicId.contains(groupStartStr)) {
                return logicId.substring(logicId.indexOf(groupStartStr));
            }
        }
        return logicId;
    }

    public static String convertTrxCodeToEsbInterfaceId(String trxCode) {
        if (trxCode.contains("-")) {
            trxCode = trxCode.split("-")[1];
        }
        // 定义正则表达式匹配6位数字
        String regex = "\\d{6}";

        // 编译正则表达式为Pattern对象
        Pattern pattern = Pattern.compile(regex);

        // 创建一个Matcher对象用于执行查找操作
        Matcher matcher = pattern.matcher(trxCode);

        // 查找所有匹配项并打印结果
        while (matcher.find()) {
            return matcher.group();
        }
        return trxCode;
    }

    @Override
    public List<WisdomIntegrateServiceSearchDto> searchIntegrateService(String mvcId, String description) {
        //定义IntegrateServiceSearchDto列表
        List<WisdomIntegrateServiceSearchDto> integrateServiceSearchDtoList = new ArrayList<>();
        //根据mvcId或description模糊查询前置信息
        List<WisdomMvc> wisdomMvcList;
        if (StrUtil.isNotBlank(mvcId)) {
            wisdomMvcList = wisdomMvcMapper.selectByLikeMvcId(mvcId);
        } else {
            wisdomMvcList = wisdomMvcMapper.selectByLikeDescription(description);
        }
        //遍历前置信息
        for (WisdomMvc wisdomMvc : wisdomMvcList) {
            //根据mvcId=operationType查询调用该前置的前端
            List<WisdomOperationType> wisdomOperationTypeList = wisdomOperationTypeMapper.selectByLikeOperationType(wisdomMvc.getMvcId());
            //定义交易名列表
            List<WisdomIntegrateServiceSearchDto.TradeNameSearchDto> tradeNameSearchDtoList = new ArrayList<>();
            //遍历调用该前置的前端
            for (WisdomOperationType wisdomOperationType : wisdomOperationTypeList) {
                //根据tradeNameId查询交易名
                WisdomTradeName wisdomTradeName = wisdomTradeNameMapper.selectById(wisdomOperationType.getTradeNameId());
                tradeNameSearchDtoList.add(new WisdomIntegrateServiceSearchDto.TradeNameSearchDto(wisdomTradeName.getTradeName(), wisdomTradeName.getFilePath()));
            }
            //创建前置dto对象
            WisdomIntegrateServiceSearchDto integrateServiceSearchDto = new WisdomIntegrateServiceSearchDto(wisdomMvc.getMvcId(), wisdomMvc.getDescription(), tradeNameSearchDtoList, null);
            //将前置dto对象添加到列表
            integrateServiceSearchDtoList.add(integrateServiceSearchDto);
            //根据BID查询前置信息调用的所有中台服务
            List<WisdomBl> wisdomBlList = wisdomBlMapper.selectByBId(wisdomMvc.getBId());
            //定义中台服务信息列表
            List<WisdomIntegrateServiceSearchDto.ServiceInfo> serviceInfoList = new ArrayList<>();
            //遍历中台服务信息，根据logicId=serviceGroup查询所有中台信息
            for (WisdomBl wisdomBl : wisdomBlList) {
                //查询中台信息
                String serviceGroup = convertLogicIdToServiceGroup(wisdomBl.getLogicId());
                ZtServiceDubbov ztServiceDubbov = searchServiceGroup(serviceGroup);
                if (ObjectUtil.isNull(ztServiceDubbov)) continue;
                //中台信息添加到列表
                serviceInfoList.add(new WisdomIntegrateServiceSearchDto.ServiceInfo(ztServiceDubbov.getServiceName(), ztServiceDubbov.getServiceGroup(), ztServiceDubbov.getDescription(), ztServiceDubbov.getBeanRef(), null));
            }
            //将中台服务信息添加到dto对象中
            integrateServiceSearchDto.setServiceInfoList(serviceInfoList);
            //遍历中台服务信息列表，查询esb调用信息
            for (WisdomIntegrateServiceSearchDto.ServiceInfo serviceInfo : serviceInfoList) {
                //查询中台调用ESB
                List<ZtServiceBl> serviceBlList = ztServiceBlMapper.selectByBeanRefAndServiceName(serviceInfo.getBeanRef(), serviceInfo.getServiceName());
                //定义ESB信息列表
                List<WisdomIntegrateServiceSearchDto.ESBInfo> esbInfoList = new ArrayList<>();
                //遍历调用的ESB列表
                for (ZtServiceBl ztServiceBl : serviceBlList) {
                    if (StrUtil.isBlank(ztServiceBl.getTrxCode())) {
                        continue;
                    }
                    //查询ESB信息
                    EsbDictionary esbDictionary = esbDictionaryMapper.selectByInterfaceId(convertTrxCodeToEsbInterfaceId(ztServiceBl.getTrxCode()));
                    if (ObjectUtil.isNull(esbDictionary)) {
                        continue;
                    }
                    //添加到ESB信息列表
                    esbInfoList.add(new WisdomIntegrateServiceSearchDto.ESBInfo(esbDictionary.getInterfaceId(), esbDictionary.getInterfaceName()));
                }
                //ESB列表添加到中台对象中
                serviceInfo.setEsbInfoList(esbInfoList);
            }
        }
        return integrateServiceSearchDtoList;
    }

    @Override
    public List<WisdomServiceSearchDto> searchService(String serviceGroup, String description) {
        //创建serviceSearchDto列表
        List<WisdomServiceSearchDto> serviceSearchDtoList = new ArrayList<>();
        //从中台数据库模糊查询
        List<ZtServiceDubbov> serviceDubbovList;
        if (StrUtil.isNotBlank(serviceGroup)) {
            serviceDubbovList = ztServiceDubbovMapper.selectByLikeServiceGroup(serviceGroup);
        } else {
            serviceDubbovList = ztServiceDubbovMapper.selectByLikeDescription(description);
        }
        //遍历查询出的中台信息
        for (ZtServiceDubbov ztServiceDubbov : serviceDubbovList) {
            //创建serviceSearchDto对象
            WisdomServiceSearchDto serviceSearchDto = new WisdomServiceSearchDto(ztServiceDubbov.getServiceGroup(), ztServiceDubbov.getDescription(), null, null);
            //中台信息添加到serviceSearchDtoList列表中
            serviceSearchDtoList.add(serviceSearchDto);
            //根据serviceGroup=logicId查询调用该中台的前置
            List<WisdomBl> wisdomBlList = wisdomBlMapper.selectByLikeLogicId(ztServiceDubbov.getServiceGroup());
            //定义前置信息列表
            List<WisdomServiceSearchDto.IntegrateServiceInfo> integrateServiceInfoList = new ArrayList<>();
            //遍历调用该中台的前置，根据BID查询前置信息
            for (WisdomBl wisdomBl : wisdomBlList) {
                WisdomMvc wisdomMvc = wisdomMvcMapper.selectByBId(wisdomBl.getBId());
                if (ObjectUtil.isNull(wisdomMvc)) {
                    continue;
                }
                //添加到前置信息列表中
                integrateServiceInfoList.add(new WisdomServiceSearchDto.IntegrateServiceInfo(wisdomMvc.getMvcId(), wisdomMvc.getDescription(), null));
            }
            //将前置信息添加到serviceSearchDto对象中
            serviceSearchDto.setIntegrateServiceInfoList(integrateServiceInfoList);
            //遍历前置信息，查询调用该前置的交易名称
            for (WisdomServiceSearchDto.IntegrateServiceInfo integrateServiceInfo : integrateServiceInfoList) {
                //创建交易名称信息列表
                List<WisdomServiceSearchDto.TradeNameInfo> tradeNameInfoList = new ArrayList<>();
                //根据mvcId=operationType查询调用该前置的交易名
                List<WisdomOperationType> wisdomOperationTypeList = wisdomOperationTypeMapper.selectByLikeOperationType(integrateServiceInfo.getMvcId());
                //遍历调用该前置的交易名，获取交易名称信息
                for (WisdomOperationType wisdomOperationType : wisdomOperationTypeList) {
                    WisdomTradeName wisdomTradeName = wisdomTradeNameMapper.selectById(wisdomOperationType.getTradeNameId());
                    //交易名称信息添加到交易名称信息列表中
                    tradeNameInfoList.add(new WisdomServiceSearchDto.TradeNameInfo(wisdomTradeName.getTradeName(), wisdomTradeName.getFilePath()));
                }
                //将交易名称信息添加到serviceSearchDto.integrateServiceInfo对象中
                integrateServiceInfo.setTradeNameInfoList(tradeNameInfoList);
            }
            //查询当前中台调用的ESB
            List<ZtServiceBl> serviceBlList = ztServiceBlMapper.selectByBeanRefAndServiceName(ztServiceDubbov.getBeanRef(), ztServiceDubbov.getServiceName());
            //定义ESB信息列表
            List<WisdomServiceSearchDto.ESBInfo> esbInfoList = new ArrayList<>();
            //遍历该中台调用的ESB列表
            for (ZtServiceBl ztServiceBl : serviceBlList) {
                //根据trxCode查询ESB信息
                EsbDictionary esbDictionary = esbDictionaryMapper.selectByInterfaceId(convertTrxCodeToEsbInterfaceId(ztServiceBl.getTrxCode()));
                //添加到ESB信息列表中
                esbInfoList.add(new WisdomServiceSearchDto.ESBInfo(esbDictionary.getInterfaceId(), esbDictionary.getInterfaceName()));
            }
            //将ESB信息添加到serviceSearchDto对象中
            serviceSearchDto.setEsbInfoList(esbInfoList);
        }
        return serviceSearchDtoList;
    }

    @Override
    public List<WisdomEsbSearchDto> searchEsb(String interfaceId) {
        //创建ESB信息列表
        List<WisdomEsbSearchDto> esbSearchDtoList = new ArrayList<>();
        //根据接口id查询ESB
        List<EsbDictionary> esbDictionaryList = esbDictionaryMapper.selectByLikeInterfaceId(interfaceId);
        //遍历查出来的ESB列表
        for (EsbDictionary esbDictionary : esbDictionaryList) {
            //ESB信息添加到ESB信息列表中
            esbSearchDtoList.add(new WisdomEsbSearchDto(esbDictionary.getInterfaceId(), esbDictionary.getInterfaceName(), null));
        }
        //遍历ESB信息列表，查询调用该ESB的中台
        for (WisdomEsbSearchDto esbSearchDto : esbSearchDtoList) {
            List<ZtServiceBl> ztServiceBlList = ztServiceBlMapper.selectByLikeTrxCode(esbSearchDto.getInterfaceId());
            //定义中台信息列表
            List<WisdomEsbSearchDto.ServiceInfo> serviceInfoList = new ArrayList<>();
            //遍历调用该ESB的中台列表
            for (ZtServiceBl ztServiceBl : ztServiceBlList) {
                //根据BeanRef查询中台信息
                ZtServiceDubbov ztServiceDubbov = ztServiceDubbovMapper.selectByBeanRefAndServiceName(ztServiceBl.getBeanRef(), ztServiceBl.getServiceName());
                if (ObjectUtil.isNull(ztServiceDubbov)){
                    continue;
                }
                //添加到中台信息列表中
                serviceInfoList.add(new WisdomEsbSearchDto.ServiceInfo(ztServiceDubbov.getServiceGroup(), ztServiceDubbov.getDescription(), null));
            }
            //将中台信息添加到esbSearchDto对象中
            esbSearchDto.setServiceInfoList(serviceInfoList);
            //遍历中台信息列表，根据serviceGroup=logicId查询调用该中台的前置
            for (WisdomEsbSearchDto.ServiceInfo serviceInfo : serviceInfoList){
                List<WisdomBl> wisdomBlList = wisdomBlMapper.selectByLikeLogicId(serviceInfo.getServiceGroup());
                //定义前置信息列表
                List<WisdomEsbSearchDto.IntegrateServiceInfo> integrateServiceInfoList = new ArrayList<>();
                //遍历调用该中台的前置
                for (WisdomBl wisdomBl : wisdomBlList) {
                    //根据BID查询前置信息
                    WisdomMvc wisdomMvc = wisdomMvcMapper.selectByBId(wisdomBl.getBId());
                    //添加到前置信息列表
                    integrateServiceInfoList.add(new WisdomEsbSearchDto.IntegrateServiceInfo(wisdomMvc.getMvcId(), wisdomMvc.getDescription(), wisdomMvc.getBId(), null));
                }
                //将前置信息添加到serviceInfo对象中
                serviceInfo.setIntegrateServiceInfoList(integrateServiceInfoList);
                //遍历前置信息，根据mvcId=operationType查询调用该前置的交易名称
                for (WisdomEsbSearchDto.IntegrateServiceInfo integrateServiceInfo: integrateServiceInfoList){
                    List<WisdomOperationType> wisdomOperationTypeList = wisdomOperationTypeMapper.selectByLikeOperationType(integrateServiceInfo.getMvcId());
                    //定义交易名称信息列表
                    List<WisdomEsbSearchDto.TradeNameInfo> tradeNameInfoList = new ArrayList<>();
                    //遍历调用该前置的交易名称
                    for (WisdomOperationType wisdomOperationType : wisdomOperationTypeList) {
                        //根据tradeNameId查询交易名称信息
                        WisdomTradeName wisdomTradeName = wisdomTradeNameMapper.selectById(wisdomOperationType.getTradeNameId());
                        //添加到交易名称信息列表中
                        tradeNameInfoList.add(new WisdomEsbSearchDto.TradeNameInfo(wisdomTradeName.getTradeName(), wisdomTradeName.getFilePath(), wisdomOperationType.getCallFrom()));
                    }
                    //将交易名称信息添加到integrateServiceInfo对象中
                    integrateServiceInfo.setTradeNameInfoList(tradeNameInfoList);
                }
            }
        }
        return esbSearchDtoList;
    }
}
