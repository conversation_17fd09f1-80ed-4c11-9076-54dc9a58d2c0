package com.bqd.serviceenr.controller;

import cn.hutool.core.util.StrUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.model.esbnetworkreplay.EsbDeduplicateIgnoreDto;
import com.bqd.base.response.Response;
import com.bqd.serviceenr.service.DeduplicateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: Esb流量回放-报文去重功能
 * @Author: wangzirui
 * @CreateTime: 2024-11-25
 */
@RestController
@RequestMapping("/deduplicate")
public class DeduplicateController {

    @Autowired
    private DeduplicateService deduplicateService;

    /**
     * 根据接口id获取忽略的字段信息
     * @param interfaceId
     * @return
     */
    @GetMapping("/fetchIgnoredField")
    public Response fetchIgnoredField(@RequestParam("interfaceId") String interfaceId) {
        return Response.success(deduplicateService.fetchIgnoredField(interfaceId));
    }

    /**
     * 更新忽略的字段信息
     * @param esbDeduplicateIgnoreDto
     * @return
     */
    @PostMapping("/updateIgnoredField")
    public Response updateIgnoredField(@RequestBody EsbDeduplicateIgnoreDto esbDeduplicateIgnoreDto) {
        deduplicateService.updateIgnoredField(esbDeduplicateIgnoreDto);
        return Response.success();
    }

    /**
     * 获取全局忽略的字段信息
     * @return
     */
    @GetMapping("/global/fetchIgnoredField")
    public Response fetchIgnoredFieldGlobal() {
        return Response.success(deduplicateService.fetchIgnoredFieldGlobal());
    }

    /**
     * 添加全局忽略的字段信息
     * @param ignoredField
     * @return
     */
    @GetMapping("/global/addIgnoredField")
    public Response addIgnoredFieldGlobal(@RequestParam("ignoredField") String ignoredField) {
        if (StrUtil.isBlank(ignoredField)) {
            throw new CustomizedException("忽略字段为空");
        }
        deduplicateService.addIgnoredFieldGlobal(ignoredField);
        return Response.success();
    }

    /**
     * 删除全局忽略的字段信息
     * @param ignoredField
     * @return
     */
    @GetMapping("/global/deleteIgnoredField")
    public Response deleteIgnoredFieldGlobal(@RequestParam("ignoredField") String ignoredField) {
        if (StrUtil.isBlank(ignoredField)) {
            throw new CustomizedException("忽略字段为空");
        }
        deduplicateService.deleteIgnoredFieldGlobal(ignoredField);
        return Response.success();
    }

    /**
     * 针对指定的接口ID的报文数据去重
     * @param interfaceId
     * @return
     */
    @GetMapping("/deduplicate/{interfaceId}")
    public Response deduplicate(@PathVariable String interfaceId) {
        deduplicateService.deduplicate(interfaceId);
        return Response.success();
    }

    /**
     * 指定接口ID的报文资产内部去重
     * @param interfaceId
     * @return
     */
    @GetMapping("/internalDeduplicate/{interfaceId}")
    public Response internalDeduplicate(@PathVariable String interfaceId) {
        deduplicateService.internalDeduplicate(interfaceId);
        return Response.success();
    }

}
