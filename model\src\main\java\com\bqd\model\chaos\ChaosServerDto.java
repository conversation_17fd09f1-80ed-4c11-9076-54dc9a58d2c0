package com.bqd.model.chaos;

import com.bqd.model.servermanagement.ServerInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChaosServerDto extends ServerInfo {
    private String serverInfoId;
    private Integer agentStatus;
}
