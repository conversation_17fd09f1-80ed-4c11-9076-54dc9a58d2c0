package com.bqd.serviceesbdata.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.bqd.base.rpc.esbdata.EsbContentMapper;
import com.bqd.base.rpc.esbdata.EsbFieldEnumMapper;
import com.bqd.base.rpc.esbdata.EsbInfoMapper;
import com.bqd.base.rpc.esbnetworkreplay.EsbCompareIgnoreMapper;
import com.bqd.base.tools.XmlTool;
import com.bqd.model.common.PageDto;
import com.bqd.model.esbdata.*;
import com.bqd.model.esbdata.xmlcompare.CompareDto;
import com.bqd.model.esbnetworkreplay.EsbCompareIgnore;
import com.bqd.base.tools.EsbTool;
import com.bqd.serviceesbdata.service.EsbDbChangeService;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-25
 */
@Service
@Slf4j
public class EsbDbChangeServiceImpl implements EsbDbChangeService {

    @Autowired
    private EsbInfoMapper esbInfoMapper;
    @Autowired
    private EsbContentMapper esbContentMapper;
    @Autowired
    private EsbFieldEnumMapper esbFieldEnumMapper;
    @Autowired
    private EsbCompareIgnoreMapper esbCompareIgnoreMapper;

    @Override
    public void insertToDbByIntfId(String interfaceId, String startTime, String endTime) {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            Class.forName("com.ibm.db2.jcc.DB2Driver");
            connection = DriverManager.getConnection("***********************************", "db2inst2", "123456");
            //String sql = "SELECT * FROM (SELECT * FROM SVCGOV.T_SVC_TRACE_1 UNION ALL SELECT * FROM SVCGOV.T_SVC_TRACE_2) WHERE INTF_ID = ? AND EVENT_TYPE_NM = '接收HTTP请求' AND EVENT_TIME >= ? AND EVENT_TIME <= ? ORDER BY EVENT_TIME";
            //preparedStatement = connection.prepareStatement(sql);
            //preparedStatement.setString(1, interfaceId);
            //preparedStatement.setString(2, startTime);
            //preparedStatement.setString(3, endTime);
            String sql = "SELECT * FROM (SELECT * FROM SVCGOV.T_SVC_TRACE_1 UNION ALL SELECT * FROM SVCGOV.T_SVC_TRACE_2) WHERE INTF_ID = ? AND EVENT_TYPE_NM = '接收HTTP请求'";
            preparedStatement = connection.prepareStatement(sql);
            preparedStatement.setString(1, interfaceId);
            resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                String svcCorrId = resultSet.getString("SVC_CORR_ID");
                String eventTime = resultSet.getString("EVENT_TIME");
                String svcMsgChar = resultSet.getString("SVC_MSG_CHAR");
                String desc = resultSet.getString("DESC");
                String intfId = resultSet.getString("INTF_ID");
                String eventTypeNm = resultSet.getString("EVENT_TYPE_NM");
                String infoId = IdUtil.fastSimpleUUID();
                String url = EsbTool.extractUrlFromDesc(desc);
                EsbInfo esbInfo = new EsbInfo(infoId, intfId, svcCorrId, eventTypeNm, eventTime, url);
                EsbContent esbContent = new EsbContent(infoId, EsbTool.extractRequestBody(svcMsgChar), null);
                esbInfoMapper.insert(esbInfo);
                esbContentMapper.insert(esbContent);
            }
            resultSet.close();
            preparedStatement.close();
            connection.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void deduplicate(String interfaceId, String fieldPath) {
        List<EsbContent> esbContentList = esbContentMapper.selectByInterfaceId(interfaceId);
        Map<String, EsbFieldEnum> map = new HashMap<>();
        for (EsbContent esbContent: esbContentList) {
            EsbFieldEnum esbFieldEnum = new EsbFieldEnum(IdUtil.fastSimpleUUID(), interfaceId, esbContent.getInfoId(), fieldPath, null, esbContent.getRequestBody());
            try {
                if (StrUtil.isBlank(fieldPath)) {
                    map.put("", esbFieldEnum);
                    break;
                }
                Document document = DocumentHelper.parseText(esbContent.getRequestBody());
                Node node = document.selectSingleNode(fieldPath.replace(".", "/"));
                if (node == null) {
                    log.info("节点不存在, infoId: {}, interfaceId: {}, 节点: {}", esbContent.getInfoId(), interfaceId, fieldPath);
                    continue;
                }
                String enumValue = node.getText();
                esbFieldEnum.setEnumValue(enumValue);
                map.put(enumValue, esbFieldEnum);
            } catch (DocumentException e) {
                log.error("解析xml失败, infoId:{}", esbContent.getInfoId());
            }
        }
        for (EsbFieldEnum esbFieldEnum: map.values()) {
            esbFieldEnumMapper.insert(esbFieldEnum);
        }
    }

    @Override
    public int countInfoByInterfaceId(String interfaceId) {
        return esbInfoMapper.countByInterfaceId(interfaceId);
    }

    @Override
    public void deleteByInterfaceId(String interfaceId) {
        List<EsbInfo> esbInfoList = esbInfoMapper.selectByInterfaceId(interfaceId);
        for (EsbInfo esbInfo: esbInfoList) {
            esbInfoMapper.deleteById(esbInfo.getId());
            esbContentMapper.deleteByInfoId(esbInfo.getId());
        }
    }

    @Override
    public List<String> getFieldByInterfaceId(String interfaceId) {
        return esbFieldEnumMapper.selectFieldByInterfaceId(interfaceId);
    }

    @Override
    public PageDto<EsbFieldEnum> pagedByCondition(EsbFieldEnumDto esbFieldEnumDto) {
        EsbFieldEnum esbFieldEnum = BeanUtil.copyProperties(esbFieldEnumDto, EsbFieldEnum.class);
        int totalCount = esbFieldEnumMapper.countByCondition(esbFieldEnum);
        int start = PageUtil.getStart(esbFieldEnumDto.getPageNo(), esbFieldEnumDto.getPageSize()) + 1;
        int end = PageUtil.getEnd(esbFieldEnumDto.getPageNo(), esbFieldEnumDto.getPageSize());
        int totalPage = PageUtil.totalPage(totalCount, esbFieldEnumDto.getPageSize());
        esbFieldEnumDto.setStartRow(start);
        esbFieldEnumDto.setEndRow(end);
        List<EsbFieldEnum> esbFieldEnumList = esbFieldEnumMapper.pagedByCondition(esbFieldEnumDto);
        return new PageDto<>(esbFieldEnumDto.getPageNo(), esbFieldEnumDto.getPageSize(), totalPage, totalCount, esbFieldEnumList);
    }

    @Override
    public void deleteByCondition(EsbFieldEnum esbFieldEnum) {
        esbFieldEnumMapper.deleteByCondition(esbFieldEnum);
    }

    @Override
    public EsbDbChangeRespDto sendAndCompare(EsbDbChangeReqtDto esbDbChangeReqtDto) {
        String post1 = HttpUtil.post(esbDbChangeReqtDto.getUrl1(), esbDbChangeReqtDto.getContent1());
        String post2 = HttpUtil.post(esbDbChangeReqtDto.getUrl2(), esbDbChangeReqtDto.getContent2());
        EsbCompareIgnore esbCompareIgnore = esbCompareIgnoreMapper.selectIgnoredField(esbDbChangeReqtDto.getInterfaceId());
        List<EsbCompareIgnore> globalIgnoreList = esbCompareIgnoreMapper.selectIgnoredFieldGlobal();
        List<String> esbCompareIgnoreList = globalIgnoreList.stream().map(EsbCompareIgnore::getIgnoredField).collect(Collectors.toList());
        if (ObjectUtil.isNotNull(esbCompareIgnore) && ObjectUtil.isNotNull(esbCompareIgnore.getIgnoredField())) {
            esbCompareIgnoreList.addAll(Arrays.asList(esbCompareIgnore.getIgnoredField().split(",")));
        }
        CompareDto compareDto = new CompareDto(post1, post2, esbCompareIgnoreList);
        String diffField = compare(compareDto);
        return new EsbDbChangeRespDto(post1, post2, diffField);
    }

    private String compare(CompareDto compareDto) {
        Document document1 = XmlTool.readXmlFromStr(compareDto.getXml1());
        Document document2 = XmlTool.readXmlFromStr(compareDto.getXml2());

        XmlTool.removeNodes(document1, compareDto.getIgnoreField());
        XmlTool.removeNodes(document2, compareDto.getIgnoreField());

        XmlTool.convertToStructuredTag(document1);
        XmlTool.convertToStructuredTag(document2);

        Document flattened1 = XmlTool.flattenXML(document1);
        Document flattened2 = XmlTool.flattenXML(document2);

        Document sorted1 = XmlTool.sortXML(flattened1);
        Document sorted2 = XmlTool.sortXML(flattened2);

        return XmlTool.compareFlattenXml(sorted1.getDocument(), sorted2.getDocument());
    }

}
