package com.bqd.model.esbdata;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-07-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsbServiceModel {
    private String interfaceId;
    private String interfaceName;
    private String subjectDomain;
    private String releaseRange;
    private String serviceProvider;
    private String interfaceType;
    private String parseTime;
}
