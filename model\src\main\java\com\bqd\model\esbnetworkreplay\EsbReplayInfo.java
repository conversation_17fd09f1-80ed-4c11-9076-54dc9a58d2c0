package com.bqd.model.esbnetworkreplay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-08-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsbReplayInfo {
    private String id;
    private String replayPlanId;
    private String url;
    private String interfaceId;
    private int status;
    private String requestTime;
    private String responseTime;
    private String versionNumber;
}
