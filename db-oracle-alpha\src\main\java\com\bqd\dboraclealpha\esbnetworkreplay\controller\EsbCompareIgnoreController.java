package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbCompareIgnoreMapper;
import com.bqd.model.esbnetworkreplay.EsbCompareIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-12
 */
@RestController
@RequestMapping("/enr/esbCompareIgnore")
public class EsbCompareIgnoreController {

    @Autowired
    private EsbCompareIgnoreMapper esbCompareIgnoreMapper;

    @GetMapping("/selectIgnoredField")
    public EsbCompareIgnore selectIgnoredField(@RequestParam String interfaceId) {
        return esbCompareIgnoreMapper.selectIgnoredField(interfaceId);
    }

    @GetMapping("/selectIgnoredFieldGlobal")
    public List<EsbCompareIgnore> selectIgnoredFieldGlobal() {
        return esbCompareIgnoreMapper.selectIgnoredFieldGlobal();
    }

    @PostMapping("/insert")
    public void insert(@RequestBody EsbCompareIgnore esbCompareIgnore) {
        esbCompareIgnoreMapper.insert(esbCompareIgnore);
    }

    @PostMapping("/updateById")
    public void updateById(@RequestBody EsbCompareIgnore esbCompareIgnore) {
        esbCompareIgnoreMapper.updateById(esbCompareIgnore);
    }

    @GetMapping("/deleteIgnoredFieldGlobal")
    public void deleteIgnoredFieldGlobal(@RequestParam String ignoredField) {
        esbCompareIgnoreMapper.deleteIgnoredFieldGlobal(ignoredField);
    }

}
