package com.bqd.dboraclealpha.transactionchain.controller;

import com.bqd.dboraclealpha.transactionchain.mapper.WisdomTradeNameMapper;
import com.bqd.model.transactionchain.WisdomTradeName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
@RestController
@RequestMapping("/transactionChain/wisdom/tradeName")
public class WisdomTradeNameController implements com.bqd.base.rpc.transactionchain.WisdomTradeNameMapper {

    @Autowired
    private WisdomTradeNameMapper wisdomTradeNameMapper;

    @Override
    @PostMapping("/insert")
    public void insert(@RequestBody WisdomTradeName wisdomTradeName) {
        wisdomTradeNameMapper.insert(wisdomTradeName);
    }

    @Override
    @GetMapping("/selectByLike")
    public List<WisdomTradeName> selectByLike(@RequestParam String tradeName) {
        return wisdomTradeNameMapper.selectByLike(tradeName);
    }

    @Override
    @GetMapping("/selectById")
    public WisdomTradeName selectById(@RequestParam String id) {
        return wisdomTradeNameMapper.selectById(id);
    }
}
