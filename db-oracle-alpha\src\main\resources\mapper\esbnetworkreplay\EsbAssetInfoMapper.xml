<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbAssetInfoMapper">

    <resultMap id="esbAssetInfo" type="com.bqd.model.esbnetworkreplay.EsbAssetInfo">
        <result column="ASSET_ID" property="assetId" javaType="java.lang.String"/>
        <result column="HOST" property="host" javaType="java.lang.String"/>
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
        <result column="REQUEST_TIME" property="requestTime" javaType="java.lang.Long"/>
        <result column="RESPONSE_TIME" property="responseTime" javaType="java.lang.Long"/>
        <result column="SVC_CORR_ID" property="svcCorrId" javaType="java.lang.String"/>
        <result column="CHANNEL_ID" property="channelId" javaType="java.lang.String"/>
        <result column="TRANS_CDE" property="transCde" javaType="java.lang.String"/>
        <result column="CSMR_ID" property="csmrId" javaType="java.lang.String"/>
        <result column="VERSION_NUMBER" property="versionNumber" javaType="java.lang.String"/>
        <result column="ESB_RESP_MSG" property="esbRespMsg" javaType="java.lang.String"/>
        <result column="ESB_RESP_CODE" property="esbRespCode" javaType="java.lang.String"/>
        <result column="PROVIDER_ID" property="providerId" javaType="java.lang.String"/>
    </resultMap>
    
    <resultMap id="esbAssetInfoDetailDto" type="com.bqd.model.esbnetworkreplay.EsbAssetInfoDetailDto" extends="esbAssetInfo">
        <result column="REQUEST_BODY" property="requestBody" javaType="java.lang.String" />
        <result column="RESPONSE_BODY" property="responseBody" javaType="java.lang.String" />
    </resultMap>

    <resultMap id="assetInfoDto" type="com.bqd.model.esbnetworkreplay.EsbAssetInfoDto">
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
        <result column="INTERFACE_NAME" property="interfaceName" javaType="java.lang.String"/>
        <result column="SERVICE_PROVIDER" property="serviceProvider" javaType="java.lang.String"/>
        <result column="SUBJECT_DOMAIN" property="subjectDomain" javaType="java.lang.String"/>
        <result column="INTERFACE_TYPE" property="interfaceType" javaType="java.lang.String"/>
        <result column="COUNT_NUM" property="count" javaType="java.lang.Integer"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO ESB_ASSET_INFO (ASSET_ID, HOST, INTERFACE_ID, REQUEST_TIME, RESPONSE_TIME, SVC_CORR_ID, CHANNEL_ID,
                                    TRANS_CDE, CSMR_ID, VERSION_NUMBER, ESB_RESP_MSG, ESB_RESP_CODE, PROVIDER_ID)
        VALUES (#{assetId}, #{host}, #{interfaceId}, #{requestTime}, #{responseTime}, #{svcCorrId,jdbcType=VARCHAR},
                #{channelId,jdbcType=VARCHAR}, #{transCde,jdbcType=VARCHAR}, #{csmrId,jdbcType=VARCHAR},
                #{versionNumber,jdbcType=VARCHAR}, #{esbRespMsg,jdbcType=VARCHAR}, #{esbRespCode,jdbcType=VARCHAR},
                #{providerId,jdbcType=VARCHAR})
    </insert>
    
    <select id="selectInfoDetailByCondition" resultMap="esbAssetInfoDetailDto">
        SELECT t1.*, t2.REQUEST_BODY, t2.RESPONSE_BODY FROM (
        SELECT * FROM ESB_ASSET_INFO WHERE 1=1
        <if test="assetId != null and assetId.length() != 0">
            AND ASSET_ID = #{assetId}
        </if>
        <if test="host != null and host.length() != 0">
            AND HOST = #{host}
        </if>
        <if test="interfaceId != null and interfaceId.length() != 0">
            AND INTERFACE_ID = #{interfaceId}
        </if>
        <if test="reqtStartTime != null and reqtStartTime.length() != 0">
            AND REQUEST_TIME >= #{reqtStartTime}
        </if>
        <if test="reqtEndTime != null and reqtEndTime.length() != 0">
            AND #{reqtEndTime} >= RESPONSE_TIME
        </if>
        <if test="svcCorrId != null and svcCorrId.length() != 0">
            AND SVC_CORR_ID = #{svcCorrId}
        </if>
        <if test="channelId != null and channelId.length() != 0">
            AND CHANNEL_ID = #{channelId}
        </if>
        <if test="transCde != null and transCde.length() != 0">
            AND TRANS_CDE = #{transCde}
        </if>
        <if test="csmrId != null and csmrId.length() != 0">
            AND CSMR_ID = #{csmrId}
        </if>
        <if test="versionNumber != null and versionNumber.length() != 0">
            AND VERSION_NUMBER = #{versionNumber}
        </if>
        <if test="esbRespCode != null and esbRespCode.length() != 0">
            AND ESB_RESP_CODE = #{esbRespCode}
        </if>
        <if test="esbRespMsg != null and esbRespMsg.length() != 0">
            AND ESB_RESP_MSG = #{esbRespMsg}
        </if>
        <if test="providerId != null and providerId.length() != 0">
            AND PROVIDER_ID = #{providerId}
        </if>
        ORDER BY REQUEST_TIME DESC) t1 INNER JOIN ESB_ASSET_DETAIL t2 ON t1.ASSET_ID = t2.ASSET_ID
    </select>

    <delete id="deleteById">
        DELETE
        FROM ESB_ASSET_INFO
        WHERE ASSET_ID = #{assetId}
    </delete>

    <sql id="assetInfoInnerJoinServiceModel">
        SELECT COUNT_NUM, t2.*
        FROM (SELECT COUNT(*) COUNT_NUM, INTERFACE_ID
        FROM ESB_ASSET_INFO
        WHERE 1=1
        <if test="interfaceId != null and interfaceId.length() != 0">
            AND INTERFACE_ID = #{interfaceId}
        </if>
        <if test="startTime != null and startTime.length() != 0">
            AND REQUEST_TIME >= #{startTime}
        </if>
        <if test="endTime != null and endTime.length() != 0">
            AND #{endTime} >= RESPONSE_TIME
        </if>
        GROUP BY INTERFACE_ID) t1
        INNER JOIN ESB_SERVICE_MODEL t2 ON t1.INTERFACE_ID = t2.INTERFACE_ID
    </sql>

    <sql id="infoCondition">
        <if test="subjectDomain != null and subjectDomain.length() != 0">
            AND SUBJECT_DOMAIN = #{subjectDomain}
        </if>
        <if test="serviceProvider != null and serviceProvider.length() != 0">
            AND SERVICE_PROVIDER = #{serviceProvider}
        </if>
        <if test="interfaceType != null and interfaceType.length() != 0">
            AND INTERFACE_TYPE = #{interfaceType}
        </if>
    </sql>

    <sql id="infoDetailCondition">
        <if test="channelId != null and channelId.length() != 0">
            AND CHANNEL_ID = #{channelId}
        </if>
        <if test="transCde != null and transCde.length() != 0">
            AND TRANS_CDE = #{transCde}
        </if>
        <if test="csmrId != null and csmrId.length() != 0">
            AND CSMR_ID = #{csmrId}
        </if>
        <if test="versionNumber != null and versionNumber.length() != 0">
            AND VERSION_NUMBER = #{versionNumber}
        </if>
        <if test="esbRespCode != null and esbRespCode.length() != 0">
            AND ESB_RESP_CODE = #{esbRespCode}
        </if>
        <if test="esbRespMsg != null and esbRespMsg.length() != 0">
            AND ESB_RESP_MSG = #{esbRespMsg}
        </if>
        <if test="startTime != null and startTime.length() != 0">
            AND REQUEST_TIME >= #{startTime}
        </if>
        <if test="endTime != null and endTime.length() != 0">
            AND #{endTime} >= REQUEST_TIME
        </if>
    </sql>

    <select id="selectFieldValue" resultMap="assetInfoDto">
        SELECT DISTINCT (<foreach collection="list" item="field" separator=",">${field}</foreach>) FROM (SELECT t2.*
        FROM (
        (SELECT DISTINCT INTERFACE_ID
        FROM ESB_ASSET_INFO) t1
        INNER JOIN ESB_SERVICE_MODEL t2 ON t1.INTERFACE_ID = t2.INTERFACE_ID))
    </select>

    <select id="selectFieldValueByInterfaceId" resultType="java.lang.String">
        SELECT DISTINCT ${field}
        FROM ESB_ASSET_INFO
        WHERE INTERFACE_ID = #{interfaceId}
    </select>

    <select id="countByCondition" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM (
        <include refid="assetInfoInnerJoinServiceModel"/>
        WHERE 1=1<include refid="infoCondition"/>)
    </select>

    <select id="selectPagedByCondition" resultMap="assetInfoDto">
        SELECT * FROM (
        SELECT ROWNUM ROW_NUM, t3.* FROM (
        <include refid="assetInfoInnerJoinServiceModel"/>
        WHERE 1=1
        <include refid="infoCondition"/>
        ORDER BY COUNT_NUM) t3) WHERE ROW_NUM >=
        #{startRow} AND #{endRow} >= ROW_NUM
    </select>

    <select id="countInfoDetailByCondition" resultType="int">
        SELECT COUNT(*) FROM ESB_ASSET_INFO WHERE INTERFACE_ID = #{interfaceId}
        <include refid="infoDetailCondition"/>
    </select>

    <select id="selectInfoDetailPagedByCondition" resultMap="esbAssetInfo">
        SELECT * FROM (
        SELECT ROWNUM ROW_NUM, t1.* FROM (
        SELECT * FROM ESB_ASSET_INFO WHERE INTERFACE_ID = #{interfaceId}
        <include refid="infoDetailCondition"/>
        ORDER BY REQUEST_TIME DESC) t1) WHERE ROW_NUM >= #{startRow} AND #{endRow} >= ROW_NUM
    </select>

</mapper>