package com.bqd.model.projectmanagement;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GitRepoInfo {
    private String id;
    private String remoteUrl;
    private String localPath;
    private String repositoryName;
    private String branchName;
    private String scheduled;
    private String updateTime;
    private String repositoryComment;
    private String credentialId;
    private String updateStatus;
}
