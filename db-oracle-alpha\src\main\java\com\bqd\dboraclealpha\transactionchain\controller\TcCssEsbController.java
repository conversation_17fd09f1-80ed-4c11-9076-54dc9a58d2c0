package com.bqd.dboraclealpha.transactionchain.controller;

import com.bqd.dboraclealpha.transactionchain.mapper.TcCssEsbMapper;
import com.bqd.model.transactionchain.TcCssEsb;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-14
 */
@RestController
@RequestMapping("/transactionChain/cssEsb")
public class TcCssEsbController {

    @Autowired
    private TcCssEsbMapper tcCssEsbMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody TcCssEsb tcCssEsb) {
        tcCssEsbMapper.insert(tcCssEsb);
    }

    @PostMapping("/selectByCondition")
    public List<TcCssEsb> selectByCondition(@RequestBody TcCssEsb tcCssEsb) {
        return tcCssEsbMapper.selectByCondition(tcCssEsb);
    }

}
