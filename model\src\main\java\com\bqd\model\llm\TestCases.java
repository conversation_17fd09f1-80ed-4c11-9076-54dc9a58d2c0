package com.bqd.model.llm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName TEST_CASES
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class TestCases implements Serializable {
    private String id;

    private String functionId;

    private Date createTime;

    private Integer isProcessing;

    private Object statusMeta;

    private static final long serialVersionUID = 1L;
}