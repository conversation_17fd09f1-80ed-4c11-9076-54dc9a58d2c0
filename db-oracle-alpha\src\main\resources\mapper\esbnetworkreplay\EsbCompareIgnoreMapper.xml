<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbCompareIgnoreMapper">

    <resultMap id="esbCompareIgnore"
               type="com.bqd.model.esbnetworkreplay.EsbCompareIgnore">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="IGNORED_FIELD" property="ignoredField" javaType="java.lang.String"/>
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectIgnoredField" resultMap="esbCompareIgnore">
        SELECT *
        FROM ESB_COMPARE_IGNORE
        WHERE INTERFACE_ID = #{interfaceId}
    </select>

    <select id="selectIgnoredFieldGlobal" resultMap="esbCompareIgnore">
        SELECT *
        FROM ESB_COMPARE_IGNORE
        WHERE INTERFACE_ID = '1'
    </select>

    <insert id="insert">
        INSERT INTO ESB_COMPARE_IGNORE (ID, IGNORED_FIELD, INTERFACE_ID)
        VALUES (#{id}, #{ignoredField,jdbcType=VARCHAR}, #{interfaceId,jdbcType=VARCHAR})
    </insert>

    <update id="updateById">
        UPDATE ESB_COMPARE_IGNORE
        SET IGNORED_FIELD  = #{ignoredField,jdbcType=VARCHAR},
            INTERFACE_ID   = #{interfaceId,jdbcType=VARCHAR}
        WHERE ID = #{id}
    </update>

    <delete id="deleteIgnoredFieldGlobal">
        DELETE
        FROM ESB_COMPARE_IGNORE
        WHERE IGNORED_FIELD = #{ignoredField}
          AND INTERFACE_ID = '1'
    </delete>

</mapper>