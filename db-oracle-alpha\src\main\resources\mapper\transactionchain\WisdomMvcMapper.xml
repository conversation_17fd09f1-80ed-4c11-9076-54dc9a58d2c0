<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.transactionchain.mapper.WisdomMvcMapper">

    <resultMap id="wisdomMvc" type="com.bqd.model.transactionchain.WisdomMvc">
        <result property="mvcId" column="MVC_ID"/>
        <result property="bId" column="B_ID"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="fileName" column="FILE_NAME"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO WISDOM_MVC (MVC_ID, B_ID, DESCRIPTION, FILE_NAME)
        VALUES (#{mvcId}, #{bId}, #{description}, #{fileName})
    </insert>

    <delete id="truncateTable">
        TRUNCATE TABLE WISDOM_MVC
    </delete>

    <select id="selectAll" resultMap="wisdomMvc">
        SELECT * FROM WISDOM_MVC
    </select>

    <select id="selectByMvcId" resultMap="wisdomMvc">
        SELECT * FROM WISDOM_MVC WHERE MVC_ID = #{mvcId}
    </select>

    <select id="selectByLikeMvcId" resultMap="wisdomMvc">
        SELECT *
        FROM WISDOM_MVC
        WHERE MVC_ID LIKE CONCAT('%', CONCAT(#{mvcId}, '%'))
    </select>

    <select id="selectByLikeDescription" resultMap="wisdomMvc">
        SELECT *
        FROM WISDOM_MVC
        WHERE DESCRIPTION LIKE CONCAT('%', CONCAT(#{description}, '%'))
    </select>

    <select id="selectByBId" resultMap="wisdomMvc">
        SELECT *
        FROM WISDOM_MVC
        WHERE B_ID = #{bId}
    </select>

    <select id="selectZtByQz" resultMap="wisdomMvc">
        SELECT *
        FROM WISDOM_MVC
        WHERE MVC_ID LIKE CONCAT('%', CONCAT(#{qzName}, '%'))
    </select>
</mapper>