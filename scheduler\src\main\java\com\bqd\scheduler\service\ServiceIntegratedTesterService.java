package com.bqd.scheduler.service;

import com.bqd.base.response.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(name = "ServiceIntegratedTesterService", path = "/service/integrated-tester")
public interface ServiceIntegratedTesterService {

    @GetMapping("/financialStatement/clearUploadCache")
    Response clearUploadCache();

}
