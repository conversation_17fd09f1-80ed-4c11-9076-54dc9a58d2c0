package com.bqd.model.esbnetworkreplay;

import lombok.Data;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-20
 */
@Data
public class AssetDetailDto extends EsbAssetDetail {
    private String interfaceId;
    private String requestTime;
    private String responseTime;
    private String channelId;
    private String url;
    private String transCde;
    private String csmrId;
    private String versionNumber;
    private String esbRespMsg;
    private String esbRespCode;
}
