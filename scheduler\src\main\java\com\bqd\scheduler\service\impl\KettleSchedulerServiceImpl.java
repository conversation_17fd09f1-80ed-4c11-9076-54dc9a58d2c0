package com.bqd.scheduler.service.impl;

import cn.hutool.core.io.FileUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.scheduler.service.KettleSchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.pentaho.di.core.KettleEnvironment;
import org.pentaho.di.core.exception.KettleException;
import org.pentaho.di.core.plugins.PluginFolder;
import org.pentaho.di.core.plugins.StepPluginType;
import org.pentaho.di.core.util.EnvUtil;
import org.pentaho.di.trans.Trans;
import org.pentaho.di.trans.TransMeta;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-01-15
 */
@Service
@Slf4j
public class KettleSchedulerServiceImpl implements KettleSchedulerService {

    @Value("${path.kettle-json-plugin}")
    private String kettleJsonPluginPath;
    @Value("${path.ktr.xhx-rb}")
    private String xhxRbKtrPath;

    @Override
    public void xhxRb() {
        List<File> ktrFileList = getKtrFiles(xhxRbKtrPath);
        for (File ktrFile : ktrFileList) {
            log.info("新核心日报文件[{}]执行开始", ktrFile.getName());
            executeKtr(ktrFile);
            log.info("新核心日报文件[{}]执行完成", ktrFile.getName());
        }
    }

    private void executeKtr(File ktrFile) {
        try {
            StepPluginType.getInstance().getPluginFolders().add(new PluginFolder(kettleJsonPluginPath, false, true));
            KettleEnvironment.init();
            EnvUtil.environmentInit();
            TransMeta transMeta = new TransMeta(ktrFile.getAbsolutePath());
            Trans trans = new Trans(transMeta);
            trans.execute(null);
            trans.waitUntilFinished();
            if (trans.getErrors() > 0) {
                throw new CustomizedException("转换过程中出现错误");
            }
        } catch (KettleException e) {
            throw new RuntimeException(e);
        }
    }

    private List<File> getKtrFiles(String dir) {
        File[] files = FileUtil.ls(dir);
        List<File> fileList = new ArrayList<>();
        for (File file : files) {
            if (file.isFile() && file.getName().endsWith(".ktr")) {
                fileList.add(file);
            }
        }
        return fileList;
    }
}
