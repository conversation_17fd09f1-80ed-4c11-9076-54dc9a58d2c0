<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclebeta.xhx.mapper.XhxRbGrzxqkMapper">
    <resultMap id="xhxRbGrzxqk" type="com.bqd.model.xhxrb.XhxRbGrzxqk">
        <result column="GRP" property="grp" javaType="java.lang.String"/>
        <result column="TESTER" property="tester" javaType="java.lang.String"/>
        <result column="EXECUTE_COUNT" property="executeCount" javaType="java.lang.Integer"/>
        <result column="PASSED_COUNT" property="passedCount" javaType="java.lang.Integer"/>
        <result column="FAILED_COUNT" property="failedCount" javaType="java.lang.Integer"/>
        <result column="BLOCK_COUNT" property="blockCount" javaType="java.lang.Integer"/>
        <result column="EXECUTING_COUNT" property="executingCount" javaType="java.lang.Integer"/>
        <result column="CANCEL_COUNT" property="cancelCount" javaType="java.lang.Integer"/>
        <result column="DAY_EXECUTE_COUNT" property="dayExecuteCount" javaType="java.lang.Integer"/>
        <result column="DAY_PASSED_COUNT" property="dayPassedCount" javaType="java.lang.Integer"/>
        <result column="DAY_FAILED_COUNT" property="dayFailedCount" javaType="java.lang.Integer"/>
    </resultMap>

    <select id="selectAll" resultMap="xhxRbGrzxqk">
        SELECT *
        FROM T_B_XHX_RB_GRZXQK
    </select>

    <update id="pcdInsertGrpTester">
        CALL GRZXQK_INSERT_GRP_TESTER()
    </update>

    <update id="pcdUpdateExecuteCount">
        CALL GRZXQK_EXECUTE_COUNT()
    </update>

    <update id="pcdUpdatePassedCount">
        CALL GRZXQK_PASSED_COUNT()
    </update>

    <update id="pcdUpdateFailedCount">
        CALL GRZXQK_FAILED_COUNT()
    </update>

    <update id="pcdUpdateBlockCount">
        CALL GRZXQK_BLOCK_COUNT()
    </update>

    <update id="pcdUpdateExecutingCount">
        CALL GRZXQK_EXECUTING_COUNT()
    </update>

    <update id="pcdUpdateCancelCount">
        CALL GRZXQK_CANCEL_COUNT()
    </update>

    <update id="pcdUpdateDayExecuteCount">
        CALL GRZXQK_DAY_EXECUTE_COUNT(TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS'), TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS'))
    </update>

    <update id="pcdUpdateDayPassedCount">
        CALL GRZXQK_DAY_PASSED_COUNT(TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS'), TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS'))
    </update>

    <update id="pcdUpdateDayFailedCount">
        CALL GRZXQK_DAY_FAILED_COUNT(TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS'), TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS'))
    </update>

</mapper>
