package com.bqd.integratedtester.sibstranscheck.service.impl;

import cn.hutool.json.JSONObject;
import com.bqd.integratedtester.sibstranscheck.utils.SibsTransCheckUtils;
import org.dom4j.Element;
import org.dom4j.dom.DOMElement;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-05-10
 */
public class SibsRequestCheck extends SibsXmlJsonCheckAbstract {

    public SibsRequestCheck(String requestXml, String requestJson){
        super(requestXml, requestJson);
    }

    public boolean appHeadCheck() {
        return super.appHeadCheck("请求");
    }

    /**
     * localHead校验
     * @return
     */
    public boolean localHeadCheck(){
        //创建xml-appHdr副本
        Element appHdr = xml.element("appHdr").createCopy();
        //获取element下全部元素，并筛选confirmRet和ret（可能存在多个confirmRet和ret）
        List<Element> appHdrElements = appHdr.elements();
        List<Element> confirmRetXmlList = appHdrElements.stream().filter(element -> element.getName().equals("confirmRet")).collect(Collectors.toList());
        List<Element> retXmlList = appHdrElements.stream().filter(element -> element.getName().equals("ret")).collect(Collectors.toList());
        //构建<localHead>xml元素，并将confirmRet和ret添加进去
        DOMElement localHeadXmlElement = new DOMElement("localHead");
        confirmRetXmlList.forEach(confirmRetXml -> localHeadXmlElement.add(confirmRetXml.createCopy()));
        retXmlList.forEach(retXml -> localHeadXmlElement.add(retXml.createCopy()));
        HashSet<String> localHeadTagsSet = new HashSet<>();
        localHeadTagsSet.add("confirmRet");
        localHeadTagsSet.add("ret");
        //xml转json并将对象转为数组
        JSONObject convertedJsonObject = SibsTransCheckUtils.xmlToJsonObject(localHeadXmlElement.asXML(), "localHead");
        SibsTransCheckUtils.jsonObjectToArray(convertedJsonObject, localHeadTagsSet);
        String convertedJsonStr = convertedJsonObject.toString();
        //json比对
        String localHeadJsonStr = json.getJSONObject("localHead").toString();
        boolean result = SibsTransCheckUtils.jsonIsEqual(convertedJsonStr, localHeadJsonStr, this);
        if (!result){
            errMsg += "<b>[localHead校验] 请求localHead校验失败</b><br/>";
        }
        return result;
    }

    /**
     * sysHead校验
     * @return
     */
    public boolean sysHeadCheck(Set<String> reqtSysHeadIgnore){
        Element appHdr = xml.element("appHdr").createCopy();
        //移除json sysHead中不存在的字段
        appHdr.remove(appHdr.element("totalNum"));
        appHdr.remove(appHdr.element("pgupOrPgdn"));
        appHdr.remove(appHdr.element("totalRows"));
        appHdr.remove(appHdr.element("totalFlag"));
        appHdr.remove(appHdr.element("currentNum"));
        appHdr.remove(appHdr.element("ret"));
        appHdr.remove(appHdr.element("confirmRet"));

        //获取json中的sysHead
        JSONObject sysHead = json.getJSONObject("sysHead");
        for (Element element: appHdr.elements()){
            if (reqtSysHeadIgnore.contains(element.getName())) {
                continue;
            }
            String jsonVal = sysHead.getStr(element.getName());
            if (!SibsTransCheckUtils.valueIsEqual(element, jsonVal)){
                errMsg += "<b>[sysHead校验] 请求sysHead检查失败, " + element.getName() + "字段值不一致</b><br/>";
                return false;
            }
        }
        return true;
    }

    public boolean bodyCheck(boolean jsonRemoveEmptyTag, Set<String> objectToArrayTagsSet, Set<String> ignoreTagsSet) {
        return super.bodyCheck("请求", jsonRemoveEmptyTag, objectToArrayTagsSet, ignoreTagsSet);
    }

}
