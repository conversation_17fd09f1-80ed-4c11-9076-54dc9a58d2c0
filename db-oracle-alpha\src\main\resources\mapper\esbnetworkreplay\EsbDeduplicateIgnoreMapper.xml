<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbDeduplicateIgnoreMapper">
    <resultMap id="esbDeduplicateIgnore" type="com.bqd.model.esbnetworkreplay.EsbDeduplicateIgnore">
        <id column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
        <result column="IGNORED_FIELD" property="ignoredField" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectByInterfaceId" resultMap="esbDeduplicateIgnore">
        SELECT *
        FROM ESB_DEDUPLICATE_IGNORE
        WHERE INTERFACE_ID = #{interfaceId}
    </select>

    <insert id="insert">
        INSERT INTO ESB_DEDUPLICATE_IGNORE (INTERFACE_ID, IGNORED_FIELD)
        VALUES (#{interfaceId}, #{ignoredField})
    </insert>

    <update id="updateByInterfaceId">
        UPDATE ESB_DEDUPLICATE_IGNORE
        SET IGNORED_FIELD  = #{ignoredField}
        WHERE INTERFACE_ID = #{interfaceId}
    </update>

</mapper>