package com.bqd.dboraclealpha.esbnetworkreplay.mapper;

import com.bqd.model.esbnetworkreplay.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-08-07
 */
@Mapper
public interface EsbAssetInfoMapper {

    void insert(EsbAssetInfo assetInfo);

    List<EsbAssetInfoDetailDto> selectInfoDetailByCondition(EsbAssetInfoDto esbAssetInfoDto);

    void deleteById(String assetId);

    List<EsbAssetInfoDto> selectFieldValue(List<String> fieldList);

    List<String> selectFieldValueByInterfaceId(@Param("interfaceId") String interfaceId, @Param("field") String field);

    int countByCondition(AssetInfoSearchDto assetInfoSearchDto);

    List<EsbAssetInfoDto> selectPagedByCondition(AssetInfoSearchDto assetInfoSearchDto);

    int countInfoDetailByCondition(AssetInfoDetailSearchDto assetInfoDetailSearchDto);

    List<EsbAssetInfo> selectInfoDetailPagedByCondition(AssetInfoDetailSearchDto assetInfoDetailSearchDto);
}
