package com.bqd.integratedtester.sibstranscheck.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.bqd.integratedtester.sibstranscheck.utils.JsonValueToString;
import com.bqd.integratedtester.sibstranscheck.utils.SibsTransCheckUtils;
import lombok.Data;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.StringReader;
import java.util.Set;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-05-13
 */
@Data
public abstract class SibsXmlJsonCheckAbstract {
    protected Element xml;
    protected JSONObject json;

    protected String errMsg = "";

    public SibsXmlJsonCheckAbstract(String xml, String json) {
        try {
            //读取xml
            this.xml = new SAXReader().read(new StringReader(xml)).getRootElement();
            //移除xml中的空字段
            SibsTransCheckUtils.xmlRemoveEmptyTag(this.xml);

            //读取json
            this.json = JsonValueToString.deepCopyJson(new JSONObject(json));
        } catch (DocumentException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * appHead校验
     *
     * @return
     */
    public boolean appHeadCheck(String type) {
        //获取xml中的appHdr
        Element appHdr = xml.element("appHdr").createCopy();
        //逐一获取字段
        Element totalNumXml = appHdr.element("totalNum");
        Element pgupOrPgdnXml = appHdr.element("pgupOrPgdn");
        Element totalRowsXml = appHdr.element("totalRows");
        Element totalFlagXml = appHdr.element("totalFlag");
        Element currentNumXml = appHdr.element("currentNum");

        //获取json中的appHead
        JSONObject appHead = json.getJSONObject("appHead");
        String totalNumJson = null, pgupOrPgdnJson = null, totalRowsJson = null, totalFlagJson = null, currentNumJson = null;
        //逐一获取字段
        if (ObjectUtil.isNotNull(appHead)) {
             totalNumJson = appHead.getStr("totalNum");
             pgupOrPgdnJson = appHead.getStr("pgupOrPgdn");
             totalRowsJson = appHead.getStr("totalRows");
             totalFlagJson = appHead.getStr("totalFlag");
             currentNumJson = appHead.getStr("currentNum");
        }

        //进行判断
        if (!SibsTransCheckUtils.valueIsEqual(totalNumXml, totalNumJson)) {
            errMsg += "<b>[appHead校验] " + type + "appHead校验失败, totalNum不一致, xml中为: </b><br/>" + totalNumXml + "<br/>json中为: " + totalNumJson + "<br/>";
            return false;
        }
        if (!SibsTransCheckUtils.valueIsEqual(pgupOrPgdnXml, pgupOrPgdnJson)) {
            errMsg += "<b>[appHead校验] " + type + "appHead校验失败, pgupOrPgdn不一致, xml中为: </b><br/>"+ pgupOrPgdnXml + "<br/>json中为" + pgupOrPgdnJson + "<br/>";
            return false;
        }
        if (!SibsTransCheckUtils.valueIsEqual(totalRowsXml, totalRowsJson)) {
            errMsg += "<b>[appHead校验] " + type + "appHead校验失败, totalRows不一致, xml中为: </b><br/>" + totalRowsXml + "<br/>json中为: " + totalRowsJson + "<br/>";
            return false;
        }
        if (!SibsTransCheckUtils.valueIsEqual(totalFlagXml, totalFlagJson)) {
            errMsg += "<b>[appHead校验] " + type + "appHead校验失败, totalFlag不一致, xml中为: </b><br/>" + totalFlagXml + "<br/>json中为: " + totalFlagJson + "<br/>";
            return false;
        }
        if (!SibsTransCheckUtils.valueIsEqual(currentNumXml, currentNumJson)) {
            errMsg += "<b>[appHead校验] " + type + "appHead校验失败, currentNum不一致, xml中为: </b><br/>" + currentNumXml + "<br/>json中为: " + currentNumJson + "<br/>";
            return false;
        }
        return true;
    }

    /**
     * body校验
     * @param objectToArrayTagsSet
     * @return
     */
    public boolean bodyCheck(String type, boolean jsonRemoveEmptyTag, Set<String> objectToArrayTagsSet, Set<String> ignoreTagsSet){
        //创建xml appBody的副本
        Element appBody = xml.element("appBody").createCopy();
        //移除appBody中空字段
        SibsTransCheckUtils.xmlRemoveEmptyTag(appBody);
        //xml转换成json
        JSONObject convertedJsonObject = SibsTransCheckUtils.xmlToJsonObject(appBody.asXML(), "appBody");
        //转换后json将指定对象转为数组
        SibsTransCheckUtils.jsonObjectToArray(convertedJsonObject, objectToArrayTagsSet);
        //移除比对忽略键
        SibsTransCheckUtils.removeFields(convertedJsonObject, ignoreTagsSet);
        String convertedJsonStr = convertedJsonObject.toString();

        //获取json中的body
        JSONObject jsonBodyObject = json.getJSONObject("body");
        jsonBodyObject = ObjectUtil.isNull(jsonBodyObject) ? new JSONObject("{}") : jsonBodyObject;
        //判断是否需要移除json中为空的元素，且json不为空则移除json中为空的元素（可能出现没有body键的情况）
        if (jsonRemoveEmptyTag && ObjectUtil.isNotNull(jsonBodyObject)) {
            jsonBodyObject = SibsTransCheckUtils.cleanJson(jsonBodyObject);
            //SibsTransCheckUtils.jsonRemoveEmptyTag(jsonBodyObject);
        }
        //移除比对忽略键
        SibsTransCheckUtils.removeFields(jsonBodyObject, ignoreTagsSet);
        String jsonBodyStr = jsonBodyObject.toString();
        //进行比对
        boolean result = SibsTransCheckUtils.jsonIsEqual(convertedJsonStr, jsonBodyStr, this);
        if (!result){
            errMsg += "<b>[body校验] " + type + "body校验失败</b><br/>";
        }
        return result;
    }

}
