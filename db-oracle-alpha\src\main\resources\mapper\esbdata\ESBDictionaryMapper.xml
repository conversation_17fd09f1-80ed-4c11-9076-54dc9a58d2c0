<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbdata.mapper.EsbDictionaryMapper">
    <resultMap id="esbDictionary" type="com.bqd.model.esbdata.EsbDictionary">
        <id column="INTF_ID" property="interfaceId"/>
        <result column="INTF_NAME" property="interfaceName" jdbcType="VARCHAR"/>
        <result column="SUBJECT_DOMAIN" property="subjectDomain" jdbcType="VARCHAR"/>
        <result column="RELEASE_RANGE" property="releaseRange" jdbcType="VARCHAR"/>
        <result column="SERVICE_PROVIDER" property="serviceProvider" jdbcType="VARCHAR"/>
        <result column="INTERFACE_TYPE_CODE" property="interfaceTypeCode" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectByInterfaceId" resultMap="esbDictionary">
        SELECT *
        FROM TB_SERVICE_DICTIONARY
        WHERE INTF_ID = #{interfaceId}
    </select>

    <select id="selectAllSubjectDomain" resultType="java.lang.String">
        SELECT DISTINCT SUBJECT_DOMAIN
        FROM TB_SERVICE_DICTIONARY
    </select>

    <select id="selectAllServiceProvider" resultType="java.lang.String">
        SELECT DISTINCT SERVICE_PROVIDER
        FROM TB_SERVICE_DICTIONARY
    </select>

    <select id="selectAll" resultMap="esbDictionary">
        SELECT *
        FROM TB_SERVICE_DICTIONARY
    </select>

    <update id="update">
        UPDATE TB_SERVICE_DICTIONARY
        SET INTF_NAME           = #{serviceName, jdbcType=VARCHAR},
            SUBJECT_DOMAIN      = #{subjectDomain, jdbcType=VARCHAR},
            RELEASE_RANGE       = #{releaseRange, jdbcType=VARCHAR},
            SERVICE_PROVIDER    = #{serviceProvider, jdbcType=VARCHAR},
            INTERFACE_TYPE_CODE = #{interfaceTypeCode, jdbcType=VARCHAR}
        WHERE INTF_ID = #{serviceId}
    </update>

    <select id="selectByServiceProvider" resultMap="esbDictionary">
        SELECT *
        FROM TB_SERVICE_DICTIONARY
        WHERE SERVICE_PROVIDER = #{serviceProvider}
    </select>

    <select id="selectByServiceProviderList" resultMap="esbDictionary">
        SELECT * FROM TB_SERVICE_DICTIONARY WHERE SERVICE_PROVIDER IN
        <foreach collection="serviceProviderList" item="serviceProvider" open="(" close=")" separator=",">
            #{serviceProvider}
        </foreach>
    </select>

    <insert id="insertSelective">
        insert into TB_SERVICE_DICTIONARY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfaceId != null">INTF_ID,</if>
            <if test="interfaceName != null">INTF_NAME,</if>
            <if test="subjectDomain != null">SUBJECT_DOMAIN,</if>
            <if test="releaseRange != null">RELEASE_RANGE,</if>
            <if test="serviceProvider != null">SERVICE_PROVIDER,</if>
            <if test="interfaceTypeCode != null">INTERFACE_TYPE_CODE,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfaceId != null">#{interfaceId,jdbcType=VARCHAR},</if>
            <if test="interfaceName != null">#{interfaceName,jdbcType=VARCHAR},</if>
            <if test="subjectDomain != null">#{subjectDomain,jdbcType=VARCHAR},</if>
            <if test="releaseRange != null">#{releaseRange,jdbcType=VARCHAR},</if>
            <if test="serviceProvider != null">#{serviceProvider,jdbcType=VARCHAR},</if>
            <if test="interfaceTypeCode != null">#{interfaceTypeCode,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="updateSelective">
        update TB_SERVICE_DICTIONARY
        <set>
            <if test="interfaceName != null">INTF_NAME=#{interfaceName,jdbcType=VARCHAR},</if>
            <if test="subjectDomain != null">SUBJECT_DOMAIN=#{subjectDomain,jdbcType=VARCHAR},</if>
            <if test="releaseRange != null">RELEASE_RANGE=#{releaseRange,jdbcType=VARCHAR},</if>
            <if test="serviceProvider != null">SERVICE_PROVIDER=#{serviceProvider,jdbcType=VARCHAR},</if>
            <if test="interfaceTypeCode != null">INTERFACE_TYPE_CODE=#{interfaceTypeCode,jdbcType=VARCHAR},</if>
        </set>
        where INTF_ID = #{interfaceId,jdbcType=VARCHAR}
    </update>

    <select id="selectByLikeInterfaceId" resultMap="esbDictionary">
        SELECT *
        FROM TB_SERVICE_DICTIONARY
        WHERE INTF_ID LIKE CONCAT('%', CONCAT(#{interfaceId}, '%'))
    </select>


    <select id="getAllByBatch" resultMap="esbDictionary">
        SELECT
            INTF_ID,
            INTF_NAME,
            SUBJECT_DOMAIN,
            RELEASE_RANGE,
            SERVICE_PROVIDER,
            INTERFACE_TYPE_CODE
            FROM (
        SELECT a.*, ROWNUM rnum FROM (
        SELECT INTF_ID,
               INTF_NAME,
               SUBJECT_DOMAIN,
               RELEASE_RANGE,
               SERVICE_PROVIDER,
               INTERFACE_TYPE_CODE
        FROM TB_SERVICE_DICTIONARY
        ) a WHERE ROWNUM &lt;= #{endRow}
        ) WHERE rnum &gt; #{startRow}
    </select>
    <select id="selectInterfaceIdAndInterfaceName" resultMap="esbDictionary">
        select INTF_ID, INTF_NAME
        from TB_SERVICE_DICTIONARY
    </select>
    <select id="selectReleaseRangeByInterfaceId" resultMap="esbDictionary">
        select RELEASE_RANGE
        from TB_SERVICE_DICTIONARY
        where INTF_ID = #{interfaceId,jdbcType=VARCHAR}
    </select>

</mapper>