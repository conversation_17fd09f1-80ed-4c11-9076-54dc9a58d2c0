<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbReplayDetailMapper">

    <resultMap id="esbReplayDetail" type="com.bqd.model.esbnetworkreplay.EsbReplayDetail">
        <id property="infoId" column="INFO_ID" javaType="java.lang.String"/>
        <result property="replayRequestBody" column="REPLAY_REQUEST_BODY" javaType="java.lang.String"/>
        <result property="rawResponseBody" column="RAW_RESPONSE_BODY" javaType="java.lang.String"/>
        <result property="replayResponseBody" column="REPLAY_RESPONSE_BODY" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO ESB_REPLAY_DETAIL (INFO_ID, REPLAY_REQUEST_BODY, RAW_RESPONSE_BODY, REPLAY_RESPONSE_BODY)
        VALUES (#{infoId}, #{replayRequestBody}, #{rawResponseBody}, #{replayResponseBody})
    </insert>

    <select id="selectByInfoId" resultMap="esbReplayDetail">
        SELECT *
        FROM ESB_REPLAY_DETAIL
        WHERE INFO_ID = #{infoId}
    </select>

    <delete id="deleteByInfoId">
        DELETE
        FROM ESB_REPLAY_DETAIL
        WHERE INFO_ID = #{infoId}
    </delete>

    <select id="selectFieldValueByInfoId" resultMap="esbReplayDetail">
        SELECT ${field}
        FROM ESB_REPLAY_DETAIL
        WHERE INFO_ID = #{infoId}
    </select>

</mapper>
