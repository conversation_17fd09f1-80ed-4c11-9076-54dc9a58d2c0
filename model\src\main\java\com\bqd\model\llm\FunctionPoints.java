package com.bqd.model.llm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName FUNCTION_POINTS
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class FunctionPoints implements Serializable {
    private String id;

    private String docId;

    private String name;

    private Date createTime;

    private Object statusMeta;

    private static final long serialVersionUID = 1L;
}