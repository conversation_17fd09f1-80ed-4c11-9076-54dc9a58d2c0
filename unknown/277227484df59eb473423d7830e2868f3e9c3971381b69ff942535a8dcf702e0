package com.bqd.base.rpc.esbdata;

import com.bqd.model.esl.EslServerConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "EslServerConfigMapper", name = "db-mysql-alpha", path = "/db/mysql-alpha/esl/serverConfig")
public interface EslServerConfigMapper {

    @PostMapping("/insert")
    void insert(@RequestBody EslServerConfig eslServerConfig);

    @GetMapping("/selectIpAndServerName")
    List<EslServerConfig> selectIpAndServerName();

    @GetMapping("/selectByIp")
    List<EslServerConfig> selectByIp(@RequestParam String ip);
}
