package com.bqd.dboraclealpha.authoritypacket.controller;

import com.bqd.dboraclealpha.authoritypacket.mapper.AuthorityPacketCommonInfoMapper;
import com.bqd.model.authoritypacket.PacketCommonInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-08
 */
@RestController
@RequestMapping("/authorityPacket/commonInfo")
public class AuthorityPacketCommonInfoController {

    @Autowired
    private AuthorityPacketCommonInfoMapper authorityPacketCommonInfoMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody PacketCommonInfo packetCommonInfo) {
        authorityPacketCommonInfoMapper.insert(packetCommonInfo);
    }

    @PostMapping("/selectByCondition")
    public List<PacketCommonInfo> selectByCondition(@RequestBody PacketCommonInfo packetCommonInfo) {
        return authorityPacketCommonInfoMapper.selectByCondition(packetCommonInfo);
    }

    @PostMapping("/updateById")
    public void updateById(@RequestBody PacketCommonInfo packetCommonInfo) {
        authorityPacketCommonInfoMapper.updateById(packetCommonInfo);
    }

    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String id) {
        authorityPacketCommonInfoMapper.deleteById(id);
    }

}
