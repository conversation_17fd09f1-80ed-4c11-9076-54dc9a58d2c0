package com.bqd.dboraclealpha.chaos.controller;

import com.bqd.base.rpc.chaos.ChaosServerMapperRpc;
import com.bqd.dboraclealpha.chaos.mapper.ChaosServerMapper;
import com.bqd.model.chaos.ChaosServer;
import com.bqd.model.chaos.ChaosServerDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-27
 */
@RequestMapping("/chaos/server")
@RestController
public class ChaosServerController implements ChaosServerMapperRpc {

    @Autowired
    private ChaosServerMapper chaosServerMapper;

    @Override
    @PostMapping("/countDto")
    public int countDto(ChaosServerDto chaosServerDto) {
        return chaosServerMapper.countDto(chaosServerDto);
    }

    @Override
    @PostMapping("/selectDtoByCondition")
    public List<ChaosServerDto> selectDtoByCondition(@RequestParam Integer startRow, @RequestParam Integer endRow, @RequestBody ChaosServerDto chaosServerDto) {
        return chaosServerMapper.selectDtoByCondition(startRow, endRow, chaosServerDto);
    }

    @Override
    @PostMapping("/insert")
    public void insert(@RequestBody ChaosServer chaosServer) {
        chaosServerMapper.insert(chaosServer);
    }

    @Override
    @PostMapping("/updateById")
    public void updateById(@RequestBody ChaosServer chaosServer) {
        chaosServerMapper.updateById(chaosServer);
    }

    @Override
    public void deleteById(String id) {
        chaosServerMapper.deleteById(id);
    }
}
