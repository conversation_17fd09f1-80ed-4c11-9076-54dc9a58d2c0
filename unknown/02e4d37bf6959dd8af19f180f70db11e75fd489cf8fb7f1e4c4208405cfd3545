package com.bqd.dboraclealpha.authoritypacket.mapper;

import com.bqd.model.authoritypacket.PacketCommonInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AuthorityPacketCommonInfoMapper {
    void insert(PacketCommonInfo packetCommonInfo);

    List<PacketCommonInfo> selectByCondition(PacketCommonInfo packetCommonInfo);

    void updateById(PacketCommonInfo packetCommonInfo);

    void deleteById(String id);
}
