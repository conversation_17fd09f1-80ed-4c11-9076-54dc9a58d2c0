package com.bqd.dbmysqlalpha.llm.controller;


import com.bqd.dbmysqlalpha.llm.mapper.TestCasesMapper;
import com.bqd.model.llm.TestCases;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/llm/TestCases")
public class TestCasesController {

    @Autowired
    TestCasesMapper testCasesMapper;


    @GetMapping("/selectAllById")
    public TestCases selectAllById(@RequestParam String id) {
        return testCasesMapper.selectAllById(id);
    }

    @GetMapping("/selectAllByFunctionId")
    public TestCases selectAllByFunctionId(@RequestParam String functionId) {
        return testCasesMapper.selectAllByFunctionId(functionId);
    }

    @PostMapping("/insertSelective")
    public int insertSelective(@RequestBody  TestCases testCases){
        return testCasesMapper.insertSelective(testCases);
    }

    @PostMapping("/updateSelective")
    public int updateSelective(@RequestBody TestCases testCases){
        return testCasesMapper.updateSelective(testCases);
    }
}
