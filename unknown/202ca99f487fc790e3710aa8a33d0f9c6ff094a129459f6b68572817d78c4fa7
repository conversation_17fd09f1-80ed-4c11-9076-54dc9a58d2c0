package com.bqd.base.rpc.esbnetworkreplay;

import com.bqd.model.esbnetworkreplay.RecordDBEnv;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "RecordDBEnvMapper", name = "db-oracle-alpha", path = "/db/oracle-alpha/enr/config/dbEnv")
public interface RecordDBEnvMapper {
    @GetMapping("/selectAllOrderByCreateTimeDESC")
    List<RecordDBEnv> selectAllOrderByCreateTimeDESC();

    @PostMapping("/insert")
    void insert(@RequestBody RecordDBEnv recordDBEnv);

    @PostMapping("/updateById")
    void updateById(@RequestBody RecordDBEnv recordDBEnv);

    @PostMapping("/deleteById")
    void deleteById(@RequestParam String id);

    @GetMapping("/selectById")
    RecordDBEnv selectById(@RequestParam String id);
}
