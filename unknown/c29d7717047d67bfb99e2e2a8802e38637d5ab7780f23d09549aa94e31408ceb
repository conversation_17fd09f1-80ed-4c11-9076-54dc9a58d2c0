package com.bqd.base.exception;

import cn.hutool.core.util.StrUtil;
import com.bqd.base.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-25
 */
@Slf4j
@RestControllerAdvice
public class TestCenterExceptionHandler {

    @ExceptionHandler(value = CustomizedException.class)
    public Response customizedExceptionHandler(CustomizedException e) {
        if (StrUtil.isBlank(e.getPrintInfo())) {
            log.error("自定义异常捕获：{}", e.getMessage());
        } else {
            log.error("自定义异常捕获：{}；【异常信息：{}】", e.getMessage(), e.getPrintInfo());
        }
        return Response.fail(e.getMessage());
    }

    @ExceptionHandler(value = Exception.class)
    public Response exceptionHandler(Exception e) {
        log.error("全局异常捕获", e);
        return Response.fail(CustomizedExceptionEnum.INTERNAL_SERVER_ERROR.getErrorMsg());
    }

}
