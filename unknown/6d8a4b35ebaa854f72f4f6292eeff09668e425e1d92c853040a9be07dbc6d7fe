package com.bqd.dbmysqlalpha.sibsconvertcheck.controller;

import com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsHttpDataInfoMapper;
import com.bqd.model.sibstranscheck.SibsHttpDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/sibsConvertCheck/httpDataInfo")
public class SibsHttpDataInfoController {

    @Autowired
    private SibsHttpDataInfoMapper sibsHttpDataInfoMapper;

    @PostMapping("/sibsHttpDataInfo")
    public void insert(@RequestBody SibsHttpDataInfo sibsHttpDataInfo) {
        sibsHttpDataInfoMapper.insert(sibsHttpDataInfo);
    }

    @GetMapping("/selectReqtXml")
    public List<SibsHttpDataInfo> selectReqtXml() {
        return sibsHttpDataInfoMapper.selectReqtXml();
    }

    @GetMapping("/selectBySeqNoAndTransTimestamp")
    public List<SibsHttpDataInfo> selectBySeqNoAndTransTimestamp(@RequestParam("seqNo") String seqNo, @RequestParam("transTimestamp") String transTimestamp) {
        return sibsHttpDataInfoMapper.selectBySeqNoAndTransTimestamp(seqNo, transTimestamp);
    }

    @GetMapping("/countBySeqNoAndTransTimestampIfExists")
    public int countBySeqNoAndTransTimestampIfExists(@RequestParam("seqNo") String seqNo, @RequestParam("transTimestamp") String transTimestamp) {
        return sibsHttpDataInfoMapper.countBySeqNoAndTransTimestampIfExists(seqNo, transTimestamp);
    }

    @GetMapping("/selectPaged")
    public List<SibsHttpDataInfo> selectPaged(@RequestParam("startRow") int startRow, @RequestParam("pageSize") int pageSize, @RequestParam("seqNo") String seqNo, @RequestParam("transTimestamp") String transTimestamp) {
        return sibsHttpDataInfoMapper.selectPaged(startRow, pageSize, seqNo, transTimestamp);
    }

}
