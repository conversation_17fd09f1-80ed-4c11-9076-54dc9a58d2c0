package com.bqd.dbmysqlalpha.llm.mapper;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bqd.model.llm.FunctionPoints;

/**
* <AUTHOR>
* @description 针对表【FUNCTION_POINTS】的数据库操作Mapper
* @createDate 2025-05-13 14:39:14
* @Entity generator.domain.FunctionPoints
*/

@Mapper
public interface FunctionPointsMapper {

    FunctionPoints selectAllById(@Param("id") String id);

    int insertSelective(FunctionPoints functionPoints);

    int updateSelective(FunctionPoints functionPoints);

}




