package com.bqd.dbmysqlalpha.sibsconvertcheck.controller;

import com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsTransCheckConfigMapper;
import com.bqd.model.sibstranscheck.SibsTransCheckConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/sibsConvertCheck/transCheckConfig")
public class SibsTransCheckConfigController {

    @Autowired
    private SibsTransCheckConfigMapper sibsTransCheckConfigMapper;

    @GetMapping("/selectByType")
    public List<SibsTransCheckConfig> selectByType(@RequestParam String type) {
        return sibsTransCheckConfigMapper.selectByType(type);
    }

    @PostMapping("/insert")
    public void insert(@RequestBody SibsTransCheckConfig sibsTransCheckConfig) {
        sibsTransCheckConfigMapper.insert(sibsTransCheckConfig);
    }

    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String id) {
        sibsTransCheckConfigMapper.deleteById(id);
    }

}
