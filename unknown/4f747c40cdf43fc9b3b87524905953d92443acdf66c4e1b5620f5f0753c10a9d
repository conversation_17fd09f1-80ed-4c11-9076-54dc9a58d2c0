<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsHttpDataMapper">
    
    <resultMap id="sibsHttpData" type="com.bqd.model.sibstranscheck.SibsHttpData">
        <id column="INFO_ID" property="infoId" javaType="java.lang.String"/>
        <result column="DATA" property="data" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO SIBS_HTTP_DATA (INFO_ID, DATA)
        VALUES (#{infoId}, #{data})
    </insert>
    
    <select id="selectByInfoId" resultMap="sibsHttpData">
        SELECT *
        FROM SIBS_HTTP_DATA
        WHERE INFO_ID = #{infoId}
    </select>
    
</mapper>