package com.bqd.dbmysqlalpha.llm.controller;


import com.bqd.dbmysqlalpha.llm.mapper.DemandDocMapper;
import com.bqd.model.llm.DemandDoc;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/llm/DemandDoc")
public class DemandDocController {

    @Autowired
    private DemandDocMapper demandDocMapper;

    @GetMapping("/count")
    public int count() {
        return demandDocMapper.count();
    }

    @GetMapping("/selectAllById")
    public DemandDoc selectAllById(@RequestParam String id) {
        return demandDocMapper.selectAllById(id);
    }

    @GetMapping("/selectPageLimit")
    public List<DemandDoc> selectPageLimit(@RequestParam int start, @RequestParam int size) {
        return demandDocMapper.selectPageLimit(start, size);
    }

    @GetMapping("/deleteById")
    public int deleteById(@RequestParam String id) {
        return demandDocMapper.deleteById(id);
    }

    @PostMapping("/insertSelective")
    public int insertSelective(@RequestBody DemandDoc demandDoc) {
        return demandDocMapper.insertSelective(demandDoc);
    }

    @PostMapping("/updateSelective")
    public int updateSelective(@RequestBody DemandDoc demandDoc) {
        return demandDocMapper.updateSelective(demandDoc);
    }
}
