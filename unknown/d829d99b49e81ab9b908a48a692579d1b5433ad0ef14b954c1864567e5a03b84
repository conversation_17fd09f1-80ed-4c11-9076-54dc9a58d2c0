package com.bqd.dboraclealpha.esbdata.mapper;

import com.bqd.model.esbdata.EsbServiceModel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface EsbServiceModelMapper {
    List<EsbServiceModel> selectByCondition(EsbServiceModel esbServiceModel);

    List<EsbServiceModel> selectFieldValue(List<String> fieldList);

    void batchInsert(List<EsbServiceModel> eslEsbServiceModelList);

    List<EsbServiceModel> selectInterfaceIdAndName();

    String selectReleaseRangeByInterfaceId(String interfaceId);

    List<String> selectAllServiceProvider();

    List<String> selectReleaseRangeByServiceProvider(String serviceProvider);

    EsbServiceModel selectByInterfaceId(String interfaceId);
}
