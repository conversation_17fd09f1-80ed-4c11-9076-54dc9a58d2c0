package com.bqd.base.tools;

import com.bqd.base.exception.CustomizedException;

import java.sql.Connection;
import java.sql.DriverManager;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-14
 */
public class DbTool {

    /**
     * 根据数据库类型加载驱动
     *
     * @param dbType
     */
    public static void loadDriver(String dbType) {
        try {
            switch (dbType.toLowerCase()) {
                case "mysql":
                    Class.forName("com.mysql.cj.jdbc.Driver");
                    break;
                case "oracle":
                    Class.forName("oracle.jdbc.driver.OracleDriver");
                    break;
                case "db2":
                    Class.forName("com.ibm.db2.jcc.DB2Driver");
                    break;
                default:
                    throw new RuntimeException("不支持的数据库类型: " + dbType);
            }
        } catch (Exception e) {
            throw new CustomizedException(e.getMessage());
        }
    }

    /**
     * 获取数据库连接
     *
     * @param dbType   数据库类型，用于选择合适的数据库驱动
     * @param url      数据库连接URL
     * @param username 数据库用户名
     * @param password 数据库密码
     * @return 返回数据库连接对象
     * @throws CustomizedException 当无法获取数据库连接时抛出自定义异常
     */
    public static Connection getDbConnection(String dbType, String url, String username, String password) {
        loadDriver(dbType);
        try {
            return DriverManager.getConnection(url, username, password);
        } catch (Exception e) {
            throw new CustomizedException(e.getMessage());
        }
    }

}
