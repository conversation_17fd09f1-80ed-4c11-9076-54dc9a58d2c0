package com.bqd.dbmysqlalpha.sibsconvertcheck.controller;

import com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsFileUploadMapper;
import com.bqd.model.sibstranscheck.SibsFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/sibsConvertCheck/fileUpload")
public class SibsFileUploadController {

    @Autowired
    private SibsFileUploadMapper sibsFileUploadMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody SibsFileUpload sibsFileUpload) {
        sibsFileUploadMapper.insert(sibsFileUpload);
    }

    @GetMapping("/selectById")
    public SibsFileUpload selectById(@RequestParam String id) {
        return sibsFileUploadMapper.selectById(id);
    }

    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String id) {
        sibsFileUploadMapper.deleteById(id);
    }

    @GetMapping("/selectAllByUploadTime")
    public List<SibsFileUpload> selectAllByUploadTime() {
        return sibsFileUploadMapper.selectAllByUploadTime();
    }
}
