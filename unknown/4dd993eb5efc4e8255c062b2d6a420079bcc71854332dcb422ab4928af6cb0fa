package com.bqd.base.rpc.common;

import com.bqd.model.redis.RedisDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-27
 */
@FeignClient(contextId = "RedisAlphaMapper", value = "db-redis-alpha", path = "/db/redis-alpha")
public interface RedisAlphaMapper {
    @PostMapping("/setString")
    void setString(@RequestBody RedisDto redisDto);

    @GetMapping("/getString")
    RedisDto getString(@RequestParam String key);
}
