<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsTransCheckStatisticMapper">
    <select id="countByInterfaceId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM SIBS_TRANS_CHECK_STATISTIC
        WHERE INTERFACE_ID = #{interfaceId}
    </select>

    <insert id="insert">
        INSERT INTO SIBS_TRANS_CHECK_STATISTIC (ID, INTERFACE_ID)
        VALUES (#{id}, #{interfaceId})
    </insert>
</mapper>