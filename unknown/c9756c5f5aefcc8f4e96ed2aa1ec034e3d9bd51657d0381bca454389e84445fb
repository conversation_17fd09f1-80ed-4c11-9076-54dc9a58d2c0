package com.bqd.base.rpc.xhx;

import com.bqd.model.xhxrb.XhxRbZxztqk;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "XhxRbZxztqkMapper", name = "db-oracle-beta", path = "/db/oracle-beta/xhxrb/zxztqk")
public interface XhxRbZxztqkMapper {

    @GetMapping("/selectAllDesc")
    List<XhxRbZxztqk> selectAllDesc();

    @GetMapping("/pcdInsertNames")
    void pcdInsertNames();

    @GetMapping("/pcdInsertTestsetTotal")
    void pcdInsertTestsetTotal();

    @GetMapping("/pcdUpdateCaseCount")
    void pcdUpdateCaseCount();

    @GetMapping("/pcdUpdateExecuteCount")
    void pcdUpdateExecuteCount();

    @GetMapping("/pcdUpdatePassedCount")
    void pcdUpdatePassedCount();

    @GetMapping("/pcdUpdateFailedCount")
    void pcdUpdateFailedCount();

    @GetMapping("/pcdUpdateBlockCount")
    void pcdUpdateBlockCount();

    @GetMapping("/pcdUpdateExecutingCount")
    void pcdUpdateExecutingCount();

    @GetMapping("/pcdUpdateCancelCount")
    void pcdUpdateCancelCount();

    @GetMapping("/pcdUpdateTodoCount")
    void pcdUpdateTodoCount();

    @GetMapping("/pcdUpdateExecuteRate")
    void pcdUpdateExecuteRate();

    @GetMapping("/pcdUpdatePassRate")
    void pcdUpdatePassRate();

    @GetMapping("/pcdUpdateDayExecuteCount")
    void pcdUpdateDayExecuteCount(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime);

    @GetMapping("/pcdUpdateDayPassedCount")
    void pcdUpdateDayPassedCount(@RequestParam("startTime") String startTime, @RequestParam("endTime")String endTime);

    @GetMapping("/pcdUpdateDayFailedCount")
    void pcdUpdateDayFailedCount(@RequestParam("startTime") String startTime, @RequestParam("endTime")String endTime);

    @GetMapping("/pcdUpdatePlanProgress")
    void pcdUpdatePlanProgress(@RequestParam String roundName);

    @GetMapping("/pcdUpdateProgressBias")
    void pcdUpdateProgressBias();

    @GetMapping("/pcdUpdateValidBugCount")
    void pcdUpdateValidBugCount(@RequestParam("roundName") String roundName, @RequestParam("systemPrefix") String systemPrefix);

    @GetMapping("/pcdUpdateTotalBugRate")
    void pcdUpdateTotalBugRate();

}
