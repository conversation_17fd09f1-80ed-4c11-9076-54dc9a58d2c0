<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsHttpDataInfoMapper">

    <resultMap id="sibsHttpDataInfo" type="com.bqd.model.sibstranscheck.SibsHttpDataInfo">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="CONTENT_TYPE" property="contentType" javaType="java.lang.String"/>
        <result column="HTTP_TYPE" property="httpType" javaType="java.lang.String"/>
        <result column="SEQ_NO" property="seqNo" javaType="java.lang.String"/>
        <result column="TRANS_TIMESTAMP" property="transTimestamp" javaType="java.lang.String"/>
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO SIBS_HTTP_DATA_INFO (ID, CONTENT_TYPE, HTTP_TYPE, SEQ_NO, TRANS_TIMESTAMP, INTERFACE_ID)
        VALUES (#{id}, #{contentType}, #{httpType}, #{seqNo}, #{transTimestamp}, #{interfaceId})
    </insert>

    <select id="selectReqtXml" resultMap="sibsHttpDataInfo">
        SELECT *
        FROM SIBS_HTTP_DATA_INFO
        WHERE CONTENT_TYPE = 'XML'
          AND HTTP_TYPE = 'reqt'
    </select>

    <select id="selectBySeqNoAndTransTimestamp" resultMap="sibsHttpDataInfo">
        SELECT *
        FROM SIBS_HTTP_DATA_INFO
        WHERE SEQ_NO = #{seqNo} AND TRANS_TIMESTAMP = #{transTimestamp}
    </select>

    <select id="countBySeqNoAndTransTimestampIfExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM SIBS_HTTP_DATA_INFO
        WHERE true
        <if test="seqNo != null and seqNo.length != 0">
            AND SEQ_NO = #{seqNo}
        </if>
        <if test="transTimestamp != null and transTimestamp.length != 0">
            AND TRANS_TIMESTAMP = #{transTimestamp}
        </if>
    </select>

    <select id="selectPaged" resultMap="sibsHttpDataInfo">
        SELECT *
        FROM SIBS_HTTP_DATA_INFO
        WHERE true
        <if test="seqNo != null and seqNo.length != 0">
            AND SEQ_NO = #{seqNo}
        </if>
        <if test="transTimestamp != null and transTimestamp.length != 0">
            AND TRANS_TIMESTAMP = #{transTimestamp}
        </if>
        LIMIT #{startRow}, #{pageSize}
    </select>

</mapper>