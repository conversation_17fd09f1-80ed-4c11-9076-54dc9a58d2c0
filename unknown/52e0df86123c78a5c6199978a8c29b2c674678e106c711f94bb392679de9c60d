package com.bqd.base.tools;

import cn.hutool.core.util.StrUtil;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: TODO
 * @Author: wangzi<PERSON>i
 * @CreateTime: 2024-11-26
 */
public class EsbTool {
    public static String extractInterfaceIdFromUrl(String url) {
        // 定义正则表达式，匹配6位数字
        String regex = "\\b\\d{6}\\b";

        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile(regex);

        // 创建 Matcher 对象
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    public static String extractUrlFromDesc(String desc) {
        return desc.split(" ")[1];
    }

    public static String extractHostFromPacket(String packet) {
        // 正则表达式定义，用于匹配ip:port
        String regex = "((?:[0-9]{1,3}\\.){3}[0-9]{1,3}):([0-9]{1,5})";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(packet);

        if (matcher.find()) {
            // 返回完整的匹配，即 ip:端口号
            return matcher.group(0);
        } else {
            return null; // 如果没有找到匹配项，则返回null
        }
    }

    public static String extractRequestBody(String str) {
        String reqtBody = StrUtil.subBetween(str, "<reqt>", "</reqt>");
        if (reqtBody == null) {
            return null;
        }
        return "<reqt>" + reqtBody + "</reqt>";
    }

    public static String extractResponseBody(String str) {
        String respBody = StrUtil.subBetween(str, "<resp>", "</resp>");
        if (respBody == null) {
            return null;
        }
        return "<resp>" + respBody + "</resp>";
    }

}
