package com.bqd.dbmysqlalpha.sibsconvertcheck.mapper;

import com.bqd.model.sibstranscheck.SibsTransCheckResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SibsTransCheckResultMapper {
    int countBySeqNoAndTransTimestamp(@Param("seqNo") String seqNo, @Param("transTimestamp") String transTimestamp);

    void insert(SibsTransCheckResult sibsTransCheckResult);

    int count();

    List<SibsTransCheckResult> selectPagedOBResult(@Param("startRow") int startRow, @Param("pageSize") int pageSize);

    List<String> selectDistinctInterfaceId();
}
