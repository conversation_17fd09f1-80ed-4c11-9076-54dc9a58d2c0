package com.bqd.dboraclealpha.esbdata.controller;

import com.bqd.dboraclealpha.esbdata.mapper.EsbInfoMapper;
import com.bqd.model.esbdata.EsbInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-26
 */
@RestController
@RequestMapping("/esbData/esbInfo")
public class EsbInfoController {

    @Autowired
    private EsbInfoMapper esbInfoMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody EsbInfo esbInfo) {
        esbInfoMapper.insert(esbInfo);
    }

    @GetMapping("/selectPagedByInterfaceId")
    public List<EsbInfo> selectPagedByInterfaceId(@RequestParam String interfaceId, @RequestParam String startRow, @RequestParam String endRow) {
        return esbInfoMapper.selectPagedByInterfaceId(interfaceId, startRow, endRow);
    }

    @GetMapping("/countByInterfaceId")
    public int countByInterfaceId(@RequestParam String interfaceId) {
        return esbInfoMapper.countByInterfaceId(interfaceId);
    }

    @GetMapping("/selectByInterfaceId")
    public List<EsbInfo> selectByInterfaceId(@RequestParam String interfaceId) {
        return esbInfoMapper.selectByInterfaceId(interfaceId);
    }

    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String id) {
        esbInfoMapper.deleteById(id);
    }

}
