<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dbmysqlalpha.chaos.mapper.ChaosExecutingInfoMapper">
    <resultMap id="chaosExecutingInfo" type="com.bqd.model.chaos.ChaosExecutingInfo">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="CHAOS_TYPE" property="chaosType" javaType="java.lang.String"/>
        <result column="SERVER_IP" property="serverIp" javaType="java.lang.String"/>
        <result column="START_TIME" property="startTime" javaType="java.lang.Long"/>
        <result column="DURATION" property="duration" javaType="java.lang.Long"/>
        <result column="EST_END_TIME" property="estEndTime" javaType="java.lang.Long"/>
        <result column="TARGET_INFO" property="targetInfo" javaType="java.lang.String"/>
        <result column="SERVER_INFO_ID" property="serverInfoId" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectByCondition" resultMap="chaosExecutingInfo">
        SELECT * FROM CHAOS_EXECUTING_INFO WHERE 1=1
        <if test="id != null">
            AND ID = #{id,jdbcType=VARCHAR}
        </if>
        <if test="chaosType != null">
            AND CHAOS_TYPE = #{chaosType,jdbcType=VARCHAR}
        </if>
        <if test="serverIp != null">
            AND SERVER_IP = #{serverIp,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null">
            AND #{startTime,jdbcType=VARCHAR} >= START_TIME
        </if>
        <if test="duration != null">
            AND DURATION = #{duration,jdbcType=VARCHAR}
        </if>
        <if test="estEndTime != null">
            AND EST_END_TIME >= #{estEndTime,jdbcType=VARCHAR}
        </if>
        <if test="targetInfo != null">
            AND TARGET_INFO = #{targetInfo,jdbcType=VARCHAR}
        </if>
        <if test="serverInfoId != null">
            AND SERVER_INFO_ID = #{serverInfoId,jdbcType=VARCHAR}
        </if>
    </select>

    <update id="updateById">
        UPDATE CHAOS_EXECUTING_INFO
        <set>
            <if test="chaosType != null">
                CHAOS_TYPE = #{chaosType,jdbcType=VARCHAR},
            </if>
            <if test="serverIp != null">
                SERVER_IP = #{serverIp,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                START_TIME = #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="duration != null">
                DURATION = #{duration,jdbcType=VARCHAR},
            </if>
            <if test="estEndTime != null">
                EST_END_TIME = #{estEndTime,jdbcType=VARCHAR},
            </if>
            <if test="targetInfo != null">
                TARGET_INFO = #{targetInfo,jdbcType=VARCHAR},
            </if>
            <if test="serverInfoId != null">
                SERVER_INFO_ID = #{serverInfoId,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            ID = #{id,jdbcType=VARCHAR}
        </where>
    </update>

    <insert id="insert">
        INSERT INTO CHAOS_EXECUTING_INFO (ID, CHAOS_TYPE, SERVER_IP, START_TIME, DURATION, EST_END_TIME, TARGET_INFO,
                                          SERVER_INFO_ID)
        VALUES (#{id,jdbcType=VARCHAR}, #{chaosType,jdbcType=VARCHAR}, #{serverIp,jdbcType=VARCHAR},
                #{startTime,jdbcType=VARCHAR}, #{duration,jdbcType=VARCHAR}, #{estEndTime,jdbcType=VARCHAR},
                #{targetInfo,jdbcType=VARCHAR}, #{serverInfoId,jdbcType=VARCHAR})
    </insert>

</mapper>