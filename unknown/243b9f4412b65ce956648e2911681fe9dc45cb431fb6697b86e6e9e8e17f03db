package com.bqd.dbmysqlalpha.llm.controller;


import com.bqd.dbmysqlalpha.llm.mapper.FunctionPointsMapper;
import com.bqd.model.llm.DemandDoc;
import com.bqd.model.llm.FunctionPoints;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/llm/FunctionPoints")
public class FunctionPointsController {

    @Autowired
    FunctionPointsMapper functionPointsMapper;


    @GetMapping("/selectAllById")
    public FunctionPoints selectAllById(@RequestParam String id) {
        return functionPointsMapper.selectAllById(id);
    }

    @PostMapping("/insertSelective")
    public int insertSelective(@RequestBody  FunctionPoints functionPoints){
        return functionPointsMapper.insertSelective(functionPoints);
    }

    @PostMapping("/updateSelective")
    public int updateSelective(@RequestBody FunctionPoints functionPoints){
        return functionPointsMapper.updateSelective(functionPoints);
    }
}
