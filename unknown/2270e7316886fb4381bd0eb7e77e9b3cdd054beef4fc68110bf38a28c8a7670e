package com.bqd.base.rpc.esbnetworkreplay;

import com.bqd.model.esbnetworkreplay.EsbReplayInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "EsbReplayInfoMapper", name = "db-oracle-alpha", path = "/db/oracle-alpha/enr/esbReplayInfo")
public interface EsbReplayInfoMapper {
    @PostMapping("/insert")
    void insert(@RequestBody EsbReplayInfo esbReplayInfo);

    @GetMapping("/selectByPlanId")
    List<EsbReplayInfo> selectByPlanId(@RequestParam String replayPlanId);

    @GetMapping("/deleteById")
    void deleteById(@RequestParam String id);
}
