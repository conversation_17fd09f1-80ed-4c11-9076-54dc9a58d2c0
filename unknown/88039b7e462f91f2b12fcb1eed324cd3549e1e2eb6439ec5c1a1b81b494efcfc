<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsTransCheckResultMapper">
    <resultMap id="sibsTransCheckResult" type="com.bqd.model.sibstranscheck.SibsTransCheckResult">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="SEQ_NO" property="seqNo" javaType="java.lang.String"/>
        <result column="TRANS_TIMESTAMP" property="transTimestamp" javaType="java.lang.String"/>
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
        <result column="CHECK_RESULT" property="checkResult" javaType="java.lang.String"/>
    </resultMap>

    <select id="countBySeqNoAndTransTimestamp" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM SIBS_TRANS_CHECK_RESULT
        WHERE SEQ_NO = #{seqNo} AND TRANS_TIMESTAMP = #{transTimestamp}
    </select>

    <insert id="insert">
        INSERT INTO SIBS_TRANS_CHECK_RESULT (ID, SEQ_NO, TRANS_TIMESTAMP, INTERFACE_ID, CHECK_RESULT)
        VALUES (#{id}, #{seqNo}, #{transTimestamp}, #{interfaceId}, #{checkResult})
    </insert>

    <select id="count" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM SIBS_TRANS_CHECK_RESULT
    </select>

    <select id="selectPagedOBResult" resultMap="sibsTransCheckResult">
        SELECT *
        FROM SIBS_TRANS_CHECK_RESULT
        ORDER BY CHECK_RESULT ASC
        LIMIT #{startRow}, #{pageSize}
    </select>

    <select id="selectDistinctInterfaceId" resultType="java.lang.String">
        SELECT DISTINCT(INTERFACE_ID)
        FROM SIBS_TRANS_CHECK_RESULT
    </select>
</mapper>