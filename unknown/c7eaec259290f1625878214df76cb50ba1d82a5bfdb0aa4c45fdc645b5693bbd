package com.bqd.dboraclealpha.dbmanagement.controller;

import com.bqd.dboraclealpha.dbmanagement.mapper.DbConnectionInfoMapper;
import com.bqd.model.dbmanagement.DbConnectionInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-11
 */
@RestController
@RequestMapping("/dbManagement/dbConnectionInfo")
public class DbConnectionInfoController implements com.bqd.base.rpc.dbinfo.DbConnectionInfoMapper {

    @Autowired
    private DbConnectionInfoMapper dbConnectionInfoMapper;

    @Override
    @PostMapping("/selectByCondition")
    public List<DbConnectionInfo> selectByCondition(@RequestBody DbConnectionInfo dbConnectionInfo) {
        return dbConnectionInfoMapper.selectByCondition(dbConnectionInfo);
    }

    @Override
    @PostMapping("/insert")
    public void insert(@RequestBody DbConnectionInfo dbConnectionInfo) {
        dbConnectionInfoMapper.insert(dbConnectionInfo);
    }

    @Override
    @PostMapping("/updateById")
    public void updateById(@RequestBody DbConnectionInfo dbConnectionInfo) {
        dbConnectionInfoMapper.updateById(dbConnectionInfo);
    }

    @Override
    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String id) {
        dbConnectionInfoMapper.deleteById(id);
    }
}
