<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsTransCheckConfigMapper">
    <resultMap id="sibsTransCheckConfig" type="com.bqd.model.sibstranscheck.SibsTransCheckConfig">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="TYPE" property="type" javaType="java.lang.String"/>
        <result column="CONTENT" property="content" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectByType" resultMap="sibsTransCheckConfig">
        SELECT *
        FROM SIBS_TRANS_CHECK_CONFIG
        WHERE TYPE = #{type}
    </select>

    <insert id="insert">
        INSERT INTO SIBS_TRANS_CHECK_CONFIG (ID, TYPE, CONTENT)
        VALUES (#{id}, #{type}, #{content})
    </insert>

    <delete id="deleteById">
        DELETE
        FROM SIBS_TRANS_CHECK_CONFIG
        WHERE ID = #{id}
    </delete>
</mapper>