package com.bqd.base.rpc.transactionchain;

import com.bqd.model.transactionchain.WisdomTradeName;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "WisdomTradeNameMapper", name = "db-oracle-alpha", path = "/db/oracle-alpha/transactionChain/wisdom/tradeName")
public interface WisdomTradeNameMapper {
    @PostMapping("/insert")
    void insert(@RequestBody WisdomTradeName wisdomTradeName);

    @GetMapping("/selectByLike")
    List<WisdomTradeName> selectByLike(@RequestParam String tradeName);

    @GetMapping("/selectById")
    WisdomTradeName selectById(@RequestParam String id);
}
