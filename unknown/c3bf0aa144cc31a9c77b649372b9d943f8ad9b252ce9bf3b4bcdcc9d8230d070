package com.bqd.dbmysqlalpha.sibsconvertcheck.mapper;

import com.bqd.model.sibstranscheck.SibsHttpDataInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SibsHttpDataInfoMapper {
    void insert(SibsHttpDataInfo sibsHttpDataInfo);

    List<SibsHttpDataInfo> selectReqtXml();

    List<SibsHttpDataInfo> selectBySeqNoAndTransTimestamp(@Param("seqNo") String seqNo, @Param("transTimestamp") String transTimestamp);

    int countBySeqNoAndTransTimestampIfExists(@Param("seqNo") String seqNo, @Param("transTimestamp") String transTimestamp);

    List<SibsHttpDataInfo> selectPaged(@Param("startRow") int startRow, @Param("pageSize") int pageSize, @Param("seqNo") String seqNo, @Param("transTimestamp") String transTimestamp);
}
