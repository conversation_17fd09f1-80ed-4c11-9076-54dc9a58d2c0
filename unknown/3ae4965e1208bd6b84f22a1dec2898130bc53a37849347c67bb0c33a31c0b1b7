package com.bqd.dboraclealpha.esbdata.controller;

import com.bqd.dboraclealpha.esbdata.mapper.EsbDictionaryMapper;
import com.bqd.model.esbdata.EsbDictionary;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
@RestController
@RequestMapping("/esbData/esbDictionary")
public class EsbDictionaryController implements com.bqd.base.rpc.esbdata.EsbDictionaryMapper {

    @Autowired
    private EsbDictionaryMapper esbDictionaryMapper;

    @Override
    @GetMapping("/selectByInterfaceId")
    public EsbDictionary selectByInterfaceId(@RequestParam String interfaceId) {
        return esbDictionaryMapper.selectByInterfaceId(interfaceId);
    }

    @Override
    @GetMapping("/selectAllSubjectDomain")
    public List<String> selectAllSubjectDomain() {
        return esbDictionaryMapper.selectAllSubjectDomain();
    }

    @Override
    @GetMapping("/selectAllServiceProvider")
    public List<String> selectAllServiceProvider() {
        return esbDictionaryMapper.selectAllServiceProvider();
    }

    @Override
    @GetMapping("/selectAll")
    public List<EsbDictionary> selectAll() {
        return esbDictionaryMapper.selectAll();
    }

    @Override
    @PostMapping("/update")
    public void update(@RequestBody EsbDictionary esbDictionary) {
        esbDictionaryMapper.update(esbDictionary);
    }

    @Override
    @GetMapping("/selectByServiceProvider")
    public List<EsbDictionary> selectByServiceProvider(String serviceProvider) {
        return esbDictionaryMapper.selectByServiceProvider(serviceProvider);
    }

    @Override
    @GetMapping("/selectByServiceProviderList")
    public List<EsbDictionary> selectByServiceProviderList(List<String> serviceProviderList) {
        return esbDictionaryMapper.selectByServiceProviderList(serviceProviderList);
    }

    @Override
    @GetMapping("/insertSelective")
    public int insertSelective(EsbDictionary esbDictionary) {
        return esbDictionaryMapper.insertSelective(esbDictionary);
    }

    @Override
    @GetMapping("/updateSelective")
    public int updateSelective(EsbDictionary esbDictionary) {
        return esbDictionaryMapper.updateSelective(esbDictionary);
    }

    @Override
    @GetMapping("/selectByLikeInterfaceId")
    public List<EsbDictionary> selectByLikeInterfaceId(String interfaceId) {
        return esbDictionaryMapper.selectByLikeInterfaceId(interfaceId);
    }

    @Override
    @GetMapping("/getAllByBatch")
    public List<EsbDictionary> getAllByBatch(int startRow, int endRow) {
        return esbDictionaryMapper.getAllByBatch(startRow, endRow);
    }

    @Override
    @GetMapping("/selectInterfaceIdAndInterfaceName")
    public List<EsbDictionary> selectInterfaceIdAndInterfaceName() {
        return esbDictionaryMapper.selectInterfaceIdAndInterfaceName();
    }

    @Override
    @GetMapping("/selectReleaseRangeByInterfaceId")
    public EsbDictionary selectReleaseRangeByInterfaceId(String interfaceId) {
        return esbDictionaryMapper.selectReleaseRangeByInterfaceId(interfaceId);
    }
}
