<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dbmysqlalpha.llm.mapper.DemandDocMapper">

    <resultMap id="BaseResultMap" type="com.bqd.model.llm.DemandDoc">
            <id property="id" column="id" jdbcType="CHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="isProcessing" column="is_processing" jdbcType="TINYINT"/>
            <result property="statusMeta" column="status_meta" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,create_time,
        user_id,is_processing,status_meta
    </sql>

    <select id="selectPageLimit" resultMap="BaseResultMap" parameterType="map">
        SELECT
        id,name,create_time,user_id,is_processing
        FROM DEMAND_DOC order by create_time desc
        LIMIT #{start}, #{size}
    </select>

    <delete id="deleteById">
        delete
        from DEMAND_DOC
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insertSelective">
        insert into DEMAND_DOC
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="isProcessing != null">is_processing,</if>
            <if test="statusMeta != null">status_meta,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=CHAR},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
            <if test="isProcessing != null">#{isProcessing,jdbcType=TINYINT},</if>
            <if test="statusMeta != null">#{statusMeta,jdbcType=OTHER},</if>
        </trim>
    </insert>
    <update id="updateSelective">
        update DEMAND_DOC
        <set>
            <if test="name != null">name=#{name,jdbcType=VARCHAR},</if>
            <if test="createTime != null">create_time=#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="userId != null">user_id=#{userId,jdbcType=VARCHAR},</if>
            <if test="isProcessing != null">is_processing=#{isProcessing,jdbcType=NUMERIC},</if>
            <if test="statusMeta != null">status_meta=#{statusMeta},</if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="count" resultType="int">
        select count(*)
        from DEMAND_DOC
    </select>
    <select id="selectAllById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from DEMAND_DOC
        where
        id = #{id,jdbcType=VARCHAR}
    </select>

</mapper>
