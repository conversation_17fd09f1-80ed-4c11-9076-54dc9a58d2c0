package com.bqd.base.rpc.esbnetworkreplay;

import com.bqd.model.esbnetworkreplay.EsbReplaceField;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "EsbReplaceFieldMapper", name = "db-oracle-alpha", path = "/db/oracle-alpha/enr/esbReplaceField")
public interface EsbReplaceFieldMapper {
    @GetMapping("/selectByInterfaceId")
    List<EsbReplaceField> selectByInterfaceId(@RequestParam String interfaceId);

    @GetMapping("/deleteById")
    void deleteById(String id);

    @PostMapping("/insert")
    void insert(@RequestBody EsbReplaceField esbReplaceField);
}
