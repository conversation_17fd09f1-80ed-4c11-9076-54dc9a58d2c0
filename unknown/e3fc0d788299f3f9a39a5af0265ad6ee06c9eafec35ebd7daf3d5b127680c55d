package com.bqd.dbmysqlalpha.llm.mapper;
import java.util.List;
import com.bqd.model.llm.DemandDoc;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【DEMAND_DOC】的数据库操作Mapper
* @createDate 2025-04-21 16:35:32
* @Entity generator.domain.DemandDoc
*/

@Mapper
public interface DemandDocMapper {

    int count();

    DemandDoc selectAllById(@Param("id") String id);

    List<DemandDoc> selectPageLimit(@Param("start") int start, @Param("size") int size);

    int deleteById(@Param("id") String id);

    int insertSelective(DemandDoc demandDoc);

    int updateSelective(DemandDoc demandDoc);

}




