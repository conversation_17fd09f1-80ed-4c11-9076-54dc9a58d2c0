package com.bqd.base.rpc.esbdata;

import com.bqd.model.esbdata.EsbDictionary;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "EsbDictionaryMapper", name = "db-oracle-alpha", path = "/db/oracle-alpha/esbData/esbDictionary")
public interface EsbDictionaryMapper {
    @GetMapping("/selectByInterfaceId")
    EsbDictionary selectByInterfaceId(@RequestParam String interfaceId);

    @GetMapping("/selectByLikeInterfaceId")
    List<String> selectAllSubjectDomain();

    @GetMapping("/selectAllServiceProvider")
    List<String> selectAllServiceProvider();

    @GetMapping("/selectAll")
    List<EsbDictionary> selectAll();

    @PostMapping("/update")
    void update(@RequestBody EsbDictionary esbDictionary);

    @GetMapping("/selectByServiceProvider")
    List<EsbDictionary> selectByServiceProvider(@RequestParam String serviceProvider);

    @PostMapping("/selectByServiceProviderList")
    List<EsbDictionary> selectByServiceProviderList(@RequestBody List<String> serviceProviderList);

    @PostMapping("/insertSelective")
    int insertSelective(EsbDictionary esbDictionary);

    @PostMapping("/updateSelective")
    int updateSelective(EsbDictionary esbDictionary);

    @GetMapping("/selectByLikeInterfaceId")
    List<EsbDictionary> selectByLikeInterfaceId(@RequestParam String interfaceId);

    @GetMapping("/getAllByBatch")
    List<EsbDictionary> getAllByBatch(@RequestParam("startRow") int startRow, @RequestParam("endRow") int endRow);

    @GetMapping("/selectInterfaceIdAndInterfaceName")
    List<EsbDictionary> selectInterfaceIdAndInterfaceName();

    @GetMapping("/selectReleaseRangeByInterfaceId")
    EsbDictionary selectReleaseRangeByInterfaceId(@RequestParam("interfaceId") String interfaceId);
}
