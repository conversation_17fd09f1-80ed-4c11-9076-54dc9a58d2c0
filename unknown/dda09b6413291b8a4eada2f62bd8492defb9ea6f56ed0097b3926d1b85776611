<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsFileUploadMapper">

    <resultMap id="sibsFileUpload" type="com.bqd.model.sibstranscheck.SibsFileUpload">
        <id property="id" column="id" javaType="java.lang.String"/>
        <result property="fileType" column="file_type" javaType="java.lang.String"/>
        <result property="uploadFileName" column="upload_file_name" javaType="java.lang.String"/>
        <result property="serverFileName" column="server_file_name" javaType="java.lang.String"/>
        <result property="filePath" column="file_path" javaType="java.lang.String"/>
        <result property="uploadTime" column="upload_time" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO SIBS_FILE_UPLOAD (ID, FILE_TYPE, UPLOAD_FILE_NAME, SERVER_FILE_NAME, FILE_PATH, UPLOAD_TIME)
        VALUES (#{id}, #{fileType}, #{uploadFileName}, #{serverFileName}, #{filePath}, #{uploadTime})
    </insert>

    <select id="selectById" resultMap="sibsFileUpload">
        SELECT *
        FROM SIBS_FILE_UPLOAD
        WHERE ID = #{id}
    </select>

    <delete id="deleteById">
        DELETE
        FROM SIBS_FILE_UPLOAD
        WHERE ID = #{id}
    </delete>

    <select id="selectAllByUploadTime" resultMap="sibsFileUpload">
        SELECT *
        FROM SIBS_FILE_UPLOAD
        ORDER BY UPLOAD_TIME DESC
    </select>

</mapper>