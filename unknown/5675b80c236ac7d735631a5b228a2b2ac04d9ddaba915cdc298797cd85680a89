package com.bqd.dboraclealpha.esbdata.mapper;

import com.bqd.model.esbdata.EsbFieldEnum;
import com.bqd.model.esbdata.EsbFieldEnumDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface EsbFieldEnumMapper {
    void insert(EsbFieldEnum esbFieldEnum);

    List<String> selectFieldByInterfaceId(String interfaceId);

    int countByCondition(EsbFieldEnum esbFieldEnum);

    List<EsbFieldEnum> pagedByCondition(EsbFieldEnumDto esbFieldEnumDto);

    void deleteByCondition(EsbFieldEnum esbFieldEnum);
}
