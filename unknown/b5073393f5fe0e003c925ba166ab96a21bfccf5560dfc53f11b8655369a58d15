package com.bqd.dbmysqlalpha.sibsconvertcheck.controller;

import com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsTransCheckStatisticMapper;
import com.bqd.model.sibstranscheck.SibsTransCheckStatistic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/sibsConvertCheck/transCheckStatistic")
public class SibsTransCheckStatisticController {

    @Autowired
    private SibsTransCheckStatisticMapper sibsTransCheckStatisticMapper;

    @GetMapping("/countByInterfaceId")
    public int countByInterfaceId(@RequestParam String interfaceId) {
        return sibsTransCheckStatisticMapper.countByInterfaceId(interfaceId);
    }

    @PostMapping("insert")
    public void insert(@RequestBody SibsTransCheckStatistic sibsTransCheckStatistic) {
        sibsTransCheckStatisticMapper.insert(sibsTransCheckStatistic);
    }

}
