package com.bqd.dboraclealpha.esbdata.controller;

import com.bqd.dboraclealpha.esbdata.mapper.EsbServiceModelMapper;
import com.bqd.model.esbdata.EsbServiceModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-13
 */
@RestController
@RequestMapping("/esbData/serviceModel")
public class EsbServiceModelController {

    @Autowired
    private EsbServiceModelMapper esbServiceModelMapper;

    @PostMapping("/selectByCondition")
    public List<EsbServiceModel> selectByCondition(@RequestBody EsbServiceModel esbServiceModel) {
        return esbServiceModelMapper.selectByCondition(esbServiceModel);
    }

    @GetMapping("/selectFieldValue")
    public List<EsbServiceModel> selectFieldValue(@RequestParam List<String> fieldList) {
        return esbServiceModelMapper.selectFieldValue(fieldList);
    }

    @PostMapping("/batchInsert")
    public void batchInsert(@RequestBody List<EsbServiceModel> eslEsbServiceModelList){
        esbServiceModelMapper.batchInsert(eslEsbServiceModelList);
    }

    @GetMapping("/selectInterfaceIdAndName")
    public List<EsbServiceModel> selectInterfaceIdAndName(){
        return esbServiceModelMapper.selectInterfaceIdAndName();
    }

    @GetMapping("/selectReleaseRangeByInterfaceId")
    public String selectReleaseRangeByInterfaceId(@RequestParam String interfaceId){
        return esbServiceModelMapper.selectReleaseRangeByInterfaceId(interfaceId);
    }

    @GetMapping("/selectAllServiceProvider")
    public List<String> selectAllServiceProvider(){
        return esbServiceModelMapper.selectAllServiceProvider();
    }

    @GetMapping("/selectReleaseRangeByServiceProvider")
    public List<String> selectReleaseRangeByServiceProvider(@RequestParam String serviceProvider){
        return esbServiceModelMapper.selectReleaseRangeByServiceProvider(serviceProvider);
    }

    @GetMapping("/selectByInterfaceId")
    public EsbServiceModel selectByInterfaceId(@RequestParam String interfaceId){
        return esbServiceModelMapper.selectByInterfaceId(interfaceId);
    }
}
