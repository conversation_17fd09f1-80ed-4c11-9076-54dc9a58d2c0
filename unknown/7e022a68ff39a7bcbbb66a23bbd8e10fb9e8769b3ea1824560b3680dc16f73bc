package com.bqd.dbmysqlalpha.sibsconvertcheck.controller;

import com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsHttpDataMapper;
import com.bqd.model.sibstranscheck.SibsHttpData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/sibsConvertCheck/httpData")
public class SibsHttpDataController {

    @Autowired
    private SibsHttpDataMapper sibsHttpDataMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody SibsHttpData sibsHttpData) {
        sibsHttpDataMapper.insert(sibsHttpData);
    }

    @GetMapping("/selectByInfoId")
    public SibsHttpData selectByInfoId(@RequestParam String id) {
        return sibsHttpDataMapper.selectByInfoId(id);
    }

}
