package com.bqd.dbmysqlalpha.sibsconvertcheck.controller;

import com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsTransCheckFailedMapper;
import com.bqd.model.sibstranscheck.SibsTransCheckFailed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/sibsConvertCheck/transCheckFailed")
public class SibsTransCheckFailedController {

    @Autowired
    private SibsTransCheckFailedMapper sibsTransCheckFailedMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody SibsTransCheckFailed sibsTransCheckFailed) {
        sibsTransCheckFailedMapper.insert(sibsTransCheckFailed);
    }

    @GetMapping("/selectByResultId")
    public SibsTransCheckFailed selectByResultId(@RequestParam String resultId) {
        return sibsTransCheckFailedMapper.selectByResultId(resultId);
    }

}
