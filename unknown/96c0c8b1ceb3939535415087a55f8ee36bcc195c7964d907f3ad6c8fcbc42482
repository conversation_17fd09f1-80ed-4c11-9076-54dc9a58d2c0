<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dbmysqlalpha.llm.mapper.TestCasesMapper">

    <resultMap id="BaseResultMap" type="com.bqd.model.llm.TestCases">
        <id property="id" column="id" jdbcType="CHAR"/>
        <result property="functionId" column="function_id" jdbcType="CHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="isProcessing" column="is_processing" jdbcType="TINYINT"/>
        <result property="statusMeta" column="status_meta" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,function_id,create_time,
        is_processing,status_meta
    </sql>
    <select id="selectAllById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TEST_CASES
        where
        id = #{id,jdbcType=VARCHAR}
    </select>
    <insert id="insertSelective">
        insert into TEST_CASES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="functionId != null">function_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="isProcessing != null">is_processing,</if>
            <if test="statusMeta != null">status_meta,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=CHAR},</if>
            <if test="functionId != null">#{functionId,jdbcType=CHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="isProcessing != null">#{isProcessing,jdbcType=TINYINT},</if>
            <if test="statusMeta != null">#{statusMeta,jdbcType=OTHER},</if>
        </trim>
    </insert>
    <update id="updateSelective">
        update TEST_CASES
        <set>
            <if test="functionId != null">function_id=#{functionId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">create_time=#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="isProcessing != null">is_processing=#{isProcessing,jdbcType=NUMERIC},</if>
            <if test="statusMeta != null">status_meta=#{statusMeta},</if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="selectAllByFunctionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from TEST_CASES
        where
        function_id = #{functionId,jdbcType=VARCHAR}
    </select>


</mapper>
