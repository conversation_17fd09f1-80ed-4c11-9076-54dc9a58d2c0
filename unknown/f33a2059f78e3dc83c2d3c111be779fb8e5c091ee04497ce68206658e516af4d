package com.bqd.base.rpc.esbdata;

import com.bqd.model.esbdata.EsbContent;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "EsbContentMapper", value = "db-oracle-alpha", path = "/db/oracle-alpha/esbData/esbContent")
public interface EsbContentMapper {

    @PostMapping("/insert")
    void insert(@RequestBody EsbContent esbContent);

    @GetMapping("/selectByInterfaceId")
    List<EsbContent> selectByInterfaceId(@RequestParam String interfaceId);

    @GetMapping("/deleteByInfoId")
    void deleteByInfoId(@RequestParam String infoId);
}
