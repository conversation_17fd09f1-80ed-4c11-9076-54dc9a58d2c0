package com.bqd.dbmysqlalpha.llm.mapper;

import java.util.List;

import com.bqd.model.llm.TestCases;
import org.apache.ibatis.annotations.Param;

import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【TEST_CASES】的数据库操作Mapper
 * @createDate 2025-05-15 14:38:53
 * @Entity generator.domain.TestCases
 */

@Mapper
public interface TestCasesMapper {

    TestCases selectAllById(@Param("id") String id);

    TestCases selectAllByFunctionId(@Param("functionId") String functionId);

    int insertSelective(TestCases testCases);

    int updateSelective(TestCases testCases);

}




