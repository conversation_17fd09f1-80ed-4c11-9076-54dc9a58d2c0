package com.bqd.dbmysqlalpha.sibsconvertcheck.controller;

import com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsTransCheckResultMapper;
import com.bqd.model.sibstranscheck.SibsTransCheckResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/sibsConvertCheck/transCheckResult")
public class SibsTransCheckResultController {

    @Autowired
    private SibsTransCheckResultMapper sibsTransCheckResultMapper;

    @GetMapping("/countBySeqNoAndTransTimestamp")
    public int countBySeqNoAndTransTimestamp(@RequestParam("seqNo") String seqNo, @RequestParam("transTimestamp") String transTimestamp) {
        return sibsTransCheckResultMapper.countBySeqNoAndTransTimestamp(seqNo, transTimestamp);
    }

    @PostMapping("/insert")
    public void insert(@RequestBody SibsTransCheckResult sibsTransCheckResult) {
        sibsTransCheckResultMapper.insert(sibsTransCheckResult);
    }

    @GetMapping("/count")
    public int count() {
        return sibsTransCheckResultMapper.count();
    }

    @GetMapping("/selectPagedOBResult")
    public List<SibsTransCheckResult> selectPagedOBResult(@RequestParam("startRow") int startRow, @RequestParam("pageSize") int pageSize) {
        return sibsTransCheckResultMapper.selectPagedOBResult(startRow, pageSize);
    }

    @GetMapping("/selectDistinctInterfaceId")
    public List<String> selectDistinctInterfaceId() {
        return sibsTransCheckResultMapper.selectDistinctInterfaceId();
    }

}
