<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dbmysqlalpha.llm.mapper.FunctionPointsMapper">

    <resultMap id="BaseResultMap" type="com.bqd.model.llm.FunctionPoints">
            <id property="id" column="id" jdbcType="CHAR"/>
            <result property="docId" column="doc_id" jdbcType="CHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="statusMeta" column="status_meta" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,doc_id,name,
        create_time,status_meta
    </sql>
    <select id="selectAllById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from FUNCTION_POINTS
        where
        id = #{id,jdbcType=VARCHAR}
    </select>
    <insert id="insertSelective">
        insert into FUNCTION_POINTS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="docId != null">doc_id,</if>
            <if test="name != null">name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="statusMeta != null">status_meta,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=CHAR},</if>
            <if test="docId != null">#{docId,jdbcType=CHAR},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="statusMeta != null">#{statusMeta,jdbcType=OTHER},</if>
        </trim>
    </insert>
    <update id="updateSelective">
        update FUNCTION_POINTS
        <set>
            <if test="docId != null">doc_id=#{docId,jdbcType=VARCHAR},</if>
            <if test="name != null">name=#{name,jdbcType=VARCHAR},</if>
            <if test="createTime != null">create_time=#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="statusMeta != null">status_meta=#{statusMeta},</if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>
