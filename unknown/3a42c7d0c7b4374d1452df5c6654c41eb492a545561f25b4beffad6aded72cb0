package com.bqd.base.tools;

import cn.hutool.core.util.StrUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-23
 */
public class HtmlTool {

    /**
     * 给定css字符串，修改指定属性的值，并返回修改后的字符串，若要修改的值为""或null，则移除该值
     *
     * @param cssStr
     * @param key
     * @param value
     * @return
     */
    public static String changeStyleStr(String cssStr, String key, String value) {
        // 使用 LinkedHashMap 保持插入顺序
        Map<String, String> cssProperties = new HashMap<>();

        // 按分号分割 CSS 声明，注意最后一个可能为空字符串
        String[] declarations = cssStr.split(";", -1);

        for (String declaration : declarations) {
            if (!declaration.trim().isEmpty()) { // 忽略空声明
                String[] parts = declaration.trim().split(":", 2);
                if (parts.length == 2) {
                    String cssKey = parts[0].trim().toLowerCase(); // 属性名转小写以保证匹配时不区分大小写
                    String cssValue = parts[1].trim();

                    // 添加到映射中
                    cssProperties.put(cssKey, cssValue);
                }
            }
        }

        cssProperties.put(key, value);

        // 重新构建 CSS 字符串
        StringBuilder result = new StringBuilder();
        for (Map.Entry<String, String> entry : cssProperties.entrySet()) {
            if (StrUtil.isBlank(entry.getValue())) {
                continue;
            }
            result.append(entry.getKey()).append(":").append(entry.getValue()).append(";");
        }

        // 移除最后一个多余的分号
        if (result.length() > 0 && result.charAt(result.length() - 1) == ';') {
            result.setLength(result.length() - 1);
        }

        return result.toString();
    }

}
