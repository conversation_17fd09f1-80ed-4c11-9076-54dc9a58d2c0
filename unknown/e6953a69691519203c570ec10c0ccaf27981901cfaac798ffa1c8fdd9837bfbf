package com.bqd.base.response;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class Response {
    /**
     * 响应码
     */
    private int respCode;

    /**
     * 响应信息
     */
    private String respMsg;

    /**
     * 响应数据
     */
    private Object respData;

    public static Response success(){
        return Response.customized(RespStatusEnum.SUCCESS, null);
    }

    public static Response success(Object data){
        return Response.customized(RespStatusEnum.SUCCESS, data);
    }

    public static Response fail(){
        return Response.customized(RespStatusEnum.FAIL, null);
    }

    public static Response fail(String respMsg){
        return Response.customized(RespStatusEnum.FAIL.getCode(), respMsg, null);
    }

    public static Response customized(RespStatusEnum respStatusEnum, Object data){
        return new Response(respStatusEnum.getCode(), respStatusEnum.getMsg(), data);
    }

    public static Response customized(int respCode, String respMsg, Object data){
        return new Response(respCode, respMsg, data);
    }
}
