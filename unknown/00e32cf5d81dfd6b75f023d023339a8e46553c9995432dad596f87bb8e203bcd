package com.bqd.dboraclealpha.esbdata.controller;

import com.bqd.dboraclealpha.esbdata.mapper.EsbContentMapper;
import com.bqd.model.esbdata.EsbContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-26
 */
@RestController
@RequestMapping("/esbData/esbContent")
public class EsbContentController {

    @Autowired
    private EsbContentMapper esbContentMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody EsbContent esbContent) {
        esbContentMapper.insert(esbContent);
    }

    @GetMapping("/selectByInterfaceId")
    public List<EsbContent> selectByInterfaceId(@RequestParam String interfaceId) {
        return esbContentMapper.selectByInterfaceId(interfaceId);
    }

    @GetMapping("/deleteByInfoId")
    public void deleteByInfoId(@RequestParam String infoId) {
        esbContentMapper.deleteByInfoId(infoId);
    }

}
