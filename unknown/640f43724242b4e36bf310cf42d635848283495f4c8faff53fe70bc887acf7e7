<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dbmysqlalpha.sibsconvertcheck.mapper.SibsTransCheckFailedMapper">

    <resultMap id="sibsTransCheckFailed" type="com.bqd.model.sibstranscheck.SibsTransCheckFailed">
        <id column="RESULT_ID" property="resultId" javaType="java.lang.String"/>
        <result column="FAILED_INFO" property="failedInfo" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO SIBS_TRANS_CHECK_FAILED (RESULT_ID, FAILED_INFO)
        VALUES (#{resultId}, #{failedInfo})
    </insert>

    <select id="selectByResultId" resultMap="sibsTransCheckFailed">
        SELECT *
        FROM SIBS_TRANS_CHECK_FAILED
        WHERE RESULT_ID = #{resultId}
    </select>
</mapper>