package com.bqd.base.rpc.sibstranscheck;

import com.bqd.model.sibstranscheck.SibsTransCheckResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "SibsTransCheckResultMapper", name = "db-mysql-alpha", path = "/db/mysql-alpha/sibsConvertCheck/transCheckResult")
public interface SibsTransCheckResultMapper {
    @GetMapping("/countBySeqNoAndTransTimestamp")
    int countBySeqNoAndTransTimestamp(@RequestParam("seqNo") String seqNo, @RequestParam("transTimestamp") String transTimestamp);

    @PostMapping("/insert")
    void insert(@RequestBody SibsTransCheckResult sibsTransCheckResult);

    @GetMapping("/count")
    int count();

    @GetMapping("/selectPagedOBResult")
    List<SibsTransCheckResult> selectPagedOBResult(@RequestParam("startRow") int startRow, @RequestParam("pageSize") int pageSize);

    @GetMapping("/selectDistinctInterfaceId")
    List<String> selectDistinctInterfaceId();

}
