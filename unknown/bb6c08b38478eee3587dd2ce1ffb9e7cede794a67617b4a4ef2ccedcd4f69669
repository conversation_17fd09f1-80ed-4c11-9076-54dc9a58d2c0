package com.bqd.dboraclealpha.esbdata.controller;

import com.bqd.dboraclealpha.esbdata.mapper.EsbFieldEnumMapper;
import com.bqd.model.esbdata.EsbFieldEnum;
import com.bqd.model.esbdata.EsbFieldEnumDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-27
 */
@RestController
@RequestMapping("/esbData/esbFieldEnum")
public class EsbFieldEnumController {

    @Autowired
    private EsbFieldEnumMapper esbFieldEnumMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody EsbFieldEnum esbFieldEnum) {
        esbFieldEnumMapper.insert(esbFieldEnum);
    }

    @GetMapping("/selectFieldByInterfaceId")
    public List<String> selectFieldByInterfaceId(@RequestParam String interfaceId) {
        return esbFieldEnumMapper.selectFieldByInterfaceId(interfaceId);
    }

    @PostMapping("/countByCondition")
    public int countByCondition(@RequestBody EsbFieldEnum esbFieldEnum) {
        return esbFieldEnumMapper.countByCondition(esbFieldEnum);
    }

    @PostMapping("/pagedByCondition")
    public List<EsbFieldEnum> pagedByCondition(@RequestBody EsbFieldEnumDto esbFieldEnumDto) {
        return esbFieldEnumMapper.pagedByCondition(esbFieldEnumDto);
    }

    @PostMapping("/deleteByCondition")
    public void deleteByCondition(@RequestBody EsbFieldEnum esbFieldEnum) {
        esbFieldEnumMapper.deleteByCondition(esbFieldEnum);
    }

}
