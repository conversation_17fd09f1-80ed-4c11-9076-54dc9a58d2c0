package com.bqd.dboraclealpha.esbdata.mapper;

import com.bqd.model.esbdata.EsbInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EsbInfoMapper {
    void insert(EsbInfo esbInfo);

    List<EsbInfo> selectPagedByInterfaceId(@Param("interfaceId") String interfaceId, @Param("startRow") String startRow, @Param("endRow") String endRow);

    int countByInterfaceId(String interfaceId);

    List<EsbInfo> selectByInterfaceId(String interfaceId);

    void deleteById(String id);
}
