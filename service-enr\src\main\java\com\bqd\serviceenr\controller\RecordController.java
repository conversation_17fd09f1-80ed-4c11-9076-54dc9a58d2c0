package com.bqd.serviceenr.controller;

import cn.hutool.core.util.StrUtil;
import com.bqd.base.response.Response;
import com.bqd.model.esbnetworkreplay.EsbRecordInfoDto;
import com.bqd.serviceenr.service.RecordManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: Esb流量回放-录制功能
 * @Author: wangzirui
 * @CreateTime: 2024-07-23
 */
@RequestMapping("/record")
@RestController
public class RecordController {

    @Autowired
    private RecordManagementService recordManagementService;

    /**
     * 根据条件查询记录信息
     * @param recordInfoDto
     * @return
     */
    @PostMapping("/info/fetchByCondition")
    public Response fetchInfoByCondition(@RequestParam Integer pageNo, @RequestParam Integer pageSize, @RequestBody EsbRecordInfoDto recordInfoDto) {
        return Response.success(recordManagementService.fetchInfoByCondition(pageNo, pageSize, recordInfoDto));
    }

    /**
     * 根据列名获取该列数据（去重）
     * @param colNameList
     * @return
     */
    @PostMapping("/info/fetchColData")
    public Response fetchInfoColData(@RequestBody List<String> colNameList) {
        colNameList = colNameList.stream().map(String::trim).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        return Response.success(recordManagementService.fetchInfoColData(colNameList));
    }

    /**
     * 根据infoId和列名获取该条记录的列数据
     * @param recordId
     * @param colNameList
     * @return
     */
    @PostMapping("/detail/fetchColData")
    public Response fetchDetailColData(@RequestParam String recordId, @RequestBody List<String> colNameList) {
        return Response.success(recordManagementService.fetchDetailColData(recordId, colNameList));
    }

    /**
     * 根据接口id（和开始结束时间）删除记录
     * @param interfaceId
     * @param reqtStartTime
     * @param reqtEndTime
     * @return
     */
    @GetMapping("/deleteRecord")
    public Response deleteRecord(@RequestParam String interfaceId, @RequestParam(required = false) String infoId, @RequestParam(required = false) Long reqtStartTime, @RequestParam(required = false) Long reqtEndTime) {
        recordManagementService.deleteRecord(interfaceId, infoId, reqtStartTime, reqtEndTime);
        return Response.success();
    }

}
