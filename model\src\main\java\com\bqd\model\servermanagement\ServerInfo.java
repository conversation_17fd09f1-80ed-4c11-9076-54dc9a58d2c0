package com.bqd.model.servermanagement;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2024-12-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ServerInfo {
    private String id;
    private String serverName;
    private String serverIp;
    private String username;
    private String pw;
    private String environment;
    private String authType;
    private String remark;
    private Long addTime;
}
