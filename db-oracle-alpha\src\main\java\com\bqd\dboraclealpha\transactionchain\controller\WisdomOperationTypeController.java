package com.bqd.dboraclealpha.transactionchain.controller;

import com.bqd.dboraclealpha.transactionchain.mapper.WisdomOperationTypeMapper;
import com.bqd.model.transactionchain.WisdomOperationType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
@RestController
@RequestMapping("/transactionChain/wisdom/operationType")
public class WisdomOperationTypeController implements com.bqd.base.rpc.transactionchain.WisdomOperationTypeMapper {

    @Autowired
    private WisdomOperationTypeMapper wisdomOperationTypeMapper;

    @Override
    @PostMapping("/insert")
    public void insert(@RequestBody WisdomOperationType wisdomOperationType) {
        wisdomOperationTypeMapper.insert(wisdomOperationType);
    }

    @Override
    @GetMapping("/selectByTradeNameId")
    public List<WisdomOperationType> selectByTradeNameId(@RequestParam String id) {
        return wisdomOperationTypeMapper.selectByTradeNameId(id);
    }

    @Override
    @GetMapping("/selectByLikeOperationType")
    public List<WisdomOperationType> selectByLikeOperationType(@RequestParam String mvcId) {
        return wisdomOperationTypeMapper.selectByLikeOperationType(mvcId);
    }

    @Override
    @GetMapping("/selectByLikeDescription")
    public List<WisdomOperationType> selectByLikeDescription(@RequestParam String description) {
        return wisdomOperationTypeMapper.selectByLikeDescription(description);
    }
}
