package com.bqd.serviceesbdata.service;

import com.bqd.model.common.PageDto;
import com.bqd.model.esbdata.EsbDbChangeReqtDto;
import com.bqd.model.esbdata.EsbDbChangeRespDto;
import com.bqd.model.esbdata.EsbFieldEnum;
import com.bqd.model.esbdata.EsbFieldEnumDto;

import java.util.List;

public interface EsbDbChangeService {
    void insertToDbByIntfId(String interfaceId, String startTime, String endTime);

    void deduplicate(String interfaceId, String fieldPath);

    int countInfoByInterfaceId(String interfaceId);

    void deleteByInterfaceId(String interfaceId);

    List<String> getFieldByInterfaceId(String interfaceId);

    PageDto<EsbFieldEnum> pagedByCondition(EsbFieldEnumDto esbFieldEnumDto);

    void deleteByCondition(EsbFieldEnum esbFieldEnum);

    EsbDbChangeRespDto sendAndCompare(EsbDbChangeReqtDto esbDbChangeReqtDto);
}
