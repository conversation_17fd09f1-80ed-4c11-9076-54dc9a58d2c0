<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbCompareMapper">

    <insert id="insert">
        INSERT INTO ESB_COMPARE (REPLAY_INFO_ID, REPLAY_PLAN_ID, DIFFERENT_FIELD)
        VALUES (#{replayInfoId}, #{replayPlanId}, #{differentField})
    </insert>

    <delete id="deleteByPlanId">
        DELETE
        FROM ESB_COMPARE
        WHERE REPLAY_PLAN_ID = #{replayPlanId}
    </delete>

</mapper>