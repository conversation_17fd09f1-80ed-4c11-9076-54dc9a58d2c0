package com.bqd.serviceserveroperation.controller;

import com.bqd.base.response.Response;
import com.bqd.model.servermanagement.ServerInfo;
import com.bqd.serviceserveroperation.service.ServerManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 服务器管理
 * <AUTHOR>
 * @CreateTime 2024-12-30
 */
@RestController
@RequestMapping("/serverManagement")
public class ServerManagementController {

    @Autowired
    private ServerManagementService serverManagementService;

    /**
     * 添加服务器
     *
     * @param serverInfo 服务器信息
     * @return 响应
     */
    @PostMapping("/addServer")
    public Response addServer(@RequestBody ServerInfo serverInfo) {
        serverManagementService.addServer(serverInfo);
        return Response.success();
    }

    /**
     * 分页查询服务器
     *
     * @param pageNo     页码
     * @param pageSize   每页大小
     * @param serverInfo 查询条件
     * @return 响应
     */
    @PostMapping("/getByPageInfo")
    public Response getByPageInfo(@RequestParam Integer pageNo, @RequestParam Integer pageSize, @RequestBody ServerInfo serverInfo) {
        return Response.success(serverManagementService.getByPageInfo(pageNo, pageSize, serverInfo));
    }

    /**
     * 根据条件查询服务器
     *
     * @param serverInfo 查询条件
     * @return 响应
     */
    @PostMapping("/getByCondition")
    public Response getByCondition(@RequestBody ServerInfo serverInfo) {
        return Response.success(serverManagementService.getByCondition(serverInfo));
    }

    /**
     * 更新服务器
     *
     * @param serverInfo 服务器信息
     * @return 响应
     */
    @PostMapping("/updateById")
    public Response updateById(@RequestBody ServerInfo serverInfo) {
        serverManagementService.updateById(serverInfo);
        return Response.success();
    }

    /**
     * 删除服务器
     *
     * @param id 服务器id
     * @return 响应
     */
    @GetMapping("/deleteById")
    public Response deleteById(@RequestParam String id) {
        serverManagementService.deleteById(id);
        return Response.success();
    }

}
