<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbdata.mapper.EsbServiceModelMapper">
    <resultMap id="esbServiceModel" type="com.bqd.model.esbdata.EsbServiceModel">
        <id column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
        <result column="INTERFACE_NAME" property="interfaceName" javaType="java.lang.String"/>
        <result column="SUBJECT_DOMAIN" property="subjectDomain" javaType="java.lang.String"/>
        <result column="RELEASE_RANGE" property="releaseRange" javaType="java.lang.String"/>
        <result column="SERVICE_PROVIDER" property="serviceProvider" javaType="java.lang.String"/>
        <result column="INTERFACE_TYPE" property="interfaceType" javaType="java.lang.String"/>
        <result column="PARSE_TIME" property="parseTime" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectByCondition" resultMap="esbServiceModel">
        SELECT * FROM ESB_SERVICE_MODEL WHERE 1=1
        <if test="interfaceId != null">
            AND INTERFACE_ID = #{interfaceId}
        </if>
        <if test="interfaceName != null">
            AND INTERFACE_NAME = #{interfaceName}
        </if>
        <if test="subjectDomain != null">
            AND SUBJECT_DOMAIN = #{subjectDomain}
        </if>
        <if test="releaseRange != null">
            AND RELEASE_RANGE = #{releaseRange}
        </if>
        <if test="serviceProvider != null">
            AND SERVICE_PROVIDER = #{serviceProvider}
        </if>
        <if test="interfaceType != null">
            AND INTERFACE_TYPE = #{interfaceType}
        </if>
    </select>

    <delete id="deleteByInterfaceId">
        DELETE
        FROM ESB_SERVICE_MODEL
        WHERE INTERFACE_ID = #{interfaceId}
    </delete>

    <insert id="insert">
        INSERT INTO ESB_SERVICE_MODEL (INTERFACE_ID, INTERFACE_NAME, SUBJECT_DOMAIN, RELEASE_RANGE,
                                       SERVICE_PROVIDER, INTERFACE_TYPE, PARSE_TIME)
        VALUES (#{interfaceId,jdbcType=VARCHAR}, #{interfaceName,jdbcType=VARCHAR}, #{subjectDomain,jdbcType=VARCHAR},
                #{releaseRange,jdbcType=VARCHAR},
                #{serviceProvider,jdbcType=VARCHAR}, #{interfaceType,jdbcType=VARCHAR}, #{parseTime,jdbcType=VARCHAR})
    </insert>

    <select id="selectFieldValue" resultMap="esbServiceModel">
        SELECT DISTINCT
        <foreach collection="list" item="field" separator=",">
            ${field}
        </foreach>
        FROM
        ESB_SERVICE_MODEL
    </select>

    <select id="selectByInterfaceId" resultMap="esbServiceModel">
        SELECT *
        FROM ESB_SERVICE_MODEL
        WHERE INTERFACE_ID = #{interfaceId}
    </select>
</mapper>