package com.bqd.serviceenr.controller;

import com.bqd.base.response.Response;
import com.bqd.model.esbnetworkreplay.ReplayReqtDto;
import com.bqd.serviceenr.service.ReplayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-08-21
 */
@RestController
@RequestMapping("/replay")
public class ReplayController {

    @Autowired
    private ReplayService replayService;

    @PostMapping("/replay")
    public Response replay(@RequestBody ReplayReqtDto replayReqtDto) {
        replayService.replay(replayReqtDto);
        return Response.success();
    }

    @PostMapping("/replayInOrder")
    public Response replayInOrder(@RequestBody ReplayReqtDto replayReqtDto) {
        replayService.replayInOrder(replayReqtDto);
        return Response.success();
    }

    @GetMapping("/replayByTime")
    public Response replayByTime(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        replayService.replayByTime(startTime, endTime);
        return Response.success();
    }

    @GetMapping("/getReplayPlan")
    public Response getReplayPlan() {
        return Response.success(replayService.getReplayPlan());
    }

    @GetMapping("/getReplayInfo")
    public Response getReplayInfo(@RequestParam("replayPlanId") String replayPlanId) {
        return Response.success(replayService.getReplayInfo(replayPlanId));
    }

    @GetMapping("/deleteReplayPlan")
    public Response deleteReplayPlan(@RequestParam("replayPlanId") String replayPlanId) {
        replayService.deleteReplayPlan(replayPlanId);
        return Response.success();
    }

    @GetMapping("/getReplayDetailFieldValue")
    public Response getReplayDetailFieldValue(@RequestParam("infoId") String infoId, @RequestParam("field") String field) {
        return Response.success(replayService.getReplayDetailFieldValue(infoId, field));
    }

}
