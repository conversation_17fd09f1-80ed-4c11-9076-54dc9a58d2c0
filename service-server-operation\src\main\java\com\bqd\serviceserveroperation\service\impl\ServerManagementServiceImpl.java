package com.bqd.serviceserveroperation.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.PageUtil;
import com.bqd.base.rpc.serveroperation.ServerInfoMapper;
import com.bqd.model.common.PageDto;
import com.bqd.model.servermanagement.ServerInfo;
import com.bqd.serviceserveroperation.service.ServerManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Description: 服务器管理
 * <AUTHOR>
 * @CreateTime 2024-12-30
 */
@Service
public class ServerManagementServiceImpl implements ServerManagementService {

    @Autowired
    private ServerInfoMapper serverInfoMapper;

    /**
     * 添加服务器
     *
     * @param serverInfo 服务器信息
     */
    @Override
    public void addServer(ServerInfo serverInfo) {
        serverInfo.setId(IdUtil.fastSimpleUUID());
        serverInfo.setAddTime(new Date());
        serverInfoMapper.insert(serverInfo);
    }

    /**
     * 分页查询服务器
     *
     * @param pageNo     页码
     * @param pageSize   每页大小
     * @param serverInfo 查询条件
     * @return 分页结果
     */
    @Override
    public PageDto<ServerInfo> getByPageInfo(int pageNo, int pageSize, ServerInfo serverInfo) {
        int totalCount = serverInfoMapper.count(serverInfo);
        int startRow = PageUtil.getStart(pageNo, pageSize) + 1;
        int endRow = PageUtil.getEnd(pageNo, pageSize);
        int totalPage = PageUtil.totalPage(totalCount, pageSize);
        List<ServerInfo> serverInfoList = serverInfoMapper.selectPaged(startRow, endRow, serverInfo);
        return new PageDto<>(pageNo, pageSize, totalPage, totalCount, serverInfoList);
    }

    /**
     * 根据条件查询服务器
     *
     * @param serverInfo 查询条件
     * @return 服务器列表
     */
    @Override
    public List<ServerInfo> getByCondition(ServerInfo serverInfo) {
        List<ServerInfo> serverInfoList = serverInfoMapper.selectByCondition(serverInfo);
        serverInfoList.forEach(each -> each.setPw(null));
        return serverInfoList;
    }

    /**
     * 更新服务器
     *
     * @param serverInfo 服务器信息
     */
    @Override
    public void updateById(ServerInfo serverInfo) {
        serverInfoMapper.updateById(serverInfo);
    }

    /**
     * 删除服务器
     *
     * @param id 服务器id
     */
    @Override
    public void deleteById(String id) {
        serverInfoMapper.deleteById(id);
    }

}
