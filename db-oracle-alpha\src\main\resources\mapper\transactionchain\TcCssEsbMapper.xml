<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.transactionchain.mapper.TcCssEsbMapper">

    <resultMap id="tcCssEsb" type="com.bqd.model.transactionchain.TcCssEsb">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO TC_CSS_ESB (ID, INTERFACE_ID)
        VALUES (#{id}, #{interfaceId})
    </insert>

    <select id="selectByCondition" resultMap="tcCssEsb">
        SELECT * FROM TC_CSS_ESB WHERE 1=1
        <if test="id != null">
            AND ID = #{id}
        </if>
        <if test="interfaceId != null">
            AND INTERFACE_ID = #{interfaceId}
        </if>
    </select>

</mapper>