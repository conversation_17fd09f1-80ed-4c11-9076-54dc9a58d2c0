package com.bqd.model.xhxrb;

import lombok.Data;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-09-30
 */
@Data
public class XhxRbZxztqk {
    private String workspaceName;
    private String systemName;
    private String testsetName;
    private Integer caseCount;
    private Integer executeCount;
    private Integer passedCount;
    private Integer failedCount;
    private Integer blockCount;
    private Integer executingCount;
    private Integer cancelCount;
    private Integer todoCount;
    private Double executeRate;
    private Double planProgress;
    private Double progressBias;
    private Double passRate;
    private Integer validBugCount;
    private Double totalBugRate;
    private Integer dayExecuteCount;
    private Integer dayPassedCount;
    private Integer dayFailedCount;
}
