<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbReplayPlanMapper">
    <resultMap id="esbReplayPlan" type="com.bqd.model.esbnetworkreplay.EsbReplayPlan">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="PLAN_NAME" property="planName" javaType="java.lang.String"/>
        <result column="STATUS" property="status" javaType="java.lang.Integer"/>
        <result column="CREATE_TIME" property="createTime" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO ESB_REPLAY_PLAN (ID, PLAN_NAME, STATUS, CREATE_TIME)
        VALUES (#{id}, #{planName}, #{status}, #{createTime})
    </insert>

    <select id="selectAll" resultMap="esbReplayPlan">
        SELECT *
        FROM ESB_REPLAY_PLAN
    </select>

    <delete id="deleteById">
        DELETE
        FROM ESB_REPLAY_PLAN
        WHERE ID = #{id}
    </delete>

</mapper>
