package com.bqd.serviceserveroperation.utils;

import cn.hutool.core.io.IoUtil;
import com.bqd.model.servermanagement.ServerInfo;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import org.springframework.core.io.ClassPathResource;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @Description: 服务器工具类
 * @Author: wangzirui
 * @CreateTime: 2024-12-05
 */
public class ServerUtils {

    /**
     * 构建命令
     *
     * @param commandList 命令列表
     * @return 构建后的命令
     */
    public static String buildCommand(List<String> commandList) {
        StringBuilder stringBuilder = new StringBuilder();
        for (String command : commandList) {
            stringBuilder.append(command).append(" && ");
        }
        return stringBuilder.substring(0, stringBuilder.length() - 4);
    }

    /**
     * 执行命令
     *
     * @param serverInfo 服务器信息
     * @param command    命令
     * @return 执行结果
     */
    public static String executeCommand(ServerInfo serverInfo, String command) {
        JSch jSch = new JSch();
        Session session;
        try {
            // 创建会话
            session = jSch.getSession(serverInfo.getUsername(), serverInfo.getServerIp(), 22);

            // 跳过 host key 检查（根据需要配置）
            session.setConfig("StrictHostKeyChecking", "no");

            //密码登录
            if (serverInfo.getAuthType().equals("pw")) {
                session.setPassword(serverInfo.getPw());
            }
            //私钥登录
            else {
                // 添加私钥
                ClassPathResource classPathResource = new ClassPathResource("files/sshKey/id_rsa");
                InputStream inputStream = classPathResource.getInputStream();
                byte[] bytes = IoUtil.readBytes(inputStream);
                jSch.addIdentity("id_rsa", bytes, null, null);
            }

            // 连接服务器
            session.connect();

            // 打开执行命令的通道
            ChannelExec channelExec = (ChannelExec) session.openChannel("exec");

            // 设置指令
            channelExec.setCommand(command);

            // 获取命令的输出流
            InputStream resultInputStream = channelExec.getInputStream();

            // 连接通道
            channelExec.connect();

            //获取输出
            return IoUtil.read(resultInputStream, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
