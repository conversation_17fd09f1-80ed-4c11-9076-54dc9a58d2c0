package com.bqd.serviceesbdata.controller;

import com.bqd.model.esbdata.EsbDbChangeReqtDto;
import com.bqd.model.esbdata.EsbDbChangeRespDto;
import com.bqd.model.esbdata.EsbFieldEnum;
import com.bqd.model.esbdata.EsbFieldEnumDto;
import com.bqd.base.response.Response;
import com.bqd.serviceesbdata.service.EsbDbChangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-25
 */
@RestController
@RequestMapping("/esbDbChange")
public class EsbDbChangeController {

    @Autowired
    private EsbDbChangeService esbDbChangeService;

    /**
     * 从生产数据库拉取报文到测试数据库
     *
     * @param interfaceId
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/insertToDbByIntfId")
    public Response insertToDbByIntfId(@RequestParam String interfaceId, @RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime) {
        esbDbChangeService.insertToDbByIntfId(interfaceId, startTime, endTime);
        return Response.success();
    }

    /**
     * 根据指定标签去重，获取枚举值和样例报文
     *
     * @param interfaceId
     * @param fieldPath
     * @return
     */
    @GetMapping("/deduplicate")
    public Response deduplicate(@RequestParam String interfaceId, @RequestParam String fieldPath) {
        esbDbChangeService.deduplicate(interfaceId, fieldPath);
        return Response.success();
    }

    /**
     * 获取指定接口拉取到测试数据库的报文数量
     *
     * @param interfaceId
     * @return
     */
    @GetMapping("/countInfoByInterfaceId")
    public Response countInfoByInterfaceId(@RequestParam String interfaceId) {
        return Response.success(esbDbChangeService.countInfoByInterfaceId(interfaceId));
    }

    /**
     * 根据接口ID删除拉取到测试数据库中的数据
     *
     * @param interfaceId
     * @return
     */
    @GetMapping("/deleteByInterfaceId")
    public Response deleteByInterfaceId(@RequestParam String interfaceId) {
        esbDbChangeService.deleteByInterfaceId(interfaceId);
        return Response.success();
    }

    /**
     * 根据接口ID获取去重枚举值字段列表
     *
     * @param interfaceId
     * @return
     */
    @GetMapping("/getFieldByInterfaceId")
    public Response getFieldByInterfaceId(@RequestParam String interfaceId) {
        return Response.success(esbDbChangeService.getFieldByInterfaceId(interfaceId));
    }

    /**
     * 分页查询完成去重的数据
     *
     * @param esbFieldEnumDto
     * @return
     */
    @PostMapping("/getDataPaged")
    public Response getDataPaged(@RequestBody EsbFieldEnumDto esbFieldEnumDto) {
        return Response.success(esbDbChangeService.pagedByCondition(esbFieldEnumDto));
    }

    /**
     * 根据查询条件删除已去重的数据
     *
     * @param esbFieldEnum
     * @return
     */
    @PostMapping("/deleteByCondition")
    public Response deleteByCondition(@RequestBody EsbFieldEnum esbFieldEnum) {
        esbDbChangeService.deleteByCondition(esbFieldEnum);
        return Response.success();
    }

    /**
     * 发送请求并比对
     *
     * @param esbDbChangeReqtDto
     * @return
     */
    @PostMapping("/sendAndCompare")
    public Response sendAndCompare(@RequestBody EsbDbChangeReqtDto esbDbChangeReqtDto) {
        EsbDbChangeRespDto esbDbChangeRespDto = esbDbChangeService.sendAndCompare(esbDbChangeReqtDto);
        return Response.success(esbDbChangeRespDto);
    }

}
