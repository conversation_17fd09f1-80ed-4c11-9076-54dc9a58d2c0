package com.bqd.model.transactionchain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WisdomServiceSearchDto {
    private String serviceGroup;
    private String description;
    private List<IntegrateServiceInfo> integrateServiceInfoList;
    private List<ESBInfo> esbInfoList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IntegrateServiceInfo {
        private String mvcId;
        private String description;
        private List<TradeNameInfo> tradeNameInfoList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradeNameInfo {
        private String tradeName;
        private String filePath;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ESBInfo {
        private String interfaceId;
        private String interfaceName;
    }
}
