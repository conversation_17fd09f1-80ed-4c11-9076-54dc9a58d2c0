package com.bqd.integratedtester.projectmanagement.controller;

import cn.hutool.core.util.ObjectUtil;
import com.bqd.base.response.Response;
import com.bqd.integratedtester.projectmanagement.service.GitService;
import com.bqd.model.projectmanagement.GitCredential;
import com.bqd.model.projectmanagement.GitRepoInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-09
 */
@RestController
@RequestMapping("/projectManagement/git/")
public class GitController {

    @Autowired
    private GitService gitService;

    /**
     * 获取所有git仓库信息，按更新时间排序
     *
     * @param gitRepoInfo
     */
    @PostMapping("/repositories")
    public Response repositories(@RequestBody(required = false) GitRepoInfo gitRepoInfo) {
        return Response.success(gitService.repositories(gitRepoInfo));
    }

    /**
     * 添加源码仓库
     *
     * @param gitRepoInfo
     */
    @PostMapping("/repo/add")
    public Response addRepository(@RequestBody GitRepoInfo gitRepoInfo) {
        gitService.addRepository(gitRepoInfo);
        return Response.success();
    }

    /**
     * 更新git仓库信息
     *
     * @param gitRepoInfo
     */
    @PostMapping("/repo/update")
    public Response updateGitRepoInfo(@RequestBody GitRepoInfo gitRepoInfo) {
        gitService.updateRepo(gitRepoInfo);
        return Response.success();
    }

    /**
     * 根据id删除仓库
     *
     * @param id
     */
    @GetMapping("/repo/delete/{id}")
    public Response deleteRepo(@PathVariable String id) {
        gitService.deleteRepo(id);
        return Response.success();
    }

    @GetMapping("/pullOrClone/{id}")
    public Response pullOrClone(@PathVariable("id") String id) {
        gitService.pullOrClone(id);
        return Response.success();
    }

    @PostMapping("/credentials")
    public Response credentials(@RequestBody(required = false) GitCredential gitCredential) {
        return Response.success(gitService.credentials(ObjectUtil.isNull(gitCredential) ? new GitCredential() : gitCredential));
    }

    @PostMapping("/credential/add")
    public Response addCredential(@RequestBody GitCredential gitCredential) {
        gitService.addCredential(gitCredential);
        return Response.success();
    }

    @GetMapping("/credential/delete/{id}")
    public Response deleteCredential(@PathVariable String id) {
        gitService.deleteCredential(id);
        return Response.success();
    }

    @PostMapping("/credential/update")
    public Response updateCredential(@RequestBody GitCredential gitCredential) {
        gitService.updateCredential(gitCredential);
        return Response.success();
    }

}
