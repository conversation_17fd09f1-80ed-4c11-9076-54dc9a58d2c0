package com.bqd.model.chaos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-01-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
    public class ChaosExecutingInfoDto extends ChaosExecutingInfo {
    /**
     * 开始时间（string格式）
     */
    private String startTimeString;

    /**
     * 预计结束时间（string格式）
     */
    private String estEndTimeString;

    /**
     * 剩余时间（秒）
     */
    private Integer secondsRemain;
}
