package com.bqd.integratedtester.financialstatement.service;

import java.util.Map;

public interface FinancialStatementService {

    /**
     * 上传文件
     * @param fileIndex
     * @param uploadId
     * @param fileStr
     */
    String upload(String fileIndex, String uploadId, String fileStr);

    /**
     * 清除已上传的文件
     * @param fileIndex
     */
    void cleanUpload(String uploadId, String fileIndex);

    /**
     * 清除已上传文件的缓存
     */
    void clearUploadCache();

    /**
     * 获取比对的报表类型
     * @return
     */
    Map<String, String> getCompareTypes();

    /**
     * 资金状况日报表比对
     * @param valueDiff
     * @return
     */
    String fundStatusCompare(String uploadId, int valueDiff);

    /**
     * 资产负债简报（人民银行口径）比对
     * @param valueDiff
     * @return
     */
    String balanceSheetComparePboc(String uploadId, int valueDiff);

    /**
     * 资产负债简报（银保监口径）比对
     * @param valueDiff
     * @return
     */
    String balanceSheetCompareBir(String uploadId, Integer valueDiff);

    /**
     * 资金状况日报表-全行 比对
     * @param uploadId
     * @param valueDiff
     * @return
     */
    String fundStatusAllCompare(String uploadId, Integer valueDiff);

    /**
     * 零售-存款日报表 比对
     * @param uploadId
     * @param valueDiff
     * @return
     */
    String retailDepositCompare(String uploadId, Integer valueDiff);

    /**
     * 零售-金融资产日报表 比对
     * @param uploadId
     * @param valueDiff
     * @return
     */
    String retailFinancialAssetCompare(String uploadId, Integer valueDiff);

    /**
     * 售客户月日均金融资产分层日报表 比对
     * @param uploadId
     * @param valueDiff
     * @return
     */
    String retailCustomerAvgAssetCompare(String uploadId, Integer valueDiff);

    /**
     * 个人贷款日报表 比对
     * @param uploadId
     * @param valueDiff
     * @return
     */
    String personalLoanCompare(String uploadId, Integer valueDiff);

    /**
     * 各分支机构日均存款情况表 比对
     * @param uploadId
     * @param valueDiff
     * @return
     */
    String branchDailyDepositsCompare(String uploadId, Integer valueDiff);

    /**
     * 指标变化明细日报表 比对
     * @param uploadId
     * @param valueDiff
     * @return
     */
    String indicatorChangeCompare(String uploadId, Integer valueDiff);

    /**
     * 信用风险监测日报表 比对
     * @param uploadId
     * @param valueDiff
     * @return
     */
    String creditRiskMonitoringCompare(String uploadId, Integer valueDiff);

    /**
     * 全行大客户列表/分支行大客户列表 比对
     * @param uploadId
     * @param valueDiff
     * @return
     */
    String majorCustomerCompare(String uploadId, Integer valueDiff);
}
