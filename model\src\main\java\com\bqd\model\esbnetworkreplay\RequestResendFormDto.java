package com.bqd.model.esbnetworkreplay;

import lombok.Data;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-17
 */
@Data
public class RequestResendFormDto {
    private String dbInfoId;
    private List<String> serviceProvider;
    private String startTime;
    private String endTime;
    private int timeInterval;
    private String sendHost;
    private int threadNum;
}
