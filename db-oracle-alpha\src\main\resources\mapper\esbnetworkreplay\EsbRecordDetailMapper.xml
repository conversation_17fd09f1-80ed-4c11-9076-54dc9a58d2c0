<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbRecordDetailMapper">
    <resultMap id="esbRecordDetail"
               type="com.bqd.model.esbnetworkreplay.EsbRecordDetail">
        <id column="RECORD_ID" property="recordId" javaType="java.lang.String"/>
        <result column="REQUEST_BODY" property="requestBody" javaType="java.lang.String"/>
        <result column="RESPONSE_BODY" property="responseBody" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectByIdAndColName" resultMap="esbRecordDetail">
        SELECT
        <foreach collection="colNameList" item="colName" separator=",">${colName}</foreach>
        FROM ESB_RECORD_DETAIL WHERE RECORD_ID = #{recordId}
    </select>

    <insert id="insert">
        INSERT INTO ESB_RECORD_DETAIL (RECORD_ID, REQUEST_BODY, RESPONSE_BODY)
        VALUES (#{recordId}, #{requestBody}, #{responseBody})
    </insert>

    <delete id="deleteById">
        DELETE
        FROM ESB_RECORD_DETAIL
        WHERE RECORD = #{recordId}
    </delete>

    <select id="selectById" resultMap="esbRecordDetail">
        SELECT *
        FROM ESB_RECORD_DETAIL
        WHERE RECORD_ID = #{recordId}
    </select>
</mapper>