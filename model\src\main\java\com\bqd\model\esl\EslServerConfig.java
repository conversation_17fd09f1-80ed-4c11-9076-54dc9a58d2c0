package com.bqd.model.esl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-06-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EslServerConfig {
    private String serverIp;
    private String serverName;
    private String systemName;
    private String updateTime;
}
