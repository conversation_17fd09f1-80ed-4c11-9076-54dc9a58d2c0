server:
  port: 26003
  servlet:
    context-path: /service/esb-data

spring:
  profiles:
    active: dev
  application:
    name: service-esb-data
  datasource:
    url: ***********************************
    username: db2inst2
    password: 123456
  cloud:
    nacos:
      server-addr: 10.238.145.164:8848
      discovery:
        username: nacos
        password: nacos

mybatis:
  mapper-locations: classpath:mapper/**/*.xml

esb-svn:
  # ESB调用列表SVN
  invocation-list:
    url: svn://10.1.81.22:3697/doc
    username: ifbk
    password: ifbk
    workspace: /home/<USER>/svn-repository/ESB-invocation-list
# ESB调用链路excel文件路径
esl:
  path:
    server-config: ""