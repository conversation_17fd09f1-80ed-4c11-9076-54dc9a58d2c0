<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbdata.mapper.EsbContentMapper">

    <resultMap id="esbContent" type="com.bqd.model.esbdata.EsbContent">
        <id column="INFO_ID" property="infoId"/>
        <result column="REQUEST_BODY" property="requestBody"/>
        <result column="RESPONSE_BODY" property="responseBody"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO TB_ESB_CONTENT
            (INFO_ID, REQUEST_BODY, RESPONSE_BODY)
        VALUES (#{infoId}, #{requestBody,jdbcType=VARCHAR}, #{responseBody,jdbcType=VARCHAR})
    </insert>

    <select id="selectByInterfaceId" resultMap="esbContent">
        SELECT *
        FROM TB_ESB_CONTENT
        WHERE INFO_ID IN (SELECT ID
                          FROM TB_ESB_INFO
                          WHERE INTERFACE_ID = #{interfaceId})
    </select>

    <delete id="deleteByInfoId">
        DELETE
        FROM TB_ESB_CONTENT
        WHERE INFO_ID = #{infoId}
    </delete>

</mapper>
