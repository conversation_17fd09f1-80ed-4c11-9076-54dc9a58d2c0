package com.bqd.serviceenr.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.bqd.model.esbnetworkreplay.EsbDeduplicateIgnoreDto;
import com.bqd.model.esbnetworkreplay.EsbCompareIgnore;
import com.bqd.base.rpc.esbnetworkreplay.EsbCompareIgnoreMapper;
import com.bqd.serviceenr.service.CompareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-05
 */
@Service
public class CompareServiceImpl implements CompareService {

    @Autowired
    private EsbCompareIgnoreMapper esbCompareIgnoreMapper;

    @Override
    public List<String> getIgnoredField(String interfaceId) {
        EsbCompareIgnore esbCompareIgnore = esbCompareIgnoreMapper.selectIgnoredField(interfaceId);
        if (ObjectUtil.isNull(esbCompareIgnore)) {
            return new ArrayList<>();
        }
        return StrUtil.split(esbCompareIgnore.getIgnoredField(), ",", true, true);
    }

    @Override
    public List<String> getIgnoredFieldGlobal() {
        return esbCompareIgnoreMapper.selectIgnoredFieldGlobal().stream().map(EsbCompareIgnore::getIgnoredField).filter(StrUtil::isNotBlank).collect(Collectors.toList());
    }

    @Override
    public void updateIgnoredField(EsbDeduplicateIgnoreDto esbDeduplicateIgnoreDto) {
        StringBuilder ignoredField = new StringBuilder();
        for (String field : esbDeduplicateIgnoreDto.getIgnoredFieldList()) {
            ignoredField.append(field).append(",");
        }
        EsbCompareIgnore esbCompareIgnore = esbCompareIgnoreMapper.selectIgnoredField(esbDeduplicateIgnoreDto.getInterfaceId());
        if (ObjectUtil.isNull(esbCompareIgnore)) {
            esbCompareIgnoreMapper.insert(new EsbCompareIgnore(IdUtil.fastSimpleUUID(), ignoredField.toString(), esbDeduplicateIgnoreDto.getInterfaceId()));
            return;
        }
        esbCompareIgnore.setIgnoredField(ignoredField.toString());
        esbCompareIgnoreMapper.updateById(esbCompareIgnore);
    }

    @Override
    public void addIgnoredFieldGlobal(String ignoredField) {
        esbCompareIgnoreMapper.insert(new EsbCompareIgnore(IdUtil.fastSimpleUUID(), ignoredField, "1"));
    }

    @Override
    public void deleteIgnoredFieldGlobal(String ignoredField) {
        esbCompareIgnoreMapper.deleteIgnoredFieldGlobal(ignoredField);
    }
}
