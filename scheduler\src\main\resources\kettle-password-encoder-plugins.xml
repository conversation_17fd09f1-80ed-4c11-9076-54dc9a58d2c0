<password-encoder-plugins>

  <password-encoder-plugin id="Kettle">
    <description>Kettle Password Encoder</description>
    <classname>org.pentaho.support.encryption.KettleTwoWayPasswordEncoder</classname>
  </password-encoder-plugin>
  <!--
  <password-encoder-plugin id="AES">
    <description>AES Password Encoder</description>
    <classname>org.pentaho.support.encryption.AESTwoWayPasswordEncoder</classname>
    <default-encoder>true</default-encoder>
  </password-encoder-plugin>
  -->

</password-encoder-plugins>