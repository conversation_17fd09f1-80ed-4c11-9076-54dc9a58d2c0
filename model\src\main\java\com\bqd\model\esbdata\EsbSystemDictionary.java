package com.bqd.model.esbdata;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-05-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsbSystemDictionary {

    /**
     * 系统代码
     */
    private String systemCode;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 系统简称
     */
    private String systemAbbr;

    /**
     * 更新时间
     */
    private String updateTime;
}
