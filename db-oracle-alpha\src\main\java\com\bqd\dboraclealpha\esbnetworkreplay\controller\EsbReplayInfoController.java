package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbReplayInfoMapper;
import com.bqd.model.esbnetworkreplay.EsbReplayInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/enr/esbReplayInfo")
public class EsbReplayInfoController {

    @Autowired
    private EsbReplayInfoMapper esbReplayInfoMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody EsbReplayInfo esbReplayInfo) {
        esbReplayInfoMapper.insert(esbReplayInfo);
    }

    @GetMapping("/selectByPlanId")
    public List<EsbReplayInfo> selectByPlanId(@RequestParam String replayPlanId) {
        return esbReplayInfoMapper.selectByPlanId(replayPlanId);
    }

    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String id) {
        esbReplayInfoMapper.deleteById(id);
    }

}
