package com.bqd.model.chaos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-01-13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChaosInjectionDto {
    private String serverInfoId;
    private String chaosType;
    private String chaosCommandPrefix;
    private Map<String, String> paramMap;
    private String targetInfo;
}
