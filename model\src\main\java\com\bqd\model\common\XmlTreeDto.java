package com.bqd.model.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-01-02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XmlTreeDto {
    private String tag;
    private String fullPath;
    private List<EntryDto<String, String>> attributeList;
    private List<XmlTreeDto> children;
}
