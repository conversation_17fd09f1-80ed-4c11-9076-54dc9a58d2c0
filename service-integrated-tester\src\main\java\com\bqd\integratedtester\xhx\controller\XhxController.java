package com.bqd.integratedtester.xhx.controller;

import cn.hutool.core.date.DateUtil;
import com.bqd.base.response.Response;
import com.bqd.integratedtester.xhx.service.XhxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Description: 新核心日报
 * @Author: wangzirui
 * @CreateTime: 2024-11-25
 */
@Slf4j
@RestController
@RequestMapping("/xhx")
public class XhxController {

    @Autowired
    private XhxService xhxService;

    @GetMapping("/dailyReport/runProcedure")
    public Response dailyReportRunProcedure(@RequestParam("startTime") String startTime,
                                            @RequestParam("endTime") String endTime,
                                            @RequestParam("roundName") String roundName,
                                            @RequestParam("systemPrefix") String systemPrefix) {
        xhxService.runProcedure(startTime, endTime, roundName, systemPrefix);
        return Response.success();
    }

    @GetMapping("/dailyReport/export")
    public void dailyReport(HttpServletResponse response) {
        String fileName = DateUtil.now().split(" ")[0];
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
        try (Workbook workbook = xhxService.dailyReport()) {
            workbook.write(response.getOutputStream());
            response.flushBuffer();
        } catch (IOException e) {
            log.error("导出失败", e);
        }
    }

}
