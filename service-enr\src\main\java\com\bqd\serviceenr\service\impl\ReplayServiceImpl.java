package com.bqd.serviceenr.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.bqd.base.rpc.esbnetworkreplay.*;
import com.bqd.model.esbnetworkreplay.*;
import com.bqd.serviceenr.service.ReplayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.text.StrSubstitutor;
import org.dom4j.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-08-21
 */
@Service
@Slf4j
public class ReplayServiceImpl implements ReplayService {

    @Autowired
    private EsbAssetDetailMapper esbAssetDetailMapper;
    @Autowired
    private EsbReplayPlanMapper esbReplayPlanMapper;
    @Autowired
    private EsbReplayInfoMapper esbReplayInfoMapper;
    @Autowired
    private EsbCompareMapper esbCompareMapper;
    @Autowired
    private EsbReplayDetailMapper esbReplayDetailMapper;
    @Autowired
    private EsbReplaceFieldMapper esbReplaceFieldMapper;
    @Autowired
    private EsbReplaceVariableMapper esbReplaceVariableMapper;
    @Autowired
    private EsbRecordInfoMapper esbRecordInfoMapper;
    @Autowired
    private EsbRecordDetailMapper esbRecordDetailMapper;

    @Override
    public void replay(ReplayReqtDto replayReqtDto) {
        //根据接口ID和版本号从资产库获取回放报文信息
        List<AssetDetailDto> assetDetailDtoList = esbAssetDetailMapper.selectByInterfaceIdAndVersionNumber(replayReqtDto.getInterfaceId(), replayReqtDto.getVersionNumber());
        //创建回放计划
        String planId = IdUtil.fastSimpleUUID();
        EsbReplayPlan esbReplayPlan = new EsbReplayPlan(planId, 0, DateUtil.now(), "计划名称");
        esbReplayPlanMapper.insert(esbReplayPlan);
        //遍历要回放的报文
        for (AssetDetailDto assetDetailDto : assetDetailDtoList) {
            ReplayDto replayDto = new ReplayDto();
            String infoId = IdUtil.fastSimpleUUID();
            replayDto.setEsbReplayPlan(esbReplayPlan);
            replayDto.setEsbReplayInfo(new EsbReplayInfo(infoId, planId, assetDetailDto.getUrl(), assetDetailDto.getInterfaceId(), 0, DateUtil.now(), null, assetDetailDto.getVersionNumber()));
            replayDto.setEsbReplayDetail(new EsbReplayDetail(infoId, replayReqtBodyHandler(assetDetailDto.getRequestBody(), assetDetailDto.getInterfaceId()), assetDetailDto.getResponseBody(), null));
        }
    }

    @Override
    public void replayInOrder(ReplayReqtDto replayReqtDto) {
        List<EsbRecordInfo> esbRecordInfoList = esbRecordInfoMapper.selectByConditionInOrder(replayReqtDto);
        //创建回放计划
        String planId = IdUtil.fastSimpleUUID();
        EsbReplayPlan esbReplayPlan = new EsbReplayPlan(planId, 0, DateUtil.now(), "顺序回放计划名称");
        esbReplayPlanMapper.insert(esbReplayPlan);
        for (EsbRecordInfo esbRecordInfo : esbRecordInfoList) {
            ReplayDto replayDto = new ReplayDto();
            String infoId = IdUtil.fastSimpleUUID();
            replayDto.setEsbReplayPlan(esbReplayPlan);
            replayDto.setEsbReplayInfo(new EsbReplayInfo(infoId, planId, esbRecordInfo.getHost(), esbRecordInfo.getInterfaceId(), 0, DateUtil.now(), null, esbRecordInfo.getVersionNumber()));
            EsbRecordDetail esbRecordDetail = esbRecordDetailMapper.selectByCondition(new EsbRecordDetail(esbRecordInfo.getRecordId(), null, null));
            replayDto.setEsbReplayDetail(new EsbReplayDetail(infoId, esbRecordDetail.getRequestBody(), esbRecordDetail.getResponseBody(), null));
        }
    }

    @Override
    public void replayByTime(String startTime, String endTime) {
        List<EsbRecordInfo> esbRecordInfoList = esbRecordInfoMapper.selectByTime(startTime, endTime);
        //创建回放计划
        String planId = IdUtil.fastSimpleUUID();
        EsbReplayPlan esbReplayPlan = new EsbReplayPlan(planId, 0, DateUtil.now(), "计划名称");
        esbReplayPlanMapper.insert(esbReplayPlan);
        for (EsbRecordInfo esbRecordInfo : esbRecordInfoList) {
            ReplayDto replayDto = new ReplayDto();
            String infoId = IdUtil.fastSimpleUUID();
            replayDto.setEsbReplayPlan(new EsbReplayPlan(planId, 0, DateUtil.now(), "按时间回放"));
            replayDto.setEsbReplayInfo(new EsbReplayInfo(infoId, planId, esbRecordInfo.getHost(), esbRecordInfo.getInterfaceId(), 0, DateUtil.now(), null, esbRecordInfo.getVersionNumber()));
            EsbRecordDetail esbRecordDetail = esbRecordDetailMapper.selectByCondition(new EsbRecordDetail(esbRecordInfo.getRecordId(), null, null));
            replayDto.setEsbReplayDetail(new EsbReplayDetail(infoId, esbRecordDetail.getRequestBody(), esbRecordDetail.getResponseBody(), null));
        }
    }

    private String replayReqtBodyHandler(String reqtBody, String interfaceId) {
        Document document;
        try {
            document = DocumentHelper.parseText(reqtBody);
        } catch (DocumentException e) {
            throw new RuntimeException(e);
        }
        List<EsbReplaceField> esbReplaceFieldList = esbReplaceFieldMapper.selectByInterfaceId(interfaceId);
        esbReplaceFieldList.forEach(esbReplaceField -> {
            String xpath = "/" + esbReplaceField.getTag().replace(".", "/");
            Node node = document.selectSingleNode(xpath);
            if (node instanceof Element) {
                node.setText(esbReplaceField.getValue());
            }
        });
        Map<String, String> map = new HashMap<>();
        map.put("tmStamp", DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss"));
        map.put("csmrSerNbr", IdUtil.fastSimpleUUID());
        List<EsbReplaceVariable> esbReplaceVariableList = esbReplaceVariableMapper.selectAll();
        for (EsbReplaceVariable esbReplaceVariable : esbReplaceVariableList) {
            map.put(esbReplaceVariable.getName(), esbReplaceVariable.getValue());
        }
        StrSubstitutor strSubstitutor = new StrSubstitutor(map);
        return strSubstitutor.replace(document.asXML());
    }

    @Override
    public List<EsbReplayPlan> getReplayPlan() {
        return esbReplayPlanMapper.selectAll();
    }

    @Override
    public List<EsbReplayInfo> getReplayInfo(String replayPlanId) {
        return esbReplayInfoMapper.selectByPlanId(replayPlanId);
    }

    @Override
    public void deleteReplayPlan(String replayPlanId) {
        List<String> infoIdList = esbReplayInfoMapper.selectByPlanId(replayPlanId).stream().map(EsbReplayInfo::getId).collect(Collectors.toList());
        for (String infoId : infoIdList) {
            esbReplayInfoMapper.deleteById(infoId);
            esbReplayDetailMapper.deleteByInfoId(infoId);
        }
        esbCompareMapper.deleteByPlanId(replayPlanId);
        esbReplayPlanMapper.deleteById(replayPlanId);
    }

    @Override
    public EsbReplayDetail getReplayDetailFieldValue(String infoId, String field) {
        return esbReplayDetailMapper.selectFieldValueByInfoId(infoId, field);
    }

}
