package com.bqd.dboraclealpha.esbnetworkreplay.mapper;

import com.bqd.model.esbnetworkreplay.*;
import com.bqd.model.esbnetworkreplay.EsbRecordInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EsbRecordInfoMapper {
    int countByCondition(EsbRecordInfoDto recordInfoDto);

    List<EsbRecordInfo> selectByCondition(@Param("startRow") Integer startRow, @Param("endRow") Integer endRow, @Param("recordInfoDto") EsbRecordInfoDto recordInfoDto);

    List<EsbRecordInfo> selectDistinctByColName(@Param("colNameList") List<String> colNameList);

    void deleteById(String recordId);

    void insert(EsbRecordInfo esbRecordInfo);

    List<EsbRecordInfoDetailDto> selectInfoDetailByCondition(EsbRecordInfoDto esbRecordInfoDto);

}
