package com.bqd.model.esbnetworkreplay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-08-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsbAssetInfo {
    private String assetId;
    private String host;
    private String interfaceId;
    private Long requestTime;
    private Long responseTime;
    private String svcCorrId;
    private String channelId;
    private String transCde;
    private String csmrId;
    private String versionNumber;
    private String esbRespMsg;
    private String esbRespCode;
    private String providerId;
}
