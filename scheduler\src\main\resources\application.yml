server:
  port: 27000
  servlet:
    context-path: /scheduler

spring:
  profiles:
    active: dev
  application:
    name: scheduler
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  cloud:
    nacos:
      server-addr: **************:8848
      discovery:
        username: nacos
        password: nacos
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************
    username: quartz
    password: quartz
  quartz:
    job-store-type: jdbc #任务保存到数据库
