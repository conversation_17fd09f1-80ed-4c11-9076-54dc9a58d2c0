package com.bqd.serviceesbdata.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.bqd.base.rpc.esbdata.EsbServiceModelMapper;
import com.bqd.base.rpc.esbdata.EsbSystemDictionaryMapper;
import com.bqd.base.rpc.esbdata.EslServerConfigMapper;
import com.bqd.base.rpc.esbdata.EslSystemLinkMapper;
import com.bqd.model.common.TreeDto;
import com.bqd.model.esbdata.EsbServiceModel;
import com.bqd.model.esbdata.EsbSystemDictionary;
import com.bqd.model.esl.EslServerConfig;
import com.bqd.model.esl.EslSystemLink;
import com.bqd.serviceesbdata.service.EsbSystemLinkService;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-01-06
 */
@Service
public class EsbSystemLinkServiceImpl implements EsbSystemLinkService {

    @Value("${esb-svn.invocation-list.workspace}")
    private String svnILWorkspace;
    private final String serviceModelExcelName = "BQD_SOA二期_服务模型(All)_V0.1_2014.05.13.xlsx";
    @Value("${esl.path.server-config}")
    private String eslServerConfigPath;

    @Autowired
    private EsbSystemDictionaryMapper esbSystemDictionaryMapper;
    @Autowired
    private EsbServiceModelMapper esbServiceModelMapper;
    @Autowired
    private EslSystemLinkMapper eslSystemLinkMapper;
    @Autowired
    private EslServerConfigMapper eslServerConfigMapper;

    @Override
    public void parseEsbServiceModel() {
        File file = new File(svnILWorkspace + File.separator + serviceModelExcelName);
        try (Workbook workbook = WorkbookFactory.create(file)) {
            Sheet sheet = workbook.getSheetAt(1);
            List<EsbServiceModel> esbServiceModelList = new ArrayList<>();
            // 遍历单元格
            row:
            for (Row row : sheet) {
                EsbServiceModel esbServiceModel = new EsbServiceModel();
                for (Cell cell : row) {
                    int columnIndex = cell.getColumnIndex();
                    //跳过第0列及大于13的列
                    if (columnIndex == 0 || columnIndex > 13) {
                        continue;
                    }

                    //读取interfaceId
                    if (columnIndex == 1) {
                        if (workbook.getFontAt(cell.getCellStyle().getFontIndexAsInt()).getStrikeout()) {
                            continue row;
                        }
                        if (NumberUtil.isNumber(cell.toString()) && StrUtil.length(cell.toString()) == 6) {
                            esbServiceModel.setInterfaceId(cell.toString());
                            continue;
                        }
                        continue row;
                    }

                    //读取接口名称
                    if (columnIndex == 3) {
                        esbServiceModel.setInterfaceName(cell.toString());
                    }

                    //读取主题域
                    if (columnIndex == 4) {
                        esbServiceModel.setSubjectDomain(cell.toString());
                    }

                    //读取发布范围
                    if (columnIndex == 10) {
                        esbServiceModel.setReleaseRange(cell.toString());
                    }

                    //读取服务提供方
                    if (columnIndex == 13) {
                        esbServiceModel.setServiceProvider(cell.toString().replace("（", "(").replace("）", ")"));
                    }

                }
                esbServiceModel.setParseTime(DateUtil.now());
                if (StrUtil.isNotBlank(esbServiceModel.getInterfaceId())) {
                    //不解析通知类、综合管理和营销管理的接口
                    if (StrUtil.contains(esbServiceModel.getInterfaceName(), "通知")
                            && StrUtil.equalsAny(esbServiceModel.getSubjectDomain(), "09-综合管理", "10-营销管理")) {
                        continue;
                    }
                    esbServiceModelList.add(esbServiceModel);
                }
            }
            esbServiceModelMapper.batchInsert(esbServiceModelList);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static String extractServiceProviderAbbr(String str) {
        // 正则表达式匹配括号中的内容
        String regex = "\\(([^)]*)\\)";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(str);

        String lastMatch = null;
        // 遍历所有匹配，保存最后一个匹配的内容
        while (matcher.find()) {
            lastMatch = matcher.group(1);
        }

        return lastMatch;
    }

    @Override
    public void parseSystemLink() {
        List<String> serviceProviderList = esbServiceModelMapper.selectAllServiceProvider();
        serviceProviderList.forEach(serviceProvider -> {
            Set<String> releaseRangeSet = new HashSet<>();
            List<String> releaseRangeList = esbServiceModelMapper.selectReleaseRangeByServiceProvider(serviceProvider);
            releaseRangeList.forEach(releaseRanges -> {
                StrUtil.split(releaseRanges, StrUtil.SLASH).forEach(releaseRange -> {
                    if (StrUtil.isNotBlank(releaseRange)) {
                        releaseRangeSet.add(releaseRange);
                    }
                });
            });
            List<EslSystemLink> eslSystemLinkList = releaseRangeSet.stream().map(releaseRange -> new EslSystemLink(extractServiceProviderAbbr(serviceProvider), releaseRange, DateUtil.now())).collect(Collectors.toList());
            if (!eslSystemLinkList.isEmpty()) {
                eslSystemLinkMapper.batchInsert(eslSystemLinkList);
            }
        });
    }

    @Override
    public void parseEslServerConfig() {
        File file = new File(eslServerConfigPath);
        try (Workbook workbook = WorkbookFactory.create(file)) {
            Sheet sheet = workbook.getSheet("配置");
            for (Row row : sheet) {
                if (row.getRowNum() == 0){
                    continue;
                }
                String ip = null, name = null, system = null;
                for (Cell cell : row) {
                    if (cell.getColumnIndex() == 1) {
                        ip = cell.toString();
                    }
                    if (cell.getColumnIndex() == 3) {
                        name = cell.toString();
                    }
                    if (cell.getColumnIndex() == 4) {
                        system = cell.toString();
                    }
                }
                if (StrUtil.isAllNotEmpty(ip, name, system)) {
                    EslServerConfig eslServerConfig = new EslServerConfig(ip, name, system, DateUtil.now());
                    eslServerConfigMapper.insert(eslServerConfig);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TreeDto> getConfigList() {
        TreeDto children11 = new TreeDto("1-1", "网络1-1", null);
        TreeDto children12 = new TreeDto("1-2", "网络1-2", null);
        List<TreeDto> children1List = new ArrayList<>();
        children1List.add(children11);
        children1List.add(children12);
        TreeDto children1 = new TreeDto("1", "网络", children1List);

        TreeDto children21 = new TreeDto("2-1", "存储2-1", null);
        TreeDto children22 = new TreeDto("2-2", "存储2-2", null);
        TreeDto children23 = new TreeDto("2-3", "存储2-3", null);
        List<TreeDto> children2List = new ArrayList<>();
        children2List.add(children21);
        children2List.add(children22);
        children2List.add(children23);
        TreeDto children2 = new TreeDto("2", "存储", children2List);

        List<EslServerConfig> eslServerConfigList = eslServerConfigMapper.selectIpAndServerName();
        List<TreeDto> children3List = eslServerConfigList.stream().map(eslServerConfig -> new TreeDto(eslServerConfig.getServerIp(), eslServerConfig.getServerIp() + "-" + eslServerConfig.getServerName(), null)).collect(Collectors.toList());
        TreeDto children3 = new TreeDto("3", "服务器IP", children3List);

        List<TreeDto> root = new ArrayList<>();
        root.add(children1);
        root.add(children2);
        root.add(children3);
        return root;
    }

    @Override
    public List<TreeDto> getESLTreeListByConfigId(String configId) {
        List<EslServerConfig> eslServerConfigList = eslServerConfigMapper.selectByIp(configId);
        if (eslServerConfigList.isEmpty()) {
            List<EslSystemLink> eslSystemLinkList = eslSystemLinkMapper.selectAll();
            Collections.shuffle(eslSystemLinkList);
            return ListUtil.sub(eslSystemLinkList, 0, 10)
                    .stream()
                    .map(EslSystemLink::getServiceProvider)
                    .distinct()
                    .map(serviceProvider -> new TreeDto(serviceProvider, getSysDictByServiceProviderAbbr(serviceProvider).getSystemName(), generateTransInfo()))
                    .collect(Collectors.toList());
        }
        return eslServerConfigList.stream().map(eslServerConfig -> {
            EsbSystemDictionary esbSystemDictionary = esbSystemDictionaryMapper.selectBySystemName(eslServerConfig.getSystemName());
            if (ObjectUtil.isNull(esbSystemDictionary)){
                return new TreeDto(eslServerConfig.getSystemName(), eslServerConfig.getSystemName(), generateTransInfo());
            }
            return new TreeDto(esbSystemDictionary.getSystemAbbr(), eslServerConfig.getSystemName(), generateTransInfo());
        }).collect(Collectors.toList());
    }

    public EsbSystemDictionary getSysDictByServiceProviderAbbr(String serviceProviderAbbr) {
        if (serviceProviderAbbr.contains("MBB")){
            serviceProviderAbbr = "MBB";
        }
        EsbSystemDictionary esbSystemDictionary = esbSystemDictionaryMapper.selectByServiceProviderAbbr(serviceProviderAbbr);
        if (ObjectUtil.isNull(esbSystemDictionary)){
            esbSystemDictionary = new EsbSystemDictionary(null, serviceProviderAbbr, serviceProviderAbbr, null);
        }
        return esbSystemDictionary;
    }

    @Override
    public List<TreeDto> getESBInterfaceList() {
        List<EsbServiceModel> esbInterfaceList = esbServiceModelMapper.selectInterfaceIdAndName();
        return esbInterfaceList.stream().map(eslEsbServiceModel -> {
            TreeDto treeDto = new TreeDto();
            treeDto.setId(eslEsbServiceModel.getInterfaceId());
            treeDto.setLabel(eslEsbServiceModel.getInterfaceId() + " - " + eslEsbServiceModel.getInterfaceName());
            return treeDto;
        }).collect(Collectors.toList());
    }

    @Override
    public TreeDto getESLTreeListByInterfaceId(String interfaceId) {
        String releaseRanges = esbServiceModelMapper.selectReleaseRangeByInterfaceId(interfaceId);
        List<TreeDto> releaseRangeList = new ArrayList<>();
        StrUtil.split(releaseRanges, StrUtil.SLASH).forEach(releaseRange -> {
            if (StrUtil.isNotBlank(releaseRange)) {
                releaseRangeList.add(new TreeDto(releaseRange, getSysDictByServiceProviderAbbr(releaseRange).getSystemName(), generateTransInfo()));
            }
        });
        EsbServiceModel esbServiceModel = esbServiceModelMapper.selectByInterfaceId(interfaceId);
        return new TreeDto("serivceProvider", esbServiceModel.getServiceProvider(), releaseRangeList);
    }

    @Override
    public List<TreeDto> getESLTreeListByServiceProvider(String serviceProvider) {
        List<String> releaseRangeList = eslSystemLinkMapper.selectReleaseRangeByServiceProvider(serviceProvider);
        List<TreeDto> treeDtoList = new ArrayList<>();
        releaseRangeList.forEach(releaseRange -> treeDtoList.add(new TreeDto(releaseRange, getSysDictByServiceProviderAbbr(releaseRange).getSystemName(), generateTransInfo())));
        return treeDtoList;
    }

    private List<TreeDto> generateTransInfo() {
        List<TreeDto> transInfo = new ArrayList<>();
        String s1 = "交易" + RandomUtil.randomString(6);
        String s2 = "交易" + RandomUtil.randomString(6);
        String s3 = "交易" + RandomUtil.randomString(6);
        transInfo.add(new TreeDto(s1, s1, null));
        transInfo.add(new TreeDto(s2, s2, null));
        transInfo.add(new TreeDto(s3, s3, null));
        return transInfo;
    }
}