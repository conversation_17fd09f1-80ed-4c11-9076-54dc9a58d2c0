package com.bqd.scheduler.jobs;

import com.bqd.scheduler.service.KettleSchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-01-27
 */
@Component
@Slf4j
public class XhxKettleJob implements Job {

    @Autowired
    private KettleSchedulerService kettleSchedulerService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("【定时任务-开始】新核心日报抽数任务");
        kettleSchedulerService.xhxRb();
        log.info("【定时任务-完成】新核心日报抽数任务");
    }
}
