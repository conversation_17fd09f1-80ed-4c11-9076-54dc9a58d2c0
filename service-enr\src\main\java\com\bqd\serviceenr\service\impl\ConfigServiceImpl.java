package com.bqd.serviceenr.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.bqd.model.esbnetworkreplay.RequestResendFormDto;
import com.bqd.model.esbnetworkreplay.RecordDBEnv;
import com.bqd.model.esbnetworkreplay.RequestResendDBInfo;
import com.bqd.base.rpc.esbnetworkreplay.RecordDBEnvMapper;
import com.bqd.base.rpc.esbnetworkreplay.RequestResendDBInfoMapper;
import com.bqd.serviceenr.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-07-02
 */
@Service
@Slf4j
public class ConfigServiceImpl implements ConfigService {

    @Autowired
    private RecordDBEnvMapper recordDBEnvMapper;
    @Autowired
    private RequestResendDBInfoMapper requestResendDBInfoMapper;

    @Override
    public List<RecordDBEnv> getRecordDBEnv() {
        return recordDBEnvMapper.selectAllOrderByCreateTimeDESC();
    }

    @Override
    public void addRecordDBEnv(RecordDBEnv recordDBEnv) {
        recordDBEnv.setId(UUID.randomUUID().toString());
        recordDBEnv.setCreateTime(DateUtil.now());
        recordDBEnvMapper.insert(recordDBEnv);
    }

    @Override
    public void updateRecordDBEnv(RecordDBEnv recordDBEnv) {
        recordDBEnvMapper.updateById(recordDBEnv);
    }

    @Override
    public void deleteRecordDBEnv(String id) {
        recordDBEnvMapper.deleteById(id);
    }

    @Override
    public List<RequestResendDBInfo> getRequestResendDBList() {
        return requestResendDBInfoMapper.selectAllOdrByTimeDesc();
    }

    @Override
    public void addRequestResendDBInfo(RequestResendDBInfo requestResendDBInfo) {
        requestResendDBInfo.setId(IdUtil.fastSimpleUUID());
        requestResendDBInfo.setInsertTime(DateUtil.now());
        requestResendDBInfoMapper.insert(requestResendDBInfo);
    }

    @Override
    public void updateRequestResendDBInfo(RequestResendDBInfo requestResendDBInfo) {
        requestResendDBInfoMapper.updateById(requestResendDBInfo);
    }

    @Override
    public void deleteRequestResendDBEnv(String id) {
        requestResendDBInfoMapper.deleteById(id);
    }

    @Override
    public boolean testDBConnection(String id) {
        RequestResendDBInfo requestResendDBInfo = requestResendDBInfoMapper.selectById(id);
        try {
            Connection connection = DriverManager.getConnection(requestResendDBInfo.getConnectionUrl(), requestResendDBInfo.getUserName(), requestResendDBInfo.getPassword());
            return ObjectUtil.isNotNull(connection);
        } catch (Exception e) {
            log.error("Error", e);
            return false;
        }
    }

    @Override
    public void requestResend(RequestResendFormDto requestResendFormDto) {
        //获取数据库信息
        RequestResendDBInfo requestResendDBInfo = requestResendDBInfoMapper.selectById(requestResendFormDto.getDbInfoId());
        try {
            //获取数据库连接
            Connection connection = DriverManager.getConnection(requestResendDBInfo.getConnectionUrl(), requestResendDBInfo.getUserName(), requestResendDBInfo.getPassword());
            //todo
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
}
