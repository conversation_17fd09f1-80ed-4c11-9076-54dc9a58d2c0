package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbReplaceFieldMapper;
import com.bqd.model.esbnetworkreplay.EsbReplaceField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/enr/esbReplaceField")
public class EsbReplaceFieldController {

    @Autowired
    private EsbReplaceFieldMapper esbReplaceFieldMapper;

    @GetMapping("/selectByInterfaceId")
    List<EsbReplaceField> selectByInterfaceId(@RequestParam String interfaceId) {
        return esbReplaceFieldMapper.selectByInterfaceId(interfaceId);
    }

    @GetMapping("/deleteById")
    void deleteById(@RequestParam String id) {
        esbReplaceFieldMapper.deleteById(id);
    }

    @PostMapping("/insert")
    void insert(@RequestBody EsbReplaceField esbReplaceField) {
        esbReplaceFieldMapper.insert(esbReplaceField);
    }

}
