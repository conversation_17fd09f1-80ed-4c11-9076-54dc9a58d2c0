<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbDeduplicateIgnoreGlobalMapper">

    <resultMap id="esbDeduplicateIgnoreGlobal" type="com.bqd.model.esbnetworkreplay.EsbDeduplicateIgnoreGlobal">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="IGNORED_FIELD" property="ignoredField" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO ESB_DEDUPLICATE_IGNORE_GLOBAL (ID, IGNORED_FIELD)
        VALUES (#{id}, #{ignoredField})
    </insert>

    <select id="selectByCondition" resultMap="esbDeduplicateIgnoreGlobal">
        SELECT *
        FROM ESB_DEDUPLICATE_IGNORE_GLOBAL
        WHERE 1 = 1
        <if test="id != null and id.length() != 0">
            AND ID = #{id}
        </if>
        <if test="ignoredField != null and ignoredField.length() != 0">
            AND IGNORED_FIELD = #{ignoredField}
        </if>
    </select>

    <delete id="deleteByCondition">
        DELETE FROM ESB_DEDUPLICATE_IGNORE_GLOBAL WHERE 1 = 1
        <if test="id != null and id.length() != 0">
            AND ID = #{id}
        </if>
        <if test="ignoredField != null and ignoredField.length() != 0">
            AND IGNORED_FIELD = #{ignoredField}
        </if>
    </delete>

</mapper>