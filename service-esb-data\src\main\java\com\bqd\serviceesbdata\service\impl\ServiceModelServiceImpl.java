package com.bqd.serviceesbdata.service.impl;

import com.bqd.base.rpc.esbdata.EsbServiceModelMapper;
import com.bqd.model.esbdata.EsbServiceModel;
import com.bqd.serviceesbdata.service.ServiceModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-13
 */
@Service
public class ServiceModelServiceImpl implements ServiceModelService {

    @Autowired
    private EsbServiceModelMapper esbServiceModelMapper;

    @Override
    public EsbServiceModel getByInterfaceId(String interfaceId) {
        return esbServiceModelMapper.selectByInterfaceId(interfaceId);
    }

    @Override
    public List<EsbServiceModel> getServiceModelFiledValue(List<String> fieldList) {
        return esbServiceModelMapper.selectFieldValue(fieldList);
    }

}
