package com.bqd.dboraclebeta.xhx.mapper;

import com.bqd.model.xhxrb.XhxRbZxztqk;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface XhxRbZxztqkMapper {
    List<XhxRbZxztqk> selectAllDesc();

    void pcdInsertNames();
    void pcdInsertTestsetTotal();

    void pcdUpdateCaseCount();
    void pcdUpdateExecuteCount();
    void pcdUpdatePassedCount();
    void pcdUpdateFailedCount();
    void pcdUpdateBlockCount();
    void pcdUpdateExecutingCount();
    void pcdUpdateCancelCount();
    void pcdUpdateTodoCount();
    void pcdUpdateExecuteRate();
    void pcdUpdatePassRate();

    void pcdUpdateDayExecuteCount(@Param("startTime") String startTime, @Param("endTime") String endTime);
    void pcdUpdateDayPassedCount(@Param("startTime") String startTime, @Param("endTime")String endTime);
    void pcdUpdateDayFailedCount(@Param("startTime") String startTime, @Param("endTime")String endTime);

    void pcdUpdatePlanProgress(String roundName);
    void pcdUpdateProgressBias();

    void pcdUpdateValidBugCount(@Param("roundName") String roundName, @Param("systemPrefix") String systemPrefix);
    void pcdUpdateTotalBugRate();
}
