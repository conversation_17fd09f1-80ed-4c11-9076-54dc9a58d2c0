package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbCompareMapper;
import com.bqd.model.esbnetworkreplay.EsbCompare;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/enr/esbCompare")
public class EsbCompareController {

    @Autowired
    private EsbCompareMapper esbCompareMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody EsbCompare esbCompare) {
        esbCompareMapper.insert(esbCompare);
    }

    @GetMapping("/deleteByPlanId")
    public void deleteByPlanId(@RequestParam String replayPlanId) {
        esbCompareMapper.deleteByPlanId(replayPlanId);
    }

}
