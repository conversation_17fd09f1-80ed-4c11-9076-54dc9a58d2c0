package com.bqd.dboraclealpha.transactionchain.controller;

import com.bqd.dboraclealpha.transactionchain.mapper.ZtServiceBlMapper;
import com.bqd.model.transactionchain.ZtServiceBl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
@RestController
@RequestMapping("/transactionChain/zt/bl")
public class ZtServiceBlController implements com.bqd.base.rpc.transactionchain.ZtServiceBlMapper {

    @Autowired
    private ZtServiceBlMapper ztServiceBlMapper;

    @Override
    @PostMapping("/insert")
    public void insert(@RequestBody ZtServiceBl serviceBl) {
        ztServiceBlMapper.insert(serviceBl);
    }

    @Override
    @GetMapping("/selectByBeanRefAndServiceName")
    public List<ZtServiceBl> selectByBeanRefAndServiceName(@RequestParam String beanRef, @RequestParam String serviceName) {
        return ztServiceBlMapper.selectByBeanRefAndServiceName(beanRef, serviceName);
    }

    @Override
    @GetMapping("/selectByLikeTrxCode")
    public List<ZtServiceBl> selectByLikeTrxCode(@RequestParam String interfaceId) {
        return ztServiceBlMapper.selectByLikeTrxCode(interfaceId);
    }
}
