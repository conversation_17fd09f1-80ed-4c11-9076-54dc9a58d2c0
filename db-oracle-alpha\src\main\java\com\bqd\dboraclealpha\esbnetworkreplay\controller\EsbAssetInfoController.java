package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbAssetInfoMapper;
import com.bqd.model.esbnetworkreplay.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/enr/esbAssetInfo")
public class EsbAssetInfoController {

    @Autowired
    private EsbAssetInfoMapper esbAssetInfoMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody EsbAssetInfo assetInfo) {
        esbAssetInfoMapper.insert(assetInfo);
    }

    @PostMapping("/selectInfoDetailByCondition")
    public List<EsbAssetInfoDetailDto> selectInfoDetailByCondition(@RequestBody EsbAssetInfoDto esbAssetInfoDto) {
        return esbAssetInfoMapper.selectInfoDetailByCondition(esbAssetInfoDto);
    }

    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String assetId) {
        esbAssetInfoMapper.deleteById(assetId);
    }

    @GetMapping("/selectFieldValue")
    public List<EsbAssetInfoDto> selectFieldValue(@RequestParam List<String> fieldList) {
        return esbAssetInfoMapper.selectFieldValue(fieldList);
    }

    @GetMapping("/selectFieldValueByInterfaceId")
    public List<String> selectFieldValueByInterfaceId(@RequestParam("interfaceId") String interfaceId, @RequestParam("field") String field) {
        return esbAssetInfoMapper.selectFieldValueByInterfaceId(interfaceId, field);
    }

    @PostMapping("/countByCondition")
    public int countByCondition(@RequestBody AssetInfoSearchDto assetInfoSearchDto) {
        return esbAssetInfoMapper.countByCondition(assetInfoSearchDto);
    }

    @PostMapping("/selectPagedByCondition")
    public List<EsbAssetInfoDto> selectPagedByCondition(@RequestBody AssetInfoSearchDto assetInfoSearchDto) {
        return esbAssetInfoMapper.selectPagedByCondition(assetInfoSearchDto);
    }

    @PostMapping("/countInfoDetailByCondition")
    public int countInfoDetailByCondition(@RequestBody AssetInfoDetailSearchDto assetInfoDetailSearchDto) {
        return esbAssetInfoMapper.countInfoDetailByCondition(assetInfoDetailSearchDto);
    }

    @PostMapping("/selectInfoDetailPagedByCondition")
    public List<EsbAssetInfo> selectInfoDetailPagedByCondition(@RequestBody AssetInfoDetailSearchDto assetInfoDetailSearchDto) {
        return esbAssetInfoMapper.selectInfoDetailPagedByCondition(assetInfoDetailSearchDto);
    }

}
