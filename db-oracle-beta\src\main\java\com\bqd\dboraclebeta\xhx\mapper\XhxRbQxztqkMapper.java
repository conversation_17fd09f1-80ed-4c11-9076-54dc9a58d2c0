package com.bqd.dboraclebeta.xhx.mapper;

import com.bqd.model.xhxrb.XhxRbQxztqk;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface XhxRbQxztqkMapper {
    List<XhxRbQxztqk> selectAll();

    void pcdInsertSystemName(String roundName);

    void pcdUpdateCount(@Param("roundName") String roundName, @Param("zt") String zt, @Param("updateColumn") String updateColumn);

    void pcdUpdateTotalCount(String roundName);
}
