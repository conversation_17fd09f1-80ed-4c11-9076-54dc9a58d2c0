package com.bqd.integratedtester.projectmanagement.service;

import com.bqd.model.projectmanagement.GitCredential;
import com.bqd.model.projectmanagement.GitRepoInfo;

import java.util.List;

public interface GitService {
    List<GitRepoInfo> repositories(GitRepoInfo gitRepoInfo);

    void addRepository(GitRepoInfo gitRepoInfo);

    void updateRepo(GitRepoInfo gitRepoInfo);

    void deleteRepo(String id);

    void pullOrClone(String id);

    List<GitCredential> credentials(GitCredential gitCredential);

    void addCredential(GitCredential gitCredential);

    void deleteCredential(String id);

    void updateCredential(GitCredential gitCredential);
}
