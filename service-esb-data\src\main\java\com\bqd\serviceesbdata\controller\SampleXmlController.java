package com.bqd.serviceesbdata.controller;

import com.bqd.model.common.TreeDto;
import com.bqd.model.samplexml.dto.SampleXmlDto;
import com.bqd.base.response.Response;
import com.bqd.serviceesbdata.service.SampleXmlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-27
 */
@RestController
@RequestMapping("/sampleXml")
public class SampleXmlController {

    @Autowired
    private SampleXmlService sampleXmlService;

    @GetMapping("/downloadSampleXmlFromRemote")
    public Response downloadSampleXmlFromRemote(@RequestParam("interfaceId") String interfaceId) {
        return Response.success(sampleXmlService.downloadSampleXmlFromRemote(interfaceId));
    }

    @PostMapping("/getValueByCondition")
    public Response getValueByCondition(@RequestBody SampleXmlDto sampleXmlDto) {
        return Response.success(sampleXmlService.getValueByCondition(sampleXmlDto));
    }

    @PostMapping("/getSampleXml")
    public Response getSampleXml(@RequestBody SampleXmlDto sampleXmlDto) {
        return Response.success(sampleXmlService.getSampleXml(sampleXmlDto.getInterfaceId(), sampleXmlDto.getName()));
    }

    @PostMapping("/updateSampleXmlContent")
    public Response updateSampleXmlContent(@RequestBody SampleXmlDto sampleXmlDto) {
        sampleXmlService.updateSampleXmlContent(sampleXmlDto);
        return Response.success();
    }

    @PostMapping("/getFieldTree")
    public Response getFieldTree(@RequestBody SampleXmlDto sampleXmlDto) {
        ArrayList<TreeDto> returnList = new ArrayList<>();
        returnList.add(sampleXmlService.getFieldTree(sampleXmlDto.getInterfaceId(), sampleXmlDto.getName()));
        return Response.success(returnList);
    }

}
