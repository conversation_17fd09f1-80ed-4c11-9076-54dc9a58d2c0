server:
  port: 28010
  servlet:
    context-path: /db/redis-alpha

spring:
  profiles:
    active: dev
  application:
    name: db-redis-alpha
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
        username: nacos
        password: nacos
  redis:
    host: **************
    port: 6379
    password: Itdpt@2013
    database: 0
    jedis:
      pool:
        max-active: 1024
        max-idle: 10
        min-idle: 0
