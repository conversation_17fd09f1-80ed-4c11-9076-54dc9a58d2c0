package com.bqd.model.esbnetworkreplay;

import com.bqd.model.common.OraclePagedCommonParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 指定接口 录制信息详情页 数据dto
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-08-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordInfoDetailSearchReqtDto extends OraclePagedCommonParam {
    private String interfaceId;
    private String channelId;
    private String transCde;
    private String csmrId;
    private String versionNumber;
    private String esbRespMsg;
    private String esbRespCode;
    private String startTime;
    private String endTime;
}
