package com.bqd.integratedtester.financialstatement.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.base.tools.HtmlTool;
import com.bqd.enummodel.FinancialStatementColorEnum;
import com.bqd.enummodel.FinancialStatementTypeEnum;
import com.bqd.integratedtester.financialstatement.service.FinancialStatementService;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-12-23
 */
@Service
@Slf4j
public class FinancialStatementServiceImpl implements FinancialStatementService {

    private static final Map<String, Document> firstDocument = new HashMap<>();
    private static final Map<String, Document> secondDocument = new HashMap<>();

    private static final String firstFileIndex = "first";
    private static final String secondFileIndex = "second";

    /**
     * 上传文件，并返回uploadId
     *
     * @param fileIndex 文件索引
     * @param uploadId 文件上传id
     * @param fileStr 文件内容
     * @return 文件上传id
     */
    @Override
    public String upload(String fileIndex, String uploadId, String fileStr) {
        //upload为空，则生成
        if (StrUtil.isBlank(uploadId)) {
            uploadId = IdUtil.fastSimpleUUID();
        }
        if (StrUtil.equals(fileIndex, firstFileIndex)) {
            firstDocument.put(uploadId, Jsoup.parse(fileStr));
        }
        if (StrUtil.equals(fileIndex, secondFileIndex)) {
            secondDocument.put(uploadId, Jsoup.parse(fileStr));
        }
        return uploadId;
    }

    /**
     * 清除指定fileIndex的已上传文件，若没有fileIndex，则清除该id下所有文件
     *
     * @param uploadId 文件上传id
     * @param fileIndex 文件上传索引
     */
    @Override
    public void cleanUpload(String uploadId, String fileIndex) {
        if (StrUtil.equals(fileIndex, firstFileIndex)) {
            firstDocument.remove(uploadId);
            return;
        }
        if (StrUtil.equals(fileIndex, secondFileIndex)) {
            secondDocument.remove(uploadId);
            return;
        }
        //都不匹配则清空
        firstDocument.remove(uploadId);
        secondDocument.remove(uploadId);
    }

    /**
     * 清除所有已上传的文件
     */
    @Override
    public void clearUploadCache() {
        firstDocument.clear();
        secondDocument.clear();
    }

    /**
     * 获取可比对的文件类型
     *
     * @return 可比对的文件类型
     */
    @Override
    public Map<String, String> getCompareTypes() {
        FinancialStatementTypeEnum[] typeEnums = FinancialStatementTypeEnum.values();
        Map<String, String> map = new LinkedHashMap<>();
        for (FinancialStatementTypeEnum typeEnum : typeEnums) {
            map.put(typeEnum.getValue(), typeEnum.getLabel());
        }
        return map;
    }

    /**
     * 资金状况日报表-支行/资金状况日报表-分行 比对
     *
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String fundStatusCompare(String uploadId, int valueDiff) {
        return commonCompare(uploadId, valueDiff, "#tbl-container tr", 1, 0, "td:not(:first-child)", new String[]{"余额"}, "#tbl-container tbody", true, true);
    }

    /**
     * 资产负债简报（人民银行口径） 比对
     *
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String balanceSheetComparePboc(String uploadId, int valueDiff) {
        Document document1 = getDocumentByUploadId(uploadId, firstFileIndex);
        Document document2 = getDocumentByUploadId(uploadId, secondFileIndex);

        //获取所有tr元素
        Elements trElementList1 = document1.select("[lid=报表主体表] tr");
        Elements trElementList2 = document2.select("[lid=报表主体表] tr");

        //获取第二行，即表格列的行，若不为12列则返回
        Elements columnTdElementList1 = trElementList1.get(1).select("td");
        Elements columnTdElementList2 = trElementList2.get(1).select("td");
        if (columnTdElementList1.size() != 12 || columnTdElementList1.size() != columnTdElementList2.size()) {
            throw new CustomizedException("文件结构不正确，不为12列");
        }

        //提取左右两侧的行，Elements元素是td
        List<Elements> tdElementsList1 = extractPbocBalanceSheetTdElementList(trElementList1);
        List<Elements> tdElementsList2 = extractPbocBalanceSheetTdElementList(trElementList2);

        List<Elements> tdExtraElementsList1 = compareTdOneByOne(tdElementsList1, tdElementsList2, valueDiff);

        //遍历文件1中的多余元素，添加到文件2的尾部，作为文件二中删除的元素
        for (Elements tdExtraElements1 : tdExtraElementsList1) {
            tdExtraElements1.forEach(tdExtraElement -> {
                tdExtraElement.attr("style", HtmlTool.changeStyleStr(tdExtraElement.attr("style"), "background-color", FinancialStatementColorEnum.SECOND_DELETE_ROW.getColorCode()));
                tdExtraElement.attr("style", HtmlTool.changeStyleStr(tdExtraElement.attr("style"), "text-decoration", "line-through"));
            });
            Element element = new Element("tr");
            element.addClass("tableRow");
            element.append(tdExtraElements1.outerHtml());
            document2.select("[lid=报表主体表] tbody").append(element.outerHtml());
        }

        return document2.toString();
    }

    /**
     * 资产负债简报（人民银行口径） 提取左右两侧的行
     *
     * @param trElementList
     * @return
     */
    private List<Elements> extractPbocBalanceSheetTdElementList(Elements trElementList) {
        //第一次遍历tr列表，获取左侧的td元素
        List<Elements> tdElementsList = new ArrayList<>();
        for (Element trElement : trElementList) {
            //跳过前两行
            if (trElement.elementSiblingIndex() <= 1) {
                continue;
            }
            //获取tr每一行第一个td
            Element firstTd = trElement.select("td:eq(0)").get(0);

            //若有背景颜色，则说明是表头，跳过
            if (firstTd.attr("style").contains("background-color:#C0C0C0")) {
                continue;
            }

            //如果第一个td占用两列，则添加前5个
            if (StrUtil.isNotBlank(firstTd.attr("colspan"))) {
                Elements tdElementList = trElement.select("td:lt(5)");
                tdElementsList.add(tdElementList);
                continue;
            }

            //添加前6个
            Elements tdElementList = trElement.select("td:lt(6)");
            tdElementsList.add(tdElementList);
        }
        //再次遍历，获取右侧元素
        for (Element trElement : trElementList) {
            //跳过前两个元素
            if (trElement.elementSiblingIndex() <= 1) {
                continue;
            }
            //获取tr倒数第五个元素
            Elements lastFifthTdElementList = trElement.select("td:nth-last-child(5)");
            //若未取到，则跳过
            if (lastFifthTdElementList.isEmpty()) {
                continue;
            }
            Element lastFifthTdElement = lastFifthTdElementList.get(0);

            //若倒数第五个元素colspan=2，则取后5个td
            if (lastFifthTdElement.attr("colspan").equals("2")) {
                Elements allTdElementList = trElement.select("td");
                Elements lastFiveTdElementList = new Elements();
                for (int i = allTdElementList.size() - 5; i < allTdElementList.size(); i++) {
                    lastFiveTdElementList.add(allTdElementList.get(i));
                }
                tdElementsList.add(lastFiveTdElementList);
                continue;
            }

            //否则取后6个元素
            Elements allTdElementList = trElement.select("td");
            Elements lastFiveTdElementList = new Elements();
            for (int i = allTdElementList.size() - 6; i < allTdElementList.size(); i++) {
                lastFiveTdElementList.add(allTdElementList.get(i));
            }
            tdElementsList.add(lastFiveTdElementList);
        }
        return tdElementsList;
    }

    /**
     * 资产负债简报（银保监口径）
     *
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String balanceSheetCompareBir(String uploadId, Integer valueDiff) {
        Document document1 = getDocumentByUploadId(uploadId, firstFileIndex);
        Document document2 = getDocumentByUploadId(uploadId, secondFileIndex);

        //获取所有tr元素
        Elements trElementList1 = document1.select("[lid=报表主体表] tr");
        Elements trElementList2 = document2.select("[lid=报表主体表] tr");

        //获取第二行，即表格列的行，若不为10列则返回
        int columnNum1 = trElementList1.get(1).childrenSize();
        int columnNum2 = trElementList2.get(1).childrenSize();
        if (columnNum1 != 10 || columnNum2 != 10) {
            throw new CustomizedException("文件结构不正确，不为10列");
        }

        //提取左右两侧的行，Elements元素是td
        List<Elements> realRowTdElementsList1 = extractBirBalanceSheetTdElementList(trElementList1);
        List<Elements> realRowTdElementsList2 = extractBirBalanceSheetTdElementList(trElementList2);

        List<Elements> tdExtraElementsList1 = compareTdOneByOne(realRowTdElementsList1, realRowTdElementsList2, valueDiff);

        //遍历文件1中的多余元素，添加到文件2的尾部，作为文件二中删除的元素
        for (Elements tdExtraElements1 : tdExtraElementsList1) {
            tdExtraElements1.forEach(tdExtraElement -> {
                tdExtraElement.attr("style", HtmlTool.changeStyleStr(tdExtraElement.attr("style"), "background-color", FinancialStatementColorEnum.SECOND_DELETE_ROW.getColorCode()));
                tdExtraElement.attr("style", HtmlTool.changeStyleStr(tdExtraElement.attr("style"), "text-decoration", "line-through"));
            });
            Element element = new Element("tr");
            element.addClass("tableRow");
            element.append(tdExtraElements1.outerHtml());
            document2.select("[lid=报表主体表] tbody").append(element.outerHtml());
        }

        return document2.toString();
    }

    /**
     * 资产负债简报（银保监口径） 提取左右两侧的行
     *
     * @param trElementList
     * @return
     */
    private List<Elements> extractBirBalanceSheetTdElementList(Elements trElementList) {
        List<Elements> realRowTdElementsList = new ArrayList<>();

        //第一次遍历tr列表，获取左侧td列表
        for (Element trElement : trElementList) {
            //跳过前两行
            if (trElement.elementSiblingIndex() <= 1) {
                continue;
            }

            //获取tr每行的第一个、第二个td
            Element firstTd = trElement.select("td:eq(0)").get(0);
            Element secondTd = trElement.select("td:eq(1)").get(0);

            //如果第一个td占用3列，则添加前5个td
            if (StrUtil.equals(firstTd.attr("colspan"), "3")) {
                realRowTdElementsList.add(trElement.select("td:lt(5)"));
                continue;
            }
            //如果第一个td占用2列，则添加前6个td
            if (StrUtil.equals(firstTd.attr("colspan"), "2")) {
                realRowTdElementsList.add(trElement.select("td:lt(6)"));
                continue;
            }
            //如果第一个td占用1列，则判断第二个td
            if (StrUtil.isBlank(firstTd.attr("colspan"))) {
                //若第二个td占用2列，则添加前6个td
                if (StrUtil.equals(secondTd.attr("colspan"), "2")) {
                    realRowTdElementsList.add(trElement.select("td:lt(6)"));
                    continue;
                }
                //若第二个占用1列，则添加前7个td
                if (StrUtil.isBlank(firstTd.attr("colspan"))) {
                    realRowTdElementsList.add(trElement.select("td:lt(7)"));
                }
            }
        }
        //第二次遍历tr列表，获取右侧td列表
        for (Element trElement : trElementList) {
            //跳过前两行
            if (trElement.elementSiblingIndex() <= 1) {
                continue;
            }

            //获取tr每行的倒数第五个td、倒数第六个td
            Element lastFifthTd = trElement.select("td:nth-last-child(5)").get(0);
            Element lastSixthTd = trElement.select("td:nth-last-child(6)").get(0);

            //如果倒数第五个td占用3列，则添加后5个td
            if (StrUtil.equals(lastFifthTd.attr("colspan"), "3")) {
                Elements rowTdElementList = trElement.children();
                Elements lastFiveTdElements = new Elements();
                for (int i = rowTdElementList.size() - 5; i < rowTdElementList.size(); i++) {
                    lastFiveTdElements.add(rowTdElementList.get(i));
                }
                realRowTdElementsList.add(lastFiveTdElements);
                continue;
            }

            //如果倒数第五个td占用2列，则添加后6个td
            if (StrUtil.equals(lastFifthTd.attr("colspan"), "2")) {
                Elements rowTdElementList = trElement.children();
                Elements lastSixTdElements = new Elements();
                for (int i = rowTdElementList.size() - 6; i < rowTdElementList.size(); i++) {
                    lastSixTdElements.add(rowTdElementList.get(i));
                }
                realRowTdElementsList.add(lastSixTdElements);
                continue;
            }

            //如果倒数第五个td占用1列，则判断倒数第六个td
            if (StrUtil.isBlank(lastFifthTd.attr("colspan"))) {
                //若倒数第六个td占用2列，则添加后6个td
                if (StrUtil.equals(lastSixthTd.attr("colspan"), "2")) {
                    Elements rowTdElementList = trElement.children();
                    Elements lastSixTdElements = new Elements();
                    for (int i = rowTdElementList.size() - 6; i < rowTdElementList.size(); i++) {
                        lastSixTdElements.add(rowTdElementList.get(i));
                    }
                    realRowTdElementsList.add(lastSixTdElements);
                    continue;
                }
                //若倒数第六个占用1列，则添加后7个td
                if (StrUtil.isBlank(lastFifthTd.attr("colspan"))) {
                    Elements rowTdElementList = trElement.children();
                    Elements lastSevenTdElements = new Elements();
                    for (int i = rowTdElementList.size() - 7; i < rowTdElementList.size(); i++) {
                        lastSevenTdElements.add(rowTdElementList.get(i));
                    }
                    realRowTdElementsList.add(lastSevenTdElements);
                }
            }
        }
        return realRowTdElementsList;
    }

    /**
     * 资产负债简报（人行口径）/资产负债简报（银保监口径） 行数据一一比对
     *
     * @param tdElementsList1
     * @param tdElementsList2
     * @param valueDiff
     * @return
     */
    private List<Elements> compareTdOneByOne(List<Elements> tdElementsList1, List<Elements> tdElementsList2, int valueDiff) {
        //创建第一个文件中多余行的列表，Elements元素是td
        List<Elements> tdExtraElementsList1 = new ArrayList<>();

        //获取元素最少的数量为循环基准
        int tdElementsList1Size = tdElementsList1.size();
        int tdElementsList2Size = tdElementsList2.size();
        int loopLength = Math.min(tdElementsList1Size, tdElementsList2Size);

        //设置循环文件1和文件2的索引
        int index1 = 0;
        int index2 = 0;

        //按最少数量循环
        out:
        for (int i = 0; i < loopLength; i++) {
            Elements tdElementList1 = tdElementsList1.get(index1);
            Elements tdElementList2 = tdElementsList2.get(index2);

            //如果td元素数量不相等，则可能为新增/删除的行
            if (tdElementList1.size() != tdElementList2.size()) {
                if (tdElementsList1Size > tdElementsList2Size) {
                    tdExtraElementsList1.add(tdElementsList1.get(index1));
                    index1++;
                    continue;
                }
                if (tdElementsList1Size < tdElementsList2Size) {
                    tdElementList2.forEach(element -> element.attr("style", HtmlTool.changeStyleStr(element.attr("style"), "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode())));
                    index2++;
                    continue;
                }
                tdExtraElementsList1.add(tdElementsList1.get(index1));
                tdElementList2.forEach(element -> element.attr("style", HtmlTool.changeStyleStr(element.attr("style"), "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode())));
                index1++;
                index2++;
                continue;
            }

            //每个单元格逐一比对
            for (int j = 0; j < tdElementList1.size(); j++) {
                Element tdElement1 = tdElementList1.get(j);
                Element tdElement2 = tdElementList2.get(j);

                //获取span元素
                Elements span1 = tdElement1.select("span");
                Elements span2 = tdElement2.select("span");

                //没有span元素则继续
                if (span1.isEmpty() || span2.isEmpty()) {
                    //同时没有则相等，继续
                    if (span1.isEmpty() && span2.isEmpty()) {
                        continue;
                    }
                    //一方不为空，则可能为新增/删除的行
                    tdExtraElementsList1.add(tdElementsList1.get(index1));
                    tdElementList2.forEach(element -> element.attr("style", HtmlTool.changeStyleStr(element.attr("style"), "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode())));
                    index1++;
                    index2++;
                    continue out;
                }

                //获取元素值
                String valueText1 = span1.get(0).text().replace(",", "");
                String valueText2 = span2.get(0).text().replace(",", "");

                //元素值相同则跳过
                if (valueText1.equals(valueText2)) {
                    continue;
                }

                //都为数字，则开始比较
                if (NumberUtil.isNumber(valueText1) && NumberUtil.isNumber(valueText2)) {
                    applyCompareRules(tdElement2, valueText1, valueText2, valueDiff);
                    continue;
                }

                //若文本不相等，且不为数字，则可能为新增/删除行
                if (tdElementsList1Size > tdElementsList2Size) {
                    tdExtraElementsList1.add(tdElementsList1.get(index1));
                    index1++;
                    continue out;
                }
                if (tdElementsList1Size < tdElementsList2Size) {
                    tdElementList2.forEach(element -> element.attr("style", HtmlTool.changeStyleStr(element.attr("style"), "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode())));
                    index2++;
                    continue out;
                }
                tdExtraElementsList1.add(tdElementsList1.get(index1));
                tdElementList2.forEach(element -> element.attr("style", HtmlTool.changeStyleStr(element.attr("style"), "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode())));
                index1++;
                index2++;
                continue out;
            }

            //比对通过，索引增加
            index1++;
            index2++;

        }
        return tdExtraElementsList1;
    }

    /**
     * 资金状况日报表-全行 比对
     *
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String fundStatusAllCompare(String uploadId, Integer valueDiff) {
        return commonCompare(uploadId, valueDiff, "#tbl-container tr", 2, 0, "td:not(:first-child)", new String[]{"款"}, "#tbl-container tbody", true, true);
    }

    /**
     * 零售-存款日报表（包括含网点） 比对
     *
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String retailDepositCompare(String uploadId, Integer valueDiff) {
        Document document1 = getDocumentByUploadId(uploadId, firstFileIndex);
        Document document2 = getDocumentByUploadId(uploadId, secondFileIndex);

        //获取id=tbl-container下的所有tr元素
        Elements trElementList1 = document1.select("#tbl-container tr");
        Elements trElementList2 = document2.select("#tbl-container tr");

        //将第一个文件的行数据保存到HashMap，key是支行名称，value为tr行元素
        Map<String, Element> trMap1 = new HashMap<>();
        for (Element trElement1 : trElementList1) {
            //跳过前两行
            if (trElement1.elementSiblingIndex() < 2) {
                continue;
            }
            Elements tdElementList1 = trElement1.select("td");
            Element branchNameElement1 = tdElementList1.get(1).select("span").get(0);
            //如果colspan为2，则取第一列
            if (StrUtil.equals(tdElementList1.get(0).attr("colspan"), "2")) {
                branchNameElement1 = tdElementList1.get(0).select("span").get(0);
            }
            trMap1.put(branchNameElement1.text(), trElement1);
        }

        //开始比对
        //遍历第二个文件的tr行元素
        for (Element trElement2 : trElementList2) {
            //跳过前2行，为表头
            if (trElement2.elementSiblingIndex() < 2) {
                continue;
            }

            //获取第二列的单元格元素的值，为支行名称
            Elements tdElementList2 = trElement2.select("td");
            Element branchNameElement2 = tdElementList2.get(1);
            //如果第一个单元格colspan为2，则取第一列
            if (StrUtil.equals(tdElementList2.get(0).attr("colspan"), "2")) {
                branchNameElement2 = tdElementList2.get(0);
            }
            String branchName2 = branchNameElement2.text();

            //获取第一个文件中该支行名称的行，并移除元素
            Element trElement1 = trMap1.remove(branchName2);
            //若第一个文件中没有该支行,则为新增行,设置颜色
            if (trElement1 == null) {
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode());
                continue;
            }

            //获取所有非前两个的td元素即是数据的单元格
            Elements tdDataElementList1 = trElement1.select("td:nth-child(n+3)");
            Elements tdDataElementList2 = trElement2.select("td:nth-child(n+3)");

            //若colspan=2则取所有非第一个的td元素
            if (StrUtil.equals(tdElementList2.get(0).attr("colspan"), "2")) {
                tdDataElementList1 = trElement1.select("td:nth-child(n+2)");
                tdDataElementList2 = trElement2.select("td:nth-child(n+2)");
            }

            //若单元格不同，则文件结构不同，返回
            if (tdDataElementList1.size() != tdDataElementList2.size()) {
                throw new CustomizedException("文件结构不同，无法比对");
            }

            //遍历单元格，进行单元格值比对
            for (int i = 0; i < tdDataElementList2.size(); i++) {
                Element tdDataElement1 = tdDataElementList1.get(i);
                Element tdDataElement2 = tdDataElementList2.get(i);
                String valueText1 = tdDataElement1.select("span").get(0).text();
                String valueText2 = tdDataElement2.select("span").get(0).text();

                applyCompareRules(tdDataElement2, valueText1, valueText2, valueDiff);
            }
        }

        //处理多余元素
        //文档1中的多余元素，添加到文档2中
        if (!trMap1.isEmpty()) {
            trMap1.values().forEach(element -> {
                Element cloneElement1 = element.clone();
                setChildStyle(cloneElement1, "background-color", FinancialStatementColorEnum.SECOND_DELETE_ROW.getColorCode());
                setChildStyle(cloneElement1, "text-decoration", "line-through");
                Elements tbodyElement2 = document2.select("#tbl-container tbody");
                tbodyElement2.append(cloneElement1.outerHtml());
            });
        }

        //判断文档2中是否有重复支行名
        //遍历文档2中的tr元素，支行名称添加到map
        Map<String, Element> branchNameMap2 = new HashMap<>();
        for (Element trElement2 : trElementList2) {
            //跳过前两行表头
            if (trElement2.elementSiblingIndex() < 2) {
                continue;
            }
            Elements tdElementList = trElement2.select("td");
            String branchName = tdElementList.get(1).text();
            if (StrUtil.equals(tdElementList.get(0).attr("colspan"), "2")) {
                branchName = tdElementList.get(0).text();
            }

            //若map中存在说明重复，设置颜色
            if (branchNameMap2.containsKey(branchName)) {
                setChildStyle(branchNameMap2.get(branchName), "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode());
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode());
                continue;
            }
            //不存在添加到map
            branchNameMap2.put(branchName, trElement2);
        }

        //判断文档2中是否有重复行（即两行数据相同，不考虑支行名称）
        //遍历文档2中的tr元素
        out1:
        for (int i = 2; i < trElementList2.size(); i++) {
            boolean isAllZero = true;
            out2:
            for (int j = i + 1; j < trElementList2.size(); j++) {
                //取所有非前两个的td元素
                Elements spanElementList2_1 = trElementList2.get(i).select("td:nth-child(n+3)");
                Elements spanElementList2_2 = trElementList2.get(j).select("td:nth-child(n+3)");
                //若colspan=2，则取所有非第一个的td元素
                if (StrUtil.equals(trElementList2.select("td").get(0).attr("colspan"), "2")) {
                    spanElementList2_1 = trElementList2.get(i).select("td:nth-child(n+2)");
                    spanElementList2_2 = trElementList2.get(j).select("td:nth-child(n+2)");
                }
                //遍历span元素，若存在不相同的数值，说明不是重复行，跳出内层循环，继续外层循环，比较下一行tr
                for (int k = 0; k < spanElementList2_1.size(); k++) {
                    if (!StrUtil.equals(spanElementList2_1.get(k).text(), spanElementList2_2.get(k).text())) {
                        continue out2;
                    }
                    if (isAllZero && !NumberUtil.equals((double) Convert.toDouble(spanElementList2_1.get(k).text()), 0)) {
                        isAllZero = false;
                    }
                }
                if (isAllZero) {
                    continue out1;
                }
                //遍历完span元素说明存在重复行，设置颜色
                setChildStyle(trElementList2.get(i), "background-color", FinancialStatementColorEnum.SECOND_NUM_DEDUPLICATE.getColorCode());
                setChildStyle(trElementList2.get(j), "background-color", FinancialStatementColorEnum.SECOND_NUM_DEDUPLICATE.getColorCode());
            }
        }
        return document2.toString();
    }

    /**
     * 零售-金融资产报表日报表 比对
     *
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String retailFinancialAssetCompare(String uploadId, Integer valueDiff) {
        Document document1 = getDocumentByUploadId(uploadId, firstFileIndex);
        Document document2 = getDocumentByUploadId(uploadId, secondFileIndex);

        Elements trElementList1 = document1.select("#tbl-container tr");
        Elements trElementList2 = document2.select("#tbl-container tr");

        //将第一个文件的行数据保存到HashMap，key是支行名称，value为tr行元素
        Map<String, Element> trMap1 = new HashMap<>();
        for (Element trElement1 : trElementList1) {
            //跳过第一行
            if (trElement1.elementSiblingIndex() < 1) {
                continue;
            }
            Elements tdElementList1 = trElement1.select("td");
            Element branchNameElement1 = tdElementList1.get(1).select("span").get(0);
            //如果colspan为2，则取第一列
            if (StrUtil.equals(tdElementList1.get(0).attr("colspan"), "2")) {
                branchNameElement1 = tdElementList1.get(0).select("span").get(0);
            }
            trMap1.put(branchNameElement1.text(), trElement1);
        }

        //开始比对
        //遍历第二个文件的tr行元素
        for (Element trElement2 : trElementList2) {
            //跳过第一行，为表头
            if (trElement2.elementSiblingIndex() < 1) {
                continue;
            }

            //获取第二列的单元格元素的值，为支行名称
            Elements tdElementList2 = trElement2.select("td");
            Element branchNameElement2 = tdElementList2.get(1);
            //如果第一个单元格colspan为2，则取第一列
            if (StrUtil.equals(tdElementList2.get(0).attr("colspan"), "2")) {
                branchNameElement2 = tdElementList2.get(0);
            }
            String branchName2 = branchNameElement2.text();

            //获取第一个文件中该支行名称的行，并移除元素
            Element trElement1 = trMap1.remove(branchName2);
            //若第一个文件中没有该支行,则为新增行,设置颜色
            if (trElement1 == null) {
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode() + " !important");
                continue;
            }

            //获取所有非前两个的td元素即是数据的单元格
            Elements tdDataElementList1 = trElement1.select("td:nth-child(n+3)");
            Elements tdDataElementList2 = trElement2.select("td:nth-child(n+3)");

            //若colspan=2则取所有非第一个的td元素
            if (StrUtil.equals(tdElementList2.get(0).attr("colspan"), "2")) {
                tdDataElementList1 = trElement1.select("td:nth-child(n+2)");
                tdDataElementList2 = trElement2.select("td:nth-child(n+2)");
            }

            //若单元格不同，则文件结构不同，返回
            if (tdDataElementList1.size() != tdDataElementList2.size()) {
                throw new CustomizedException("文件结构不同，无法比对");
            }

            //遍历单元格，进行单元格值比对
            for (int i = 0; i < tdDataElementList2.size(); i++) {
                Element tdDataElement1 = tdDataElementList1.get(i);
                Element tdDataElement2 = tdDataElementList2.get(i);
                String valueText1 = tdDataElement1.select("span").get(0).text();
                String valueText2 = tdDataElement2.select("span").get(0).text();

                applyCompareRules(tdDataElement2, valueText1, valueText2, valueDiff);
            }
        }

        //处理多余元素
        //文档1中的多余元素，添加到文档2中
        if (!trMap1.isEmpty()) {
            trMap1.values().forEach(element -> {
                Element cloneElement1 = element.clone();
                setChildStyle(cloneElement1, "background-color", FinancialStatementColorEnum.SECOND_DELETE_ROW.getColorCode() + " !important");
                setChildStyle(cloneElement1, "text-decoration", "line-through");
                Elements tbodyElement2 = document2.select("#tbl-container tbody");
                tbodyElement2.append(cloneElement1.outerHtml());
            });
        }

        //判断文档2中是否有重复支行名
        //遍历文档2中的tr元素，支行名称添加到map
        Map<String, Element> branchNameMap2 = new HashMap<>();
        for (Element trElement2 : trElementList2) {
            //跳过第一行表头
            if (trElement2.elementSiblingIndex() < 1) {
                continue;
            }
            Elements tdElementList = trElement2.select("td");
            String branchName = tdElementList.get(1).text();
            if (StrUtil.equals(tdElementList.get(0).attr("colspan"), "2")) {
                branchName = tdElementList.get(0).text();
            }

            //若map中存在说明重复，设置颜色
            if (branchNameMap2.containsKey(branchName)) {
                setChildStyle(branchNameMap2.get(branchName), "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                continue;
            }
            //不存在添加到map
            branchNameMap2.put(branchName, trElement2);
        }

        //判断文档2中是否有重复行（即两行数据相同，不考虑支行名称）
        //遍历文档2中的tr元素
        out1:
        for (int i = 1; i < trElementList2.size(); i++) {
            boolean isAllZero = true;
            out2:
            for (int j = i + 1; j < trElementList2.size(); j++) {
                //取所有非前两个的td元素
                Elements spanElementList2_1 = trElementList2.get(i).select("td:nth-child(n+3)");
                Elements spanElementList2_2 = trElementList2.get(j).select("td:nth-child(n+3)");
                //若colspan=2，则取所有非第一个的td元素
                if (StrUtil.equals(trElementList2.select("td").get(0).attr("colspan"), "2")) {
                    spanElementList2_1 = trElementList2.get(i).select("td:nth-child(n+2)");
                    spanElementList2_2 = trElementList2.get(j).select("td:nth-child(n+2)");
                }
                //遍历span元素，若存在不相同的数值，说明不是重复行，跳出内层循环，继续外层循环，比较下一行tr
                for (int k = 0; k < spanElementList2_1.size(); k++) {
                    if (!StrUtil.equals(spanElementList2_1.get(k).text(), spanElementList2_2.get(k).text())) {
                        continue out2;
                    }
                    if (isAllZero && NumberUtil.equals((double) Convert.toDouble(spanElementList2_1.get(k).text()), 0)) {
                        isAllZero = false;
                    }
                }
                if (isAllZero) {
                    continue out1;
                }
                //遍历完span元素说明存在重复行，设置颜色
                setChildStyle(trElementList2.get(i), "background-color", FinancialStatementColorEnum.SECOND_NUM_DEDUPLICATE.getColorCode() + " !important");
                setChildStyle(trElementList2.get(j), "background-color", FinancialStatementColorEnum.SECOND_NUM_DEDUPLICATE.getColorCode() + " !important");
            }
        }

        return document2.toString();
    }

    /**
     * 零售客户月日均金融资产分层日报表 比对
     *
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String retailCustomerAvgAssetCompare(String uploadId, Integer valueDiff) {
        return commonCompare(uploadId, valueDiff, "#tbl-container tr", 2, 0, "td:not(:first-child)", new String[]{""}, "#tbl-container tbody", false, true);
    }

    /**
     * 个贷-个人贷款日报表 比对
     *
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String personalLoanCompare(String uploadId, Integer valueDiff) {
        return commonCompare(uploadId, valueDiff, "#tbl-container tr", 1, 0, "td:not(:first-child)", new String[]{""}, "#tbl-container tbody", false, true);
    }

    /**
     * 各分支机构日均存款情况表 比对
     *
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String branchDailyDepositsCompare(String uploadId, Integer valueDiff) {
        return commonCompare(uploadId, valueDiff, "#tbl-container tr", 2, 0, "td:not(:first-child)", new String[]{""}, "#tbl-container tbody", false, true);
    }

    /**
     * 指标变化明细日报表 比对
     *
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String indicatorChangeCompare(String uploadId, Integer valueDiff) {
        Document document1 = getDocumentByUploadId(uploadId, firstFileIndex);
        Document document2 = getDocumentByUploadId(uploadId, secondFileIndex);

        Elements trElementList1;
        Elements trElementList2;
        Map<String, Element> trMap1;
        Map<String, Element> branchNameMap2;

        //信贷主要变化情况
        trElementList1 = document1.select("[lid=列表1] tr");
        trElementList2 = document2.select("[lid=列表1] tr");

        //提取行map
        trMap1 = new HashMap<>();
        for (Element trElement1 : trElementList1) {
            //跳过表头行
            if (trElement1.elementSiblingIndex() <= 2) {
                continue;
            }
            Elements tdElementList1 = trElement1.select("td");
            //企业名称+业务品种+发生类型+支行名称作为key
            trMap1.put(tdElementList1.get(1).select("span").get(0).text() + tdElementList1.get(2).select("span").get(0).text() + tdElementList1.get(3).select("span").get(0).text() + tdElementList1.get(5).select("span").get(0).text(), trElement1);
        }

        //开始比对
        for (Element trElement2 : trElementList2) {
            //跳过表头行
            if (trElement2.elementSiblingIndex() <= 2) {
                continue;
            }

            ////第二个文件表格中单元格的背景颜色设置为白色
            setChildStyle(trElement2, "background-color", "#FFFFFF");
            setChildStyle(trElement2, "color", "#000000");

            //获取 表格列头
            String branchName2 = trElement2.select("td span").get(1).text() + trElement2.select("td span").get(2).text() + trElement2.select("td span").get(3).text() + trElement2.select("td span").get(5).text();

            //获取第一个文件中该支行名称的行，并移除元素
            Element trElement1 = trMap1.remove(branchName2);
            //若第一个文件中没有该支行,则为新增行,设置颜色
            if (trElement1 == null) {
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode() + " !important");
                continue;
            }

            //获取所有非第一个的td元素即是数据的单元格
            Elements tdDataElementList1 = trElement1.select("td:nth-child(5)");
            Elements tdDataElementList2 = trElement2.select("td:nth-child(5)");

            //若单元格不同，则文件结构不同，返回
            if (tdDataElementList1.size() != tdDataElementList2.size()) {
                throw new CustomizedException("文件结构不同，无法比对");
            }

            //遍历单元格，进行单元格值比对
            for (int i = 0; i < tdDataElementList2.size(); i++) {
                Element tdElement1 = tdDataElementList1.get(i);
                Element tdElement2 = tdDataElementList2.get(i);

                applyCompareRules(tdElement2, tdElement1.text(), tdElement2.text(), valueDiff);
            }
        }

        handleExtraRow(trMap1, document2.select("[lid=列表1] tbody").get(0));

        //检查重复名称
        branchNameMap2 = new HashMap<>();
        for (Element trElement2 : trElementList2) {
            //跳过表头行
            if (trElement2.elementSiblingIndex() <= 2) {
                continue;
            }
            String branchName = trElement2.select("td span").get(1).text() + trElement2.select("td span").get(2).text() + trElement2.select("td span").get(3).text() + trElement2.select("td span").get(5).text();
            //若map中存在说明重复，设置颜色
            if (branchNameMap2.containsKey(branchName)) {
                setChildStyle(branchNameMap2.get(branchName), "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                continue;
            }
            //不存在添加到map
            branchNameMap2.put(branchName, trElement2);
        }

        //当前逾期与垫款明细（大于100万）
        trElementList1 = document1.select("[lid=列表6] tr");
        trElementList2 = document2.select("[lid=列表6] tr");

        //提取行map
        trMap1 = new HashMap<>();
        for (Element trElement1 : trElementList1) {
            //跳过表头行
            if (trElement1.elementSiblingIndex() <= 1) {
                continue;
            }
            Elements tdElementList1 = trElement1.select("td");
            //企业名称+业务品种+发生类型+支行名称作为key
            trMap1.put(tdElementList1.get(1).select("span").get(0).text() + tdElementList1.get(2).select("span").get(0).text() + tdElementList1.get(5).select("span").get(0).text(), trElement1);
        }

        //开始比对
        for (Element trElement2 : trElementList2) {
            //跳过表头行
            if (trElement2.elementSiblingIndex() <= 1) {
                continue;
            }

            ////第二个文件表格中单元格的背景颜色设置为白色
            setChildStyle(trElement2, "background-color", "#FFFFFF");
            setChildStyle(trElement2, "color", "#000000");

            //获取 表格列头
            String branchName2 = trElement2.select("td span").get(1).text() + trElement2.select("td span").get(2).text() + trElement2.select("td span").get(5).text();

            //获取第一个文件中该支行名称的行，并移除元素
            Element trElement1 = trMap1.remove(branchName2);
            //若第一个文件中没有该支行,则为新增行,设置颜色
            if (trElement1 == null) {
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode() + " !important");
                continue;
            }

            //获取所有非第一个的td元素即是数据的单元格
            Elements tdDataElementList1 = trElement1.select("td:nth-child(4),td:nth-child(5)");
            Elements tdDataElementList2 = trElement2.select("td:nth-child(4),td:nth-child(5)");

            //若单元格不同，则文件结构不同，返回
            if (tdDataElementList1.size() != tdDataElementList2.size()) {
                throw new CustomizedException("文件结构不同，无法比对");
            }

            //遍历单元格，进行单元格值比对
            for (int i = 0; i < tdDataElementList2.size(); i++) {
                Element tdElement1 = tdDataElementList1.get(i);
                Element tdElement2 = tdDataElementList2.get(i);

                applyCompareRules(tdElement2, tdElement1.text(), tdElement2.text(), valueDiff);
            }
        }

        handleExtraRow(trMap1, document2.select("[lid=列表6] tbody").get(0));

        //检查重复名称
        branchNameMap2 = new HashMap<>();
        for (Element trElement2 : trElementList2) {
            //跳过表头行
            if (trElement2.elementSiblingIndex() <= 2) {
                continue;
            }
            String branchName = trElement2.select("td span").get(1).text() + trElement2.select("td span").get(2).text() + trElement2.select("td span").get(5).text();
            //若map中存在说明重复，设置颜色
            if (branchNameMap2.containsKey(branchName)) {
                setChildStyle(branchNameMap2.get(branchName), "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                continue;
            }
            //不存在添加到map
            branchNameMap2.put(branchName, trElement2);
        }

        //大额入款情况表 和 大额出款情况表
        for (int loopIdx = 0; loopIdx < 2; loopIdx++) {
            String lid = "";
            if (loopIdx == 0) {
                lid = "[lid=列表7]";
            }
            if (loopIdx == 1) {
                lid = "[lid=列表3]";
            }

            trElementList1 = document1.select(lid + " tr");
            trElementList2 = document2.select(lid + " tr");

            //提取行map
            trMap1 = new HashMap<>();
            for (Element trElement1 : trElementList1) {
                //跳过表头行
                if (trElement1.elementSiblingIndex() <= 1) {
                    continue;
                }
                Elements tdElementList1 = trElement1.select("td");
                //企业名称+支行名称作为key
                trMap1.put(tdElementList1.get(1).text() + tdElementList1.get(3).text(), trElement1);
            }

            //开始比对
            for (Element trElement2 : trElementList2) {
                //跳过表头行
                if (trElement2.elementSiblingIndex() <= 1) {
                    continue;
                }

                ////第二个文件表格中单元格的背景颜色设置为白色
                setChildStyle(trElement2, "background-color", "#FFFFFF");
                setChildStyle(trElement2, "color", "#000000");

                //获取 表格列头
                String branchName2 = trElement2.select("td").get(1).text() + trElement2.select("td").get(3).text();

                //获取第一个文件中该支行名称的行，并移除元素
                Element trElement1 = trMap1.remove(branchName2);
                //若第一个文件中没有该支行,则为新增行,设置颜色
                if (trElement1 == null) {
                    setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode() + " !important");
                    continue;
                }

                //获取所有非第一个的td元素即是数据的单元格
                Elements tdDataElementList1 = trElement1.select("td:nth-of-type(3)");
                Elements tdDataElementList2 = trElement2.select("td:nth-of-type(3)");

                //若单元格不同，则文件结构不同，返回
                if (tdDataElementList1.size() != tdDataElementList2.size()) {
                    throw new CustomizedException("文件结构不同，无法比对");
                }

                //遍历单元格，进行单元格值比对
                for (int i = 0; i < tdDataElementList2.size(); i++) {
                    Element tdElement1 = tdDataElementList1.get(i);
                    Element tdElement2 = tdDataElementList2.get(i);

                    applyCompareRules(tdElement2, tdElement1.text(), tdElement2.text(), valueDiff);
                }
            }

            handleExtraRow(trMap1, document2.select(lid + " tbody").get(0));

            //检查重复名称
            branchNameMap2 = new HashMap<>();
            for (Element trElement2 : trElementList2) {
                //跳过表头行
                if (trElement2.elementSiblingIndex() <= 2) {
                    continue;
                }
                String branchName = trElement2.select("td").get(1).text() + trElement2.select("td").get(3).text();
                //若map中存在说明重复，设置颜色
                if (branchNameMap2.containsKey(branchName)) {
                    setChildStyle(branchNameMap2.get(branchName), "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                    setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                    continue;
                }
                //不存在添加到map
                branchNameMap2.put(branchName, trElement2);
            }
        }

        //欠息明细和不良贷款明细
        for (int loopIdx = 0; loopIdx < 2; loopIdx++) {
            String lid = "";
            if (loopIdx == 0) {
                lid = "[lid=列表4]";
            }
            if (loopIdx == 1) {
                lid = "[lid=列表5]";
            }

            trElementList1 = document1.select(lid + " tr");
            trElementList2 = document2.select(lid + " tr");

            //提取行map
            trMap1 = new HashMap<>();
            for (Element trElement1 : trElementList1) {
                //跳过表头行
                if (trElement1.elementSiblingIndex() <= 1) {
                    continue;
                }
                Elements tdElementList1 = trElement1.select("td");
                //企业名称+支行名称作为key
                trMap1.put(tdElementList1.get(1).text() + tdElementList1.get(2).text() + tdElementList1.get(4).text(), trElement1);
            }

            //开始比对
            for (Element trElement2 : trElementList2) {
                //跳过表头行
                if (trElement2.elementSiblingIndex() <= 1) {
                    continue;
                }

                ////第二个文件表格中单元格的背景颜色设置为白色
                setChildStyle(trElement2, "background-color", "#FFFFFF");
                setChildStyle(trElement2, "color", "#000000");

                //获取 表格列头
                String branchName2 = trElement2.select("td").get(1).text() + trElement2.select("td").get(2).text() + trElement2.select("td").get(4).text();

                //获取第一个文件中该支行名称的行，并移除元素
                Element trElement1 = trMap1.remove(branchName2);
                //若第一个文件中没有该支行,则为新增行,设置颜色
                if (trElement1 == null) {
                    setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode() + " !important");
                    continue;
                }

                //获取所有非第一个的td元素即是数据的单元格
                Elements tdDataElementList1 = trElement1.select("td:nth-of-type(4)");
                Elements tdDataElementList2 = trElement2.select("td:nth-of-type(4)");

                //若单元格不同，则文件结构不同，返回
                if (tdDataElementList1.size() != tdDataElementList2.size()) {
                    throw new CustomizedException("文件结构不同，无法比对");
                }

                //遍历单元格，进行单元格值比对
                for (int i = 0; i < tdDataElementList2.size(); i++) {
                    Element tdElement1 = tdDataElementList1.get(i);
                    Element tdElement2 = tdDataElementList2.get(i);

                    applyCompareRules(tdElement2, tdElement1.text(), tdElement2.text(), valueDiff);
                }
            }

            handleExtraRow(trMap1, document2.select(lid + " tbody").get(0));

            //检查重复名称
            branchNameMap2 = new HashMap<>();
            for (Element trElement2 : trElementList2) {
                //跳过表头行
                if (trElement2.elementSiblingIndex() <= 2) {
                    continue;
                }
                String branchName = trElement2.select("td").get(1).text() + trElement2.select("td").get(2).text() + trElement2.select("td").get(4).text();
                //若map中存在说明重复，设置颜色
                if (branchNameMap2.containsKey(branchName)) {
                    setChildStyle(branchNameMap2.get(branchName), "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                    setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                    continue;
                }
                //不存在添加到map
                branchNameMap2.put(branchName, trElement2);
            }

        }

        return document2.toString();
    }

    /**
     * 信用风险监测日报表 比对
     *
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String creditRiskMonitoringCompare(String uploadId, Integer valueDiff) {
        Document document1 = getDocumentByUploadId(uploadId, firstFileIndex);
        Document document2 = getDocumentByUploadId(uploadId, secondFileIndex);

        Elements trElementList1;
        Elements trElementList2;
        Map<String, Element> trMap1;

        //不良贷款情况表比对
        trElementList1 = document1.select("[lid=列表6] tr");
        trElementList2 = document2.select("[lid=列表6] tr");
        addColNameAttr(trElementList1, 2, 0);
        trMap1 = extractRowToMap(trElementList1, 2, 0);
        compare(trMap1, trElementList2, valueDiff, 2, 0, "td:not(:first-child)", "");
        handleExtraRow(trMap1, document2.select("[lid=列表6] tbody").get(0));
        checkDuplicateRowName(trElementList2, 2, 0);
        checkDuplicateRowData(trElementList2, "td:not(:first-child)", 2);

        //分支机构不良贷款排名情况表
        trElementList1 = document1.select("[lid=列表5] tr");
        trElementList2 = document2.select("[lid=列表5] tr");
        addColNameAttr(trElementList1, 2, 1);
        trMap1 = extractRowToMap(trElementList1, 2, 1);
        compare(trMap1, trElementList2, valueDiff, 2, 1, "td:nth-child(n+3)", "");
        handleExtraRow(trMap1, document2.select("[lid=列表5] tbody").get(0));
        checkDuplicateRowName(trElementList2, 2, 1);
        checkDuplicateRowData(trElementList2, "td:nth-child(n+2)", 2);

        //逾期贷款(垫款)明细表
        trElementList1 = document1.select("[lid=列表4] tr");
        trElementList2 = document2.select("[lid=列表4] tr");
        addColNameAttr(trElementList1, 2, 1);
        trMap1 = extractRowToMap(trElementList1, 2, 1);
        compare(trMap1, trElementList2, valueDiff, 2, 1, "td:nth-child(n+3)", "");
        handleExtraRow(trMap1, document2.select("[lid=列表4] tbody").get(0));
        checkDuplicateRowName(trElementList2, 2, 1);
        checkDuplicateRowData(trElementList2, "td:nth-child(n+3)", 2);

        //公贷不良贷款明细表
        trElementList1 = document1.select("[lid=列表2] tr");
        trElementList2 = document2.select("[lid=列表2] tr");

        //客户名称+业务品种作为key，添加行数据到map
        trMap1 = new HashMap<>();
        for (Element trElement1 : trElementList1) {
            //跳过表头行
            if (trElement1.elementSiblingIndex() <= 2) {
                continue;
            }
            Elements tdElementList1 = trElement1.select("td");
            //客户名称+业务品种作为key
            trMap1.put(tdElementList1.get(1).select("span").get(0).text() + tdElementList1.get(2).select("span").get(0).text(), trElement1);
        }

        //开始比对
        for (Element trElement2 : trElementList2) {
            //跳过表头行
            if (trElement2.elementSiblingIndex() <= 2) {
                continue;
            }

            ////第二个文件表格中单元格的背景颜色设置为白色
            setChildStyle(trElement2, "background-color", "#FFFFFF");
            setChildStyle(trElement2, "color", "#000000");

            //获取 表格列头
            String branchName2 = trElement2.select("td span").get(1).text() + trElement2.select("td span").get(2).text();

            //获取第一个文件中该支行名称的行，并移除元素
            Element trElement1 = trMap1.remove(branchName2);
            //若第一个文件中没有该支行,则为新增行,设置颜色
            if (trElement1 == null) {
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode() + " !important");
                continue;
            }

            //获取所有非第一个的td元素即是数据的单元格
            Elements tdDataElementList1 = trElement1.select("td:nth-child(n+4)");
            Elements tdDataElementList2 = trElement2.select("td:nth-child(n+4)");

            //若单元格不同，则文件结构不同，返回
            if (tdDataElementList1.size() != tdDataElementList2.size()) {
                throw new CustomizedException("文件结构不同，无法比对");
            }

            //遍历单元格，进行单元格值比对
            for (int i = 0; i < tdDataElementList2.size(); i++) {
                Element tdElement1 = tdDataElementList1.get(i);
                Element tdElement2 = tdDataElementList2.get(i);

                applyCompareRules(tdElement2, tdElement1.text(), tdElement2.text(), valueDiff);
            }
        }

        handleExtraRow(trMap1, document2.select("[lid=列表2] tbody").get(0));

        //检查重复名称
        Map<String, Element> branchNameMap2 = new HashMap<>();
        for (Element trElement2 : trElementList2) {
            //跳过表头行
            if (trElement2.elementSiblingIndex() <= 2) {
                continue;
            }
            String branchName = trElement2.select("td span").get(1).text() + trElement2.select("td span").get(2).text();
            //若map中存在说明重复，设置颜色
            if (branchNameMap2.containsKey(branchName)) {
                setChildStyle(branchNameMap2.get(branchName), "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                continue;
            }
            //不存在添加到map
            branchNameMap2.put(branchName, trElement2);
        }
        checkDuplicateRowData(trElementList2, "td:nth-child(n+4)", 2);

        return document2.toString();
    }

    /**
     * 全行大客户列表/分支行大客户列表 比对
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @Override
    public String majorCustomerCompare(String uploadId, Integer valueDiff) {
        //时点余额大于500万客户明细
        commonCompare(uploadId, valueDiff, "[lid=列表6] tr", 2, 2, "td:nth-child(n+4)", new String[]{"余额"}, "[lid=列表6] tbody", true, false);
        //年度新增100万以上客户明细
        commonCompare(uploadId, valueDiff, "[lid=列表7] tr", 2, 2, "td:nth-child(n+4)", new String[]{"余额", "年日均", "月日均"}, "[lid=列表7] tbody", true, false);
        //年度减少100万以上客户明细
        commonCompare(uploadId, valueDiff, "[lid=列表8] tr", 2, 2, "td:nth-child(n+4)", new String[]{"余额", "年日均", "月日均"}, "[lid=列表8] tbody", true, false);
        //贷款余额500万以上客户明细
        return commonCompare(uploadId, valueDiff, "[lid=列表9] tr", 2, 2, "td:nth-child(n+4)", new String[]{"贷款余额", "存款余额"}, "[lid=列表9] tbody", true, false);
    }

    /**
     * 根据uploadId获取Document
     *
     * @param uploadId
     * @param fileIndex
     * @return
     */
    private Document getDocumentByUploadId(String uploadId, String fileIndex) {
        Document document = null;
        if (StrUtil.equals(fileIndex, firstFileIndex)) {
            document = firstDocument.get(uploadId);
        }
        if (StrUtil.equals(fileIndex, secondFileIndex)) {
            document = secondDocument.get(uploadId);
        }
        if (ObjectUtil.isNull(document)) {
            throw new CustomizedException("请先上传文件");
        }
        return document;
    }

    /**
     * 通用比对方法：即第0列为行名
     *
     * @param uploadId
     * @param valueDiff
     * @param colNameRowNum
     * @param rowNameColNum
     * @param tdDataCellCssQueryFromTr
     * @param compareCol
     * @return
     */
    private String commonCompare(
            String uploadId,
            int valueDiff,
            String trElementListCssQuery,
            int colNameRowNum,
            int rowNameColNum,
            String tdDataCellCssQueryFromTr,
            String[] compareCol,
            String tbodyElementCssQuery,
            boolean addColNameAttr,
            boolean checkDuplicateRowData) {
        Document document1 = getDocumentByUploadId(uploadId, firstFileIndex);
        Document document2 = getDocumentByUploadId(uploadId, secondFileIndex);

        //获取表格中所有tr元素
        Elements trElementList1 = document1.select(trElementListCssQuery);
        Elements trElementList2 = document2.select(trElementListCssQuery);

        //给数据单元格添加colname属性
        if (addColNameAttr) {
            addColNameAttr(trElementList1, colNameRowNum, rowNameColNum);
        }

        //将第一个文件的行数据保存到HashMap，key是支行名称，value为tr行元素
        Map<String, Element> trMap1 = extractRowToMap(trElementList1, colNameRowNum, rowNameColNum);

        //进行比较
        compare(trMap1, trElementList2, valueDiff, colNameRowNum, rowNameColNum, tdDataCellCssQueryFromTr, compareCol);

        //处理多余元素，文档1中的多余元素，添加到文档2中
        handleExtraRow(trMap1, document2.select(tbodyElementCssQuery).get(0));

        //判断文档2中是否有重复行名
        checkDuplicateRowName(trElementList2, colNameRowNum, rowNameColNum);

        //判断文档2中是否有重复行（即两行数据相同，不考虑行名称）
        if (checkDuplicateRowData) {
            checkDuplicateRowData(trElementList2, tdDataCellCssQueryFromTr, colNameRowNum);
        }

        return document2.toString();
    }

    /**
     * 给数据单元格添加colname属性
     *
     * @param trElementList
     * @param colNameRowNum
     * @param rowNameColNum
     */
    private void addColNameAttr(Elements trElementList, int colNameRowNum, int rowNameColNum) {
        //获取列表1表头行的所有td span元素
        Elements headerDataTdElementList = trElementList.get(colNameRowNum).select("td");
        //遍历所有行，给span元素添加colname列名称属性
        for (Element trElement : trElementList) {
            //跳过表头行
            if (trElement.elementSiblingIndex() <= colNameRowNum) {
                continue;
            }
            //获取每一数据行的td span元素
            Elements tdElementList = trElement.select("td");
            //跳过 名称列 开始遍历，给数据span元素添加colname列名称属性
            for (int i = rowNameColNum + 1; i < tdElementList.size(); i++) {
                tdElementList.get(i).attr("colname", headerDataTdElementList.get(i).text());
            }
        }
    }

    /**
     * 提取tr元素到map中
     *
     * @param trElementList
     * @param colNameRowNum
     * @param rowNameColNum
     * @return
     */
    private Map<String, Element> extractRowToMap(Elements trElementList, int colNameRowNum, int rowNameColNum) {
        Map<String, Element> map = new HashMap<>();
        for (Element trElement1 : trElementList) {
            //跳过表头行
            if (trElement1.elementSiblingIndex() <= colNameRowNum) {
                continue;
            }
            Elements tdElementList1 = trElement1.select("td");
            Element branchNameElement1 = tdElementList1.get(rowNameColNum).select("span").get(0);
            map.put(branchNameElement1.text(), trElement1);
        }
        return map;
    }

    /**
     * 给定trMap和tr的ElementList，进行比对
     *
     * @param trMap
     * @param trElementList
     * @param valueDiff
     * @param colNameRowNum
     * @param rowNameColNum
     * @param tdDataCellCssQueryFromTr
     * @param compareCol
     */
    private void compare(Map<String, Element> trMap, Elements trElementList, int valueDiff, int colNameRowNum, int rowNameColNum, String tdDataCellCssQueryFromTr, String...compareCol) {
        //开始比对
        //遍历第二个文件的tr行元素
        for (Element trElement2 : trElementList) {
            //跳过表头行
            if (trElement2.elementSiblingIndex() <= colNameRowNum) {
                continue;
            }

            //移除第二个文件表格中单元格的背景，并将span字体颜色设置为黑色
            setChildStyle(trElement2, "background-color", "#FFFFFF");
            setChildStyle(trElement2, "color", "#000000");

            //获取 表格列头
            Element branchNameElement2 = trElement2.select("td span").get(rowNameColNum);
            String branchName2 = branchNameElement2.text();

            //获取第一个文件中该支行名称的行，并移除元素
            Element trElement1 = trMap.remove(branchName2);
            //若第一个文件中没有该支行,则为新增行,设置颜色
            if (trElement1 == null) {
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_NEW_ROW.getColorCode() + " !important");
                continue;
            }

            //获取所有非第一个的td元素即是数据的单元格
            Elements tdDataElementList1 = trElement1.select(tdDataCellCssQueryFromTr);
            Elements tdDataElementList2 = trElement2.select(tdDataCellCssQueryFromTr);

            //若单元格不同，则文件结构不同，返回
            if (tdDataElementList1.size() != tdDataElementList2.size()) {
                throw new CustomizedException("文件结构不同，无法比对");
            }

            //遍历单元格，进行单元格值比对
            for (int i = 0; i < tdDataElementList2.size(); i++) {
                Element tdElement1 = tdDataElementList1.get(i);
                Element tdElement2 = tdDataElementList2.get(i);

                //只比对指定列，忽略其它列，若无colname则全比对
                if (StrUtil.isEmpty(tdElement1.attr("colname")) || StrUtil.containsAny(tdElement1.attr("colname"), compareCol)) {
                    applyCompareRules(tdElement2, tdElement1.text(), tdElement2.text(), valueDiff);
                }
            }
        }
    }

    /**
     * 应用比对规则
     *
     * @param tdElement
     * @param valueText1
     * @param valueText2
     * @param valueDiff
     */
    private void applyCompareRules(Element tdElement, String valueText1, String valueText2, int valueDiff) {

        valueText1 = valueText1.replace(",", "");
        valueText2 = valueText2.replace(",", "");

        //不比对百分比值
        if (StrUtil.contains(valueText1, "%") || StrUtil.contains(valueText2, "%")) {
            return;
        }

        //判断第二个值是否为空，为空标注出并返回
        if (StrUtil.isBlank(valueText2)) {
            tdElement.attr("style", HtmlTool.changeStyleStr(tdElement.attr("style"), "background-color", FinancialStatementColorEnum.VALUE_EMPTY.getColorCode()));
            return;
        }

        //不比对非数字
        if (!NumberUtil.isNumber(valueText1) || !NumberUtil.isNumber(valueText2)) {
            log.warn("协同办公报表比对-非数字：{}==={}", valueText1, valueText2);
            return;
        }

        double value1 = Double.parseDouble(valueText1);
        double value2 = Double.parseDouble(valueText2);

        //比较规则1：前一天非零，后一天是零
        if (value1 != 0 && value2 == 0) {
            String style = tdElement.attr("style");
            tdElement.attr("style", HtmlTool.changeStyleStr(style, "background-color", FinancialStatementColorEnum.SECOND_ZERO.getColorCode()));
        }

        //比较规则2：前一天是零，后一天非零
        if (value1 == 0 && value2 != 0) {
            String style = tdElement.attr("style");
            tdElement.attr("style", HtmlTool.changeStyleStr(style, "background-color", FinancialStatementColorEnum.FIRST_ZERO.getColorCode()));
        }

        //比较规则3：数据差值大于指定值（默认为10000）
        if (Math.abs(value2 - value1) > valueDiff) {
            String style = tdElement.attr("style");
            tdElement.attr("style", HtmlTool.changeStyleStr(style, "background-color", FinancialStatementColorEnum.VALUE_DIFF.getColorCode()));
        }
    }

    /**
     * 处理多余的行
     *
     * @param trMap
     * @param tbodyElement
     */
    private void handleExtraRow(Map<String, Element> trMap, Element tbodyElement) {
        if (!trMap.isEmpty()) {
            trMap.values().forEach(element -> {
                Element cloneElement = element.clone();
                setChildStyle(cloneElement, "background-color", FinancialStatementColorEnum.SECOND_DELETE_ROW.getColorCode() + " !important");
                setChildStyle(cloneElement, "text-decoration", "line-through");
                tbodyElement.append(cloneElement.outerHtml());
            });
        }
    }

    /**
     * 检查是否有重复的行名
     *
     * @param trElementList
     * @param colNameRowNum
     * @param rowNameColNum
     */
    private void checkDuplicateRowName(Elements trElementList, int colNameRowNum, int rowNameColNum) {
        Map<String, Element> branchNameMap2 = new HashMap<>();
        for (Element trElement2 : trElementList) {
            //跳过表头行
            if (trElement2.elementSiblingIndex() <= colNameRowNum) {
                continue;
            }
            String branchName = trElement2.select("td span").get(rowNameColNum).text();
            //若map中存在说明重复，设置颜色
            if (branchNameMap2.containsKey(branchName)) {
                setChildStyle(branchNameMap2.get(branchName), "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                setChildStyle(trElement2, "background-color", FinancialStatementColorEnum.SECOND_SAME_NAME.getColorCode() + " !important");
                continue;
            }
            //不存在添加到map
            branchNameMap2.put(branchName, trElement2);
        }
    }

    /**
     * 检查是否有重复的数据行
     *
     * @param trElementList
     * @param tdDataCellCssQueryFromTr
     * @param colNameRowNum
     */
    private void checkDuplicateRowData(Elements trElementList, String tdDataCellCssQueryFromTr, int colNameRowNum) {
        for (int i = colNameRowNum + 1; i < trElementList.size(); i++) {
            boolean isAllZero = true;
            Elements tdDataElementList2_1 = trElementList.get(i).select(tdDataCellCssQueryFromTr);
            out:
            for (int j = i + 1; j < trElementList.size(); j++) {
                Elements tdDataElementList2_2 = trElementList.get(j).select(tdDataCellCssQueryFromTr);
                //遍历span元素，若存在不相同的数值，说明不是重复行，跳出内层循环，继续外层循环，比较下一行tr
                for (int k = 0; k < tdDataElementList2_1.size(); k++) {
                    if (!StrUtil.equals(tdDataElementList2_1.get(k).text(), tdDataElementList2_2.get(k).text())) {
                        continue out;
                    }
                    if (isAllZero) {
                        Double value = Convert.toDouble(tdDataElementList2_1.get(k).text());
                        if (value != null && !NumberUtil.equals((double)value, 0)) {
                            isAllZero = false;
                        }
                    }
                }
                //全为0，则跳过
                if (isAllZero) {
                    continue;
                }
                //遍历完span元素说明存在重复行，且不全部为0，则设置颜色
                setChildStyle(trElementList.get(i), "background-color", FinancialStatementColorEnum.SECOND_NUM_DEDUPLICATE.getColorCode() + " !important");
                setChildStyle(trElementList.get(j), "background-color", FinancialStatementColorEnum.SECOND_NUM_DEDUPLICATE.getColorCode() + " !important");
            }
        }
    }

    /**
     * 设置给定Element的所有子元素样式
     *
     * @param element
     * @param styleName
     * @param value
     */
    private void setChildStyle(Element element, String styleName, String value) {
        element.children().forEach(node -> {
            String style = node.attr("style");
            node.attr("style", HtmlTool.changeStyleStr(style, styleName, value));
        });
    }

}
