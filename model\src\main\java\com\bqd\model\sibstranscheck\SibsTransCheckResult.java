package com.bqd.model.sibstranscheck;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-07-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SibsTransCheckResult {
    private String id;
    private String seqNo;
    private String transTimestamp;
    private String interfaceId;
    private String checkResult;
}
