<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbAssetDetailMapper">
    <resultMap id="esbAssetDetail" type="com.bqd.model.esbnetworkreplay.EsbAssetDetail">
        <result column="ASSET_ID" property="assetId" javaType="java.lang.String"/>
        <result column="REQUEST_BODY" property="requestBody" javaType="java.lang.String"/>
        <result column="RESPONSE_BODY" property="responseBody" javaType="java.lang.String"/>
    </resultMap>

    <resultMap id="assetDetailDto" type="com.bqd.model.esbnetworkreplay.AssetDetailDto" extends="esbAssetDetail">
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
        <result column="REQUEST_TIME" property="requestTime" javaType="java.lang.String"/>
        <result column="RESPONSE_TIME" property="responseTime" javaType="java.lang.String"/>
        <result column="CHANNEL_ID" property="channelId" javaType="java.lang.String"/>
        <result column="URL" property="url" javaType="java.lang.String"/>
        <result column="TRANS_CDE" property="transCde" javaType="java.lang.String"/>
        <result column="CSMR_ID" property="csmrId" javaType="java.lang.String"/>
        <result column="VERSION_NUMBER" property="versionNumber" javaType="java.lang.String"/>
        <result column="ESB_RESP_CODE" property="esbRespCode" javaType="java.lang.String"/>
        <result column="ESB_RESP_MSG" property="esbRespMsg" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO ESB_ASSET_DETAIL (ASSET_ID, REQUEST_BODY, RESPONSE_BODY)
        VALUES (#{assetId}, #{requestBody}, #{responseBody})
    </insert>

    <delete id="deleteById">
        DELETE FROM ESB_ASSET_DETAIL WHERE ASSET_ID = #{assetId}
    </delete>

    <select id="selectFieldById" resultMap="esbAssetDetail">
        SELECT
        <foreach collection="fieldList" item="field" separator=",">${field}</foreach>
        FROM ESB_ASSET_DETAIL WHERE ID = #{id}
    </select>

    <select id="selectByInterfaceIdAndVersionNumber" resultMap="assetDetailDto">
        SELECT t1.*, t2.REQUEST_BODY, t2.RESPONSE_BODY
        FROM (SELECT * FROM ESB_ASSET_INFO WHERE INTERFACE_ID = #{interfaceId} AND VERSION_NUMBER = #{versionNumber}) t1
                 INNER JOIN ESB_ASSET_DETAIL t2 ON t1.ID = t2.ID
    </select>
</mapper>