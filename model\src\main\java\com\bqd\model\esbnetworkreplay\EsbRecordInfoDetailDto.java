package com.bqd.model.esbnetworkreplay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: 指定接口录制信息 详情页数据dto
 * @Author: wang<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class EsbRecordInfoDetailDto extends EsbRecordInfo {
    private String requestBody;
    private String responseBody;
}
