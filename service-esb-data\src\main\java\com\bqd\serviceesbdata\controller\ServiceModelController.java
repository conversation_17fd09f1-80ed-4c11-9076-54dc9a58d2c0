package com.bqd.serviceesbdata.controller;

import cn.hutool.core.util.StrUtil;
import com.bqd.base.response.Response;
import com.bqd.serviceesbdata.service.ServiceModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-13
 */
@RestController
@RequestMapping("/serviceModel")
public class ServiceModelController {

    @Autowired
    private ServiceModelService serviceModelService;

    @GetMapping("/getByInterfaceId")
    public Response getByInterfaceId(@RequestParam("interfaceId") String interfaceId) {
        return Response.success(serviceModelService.getByInterfaceId(interfaceId));
    }

    /**
     * 获取指定字段的服务模型值
     * @param field
     * @return
     */
    @GetMapping("/getServiceModelFiledValue")
    public Response getServiceModelFiledValue(@RequestParam("field") String field) {
        List<String> fieldList = StrUtil.split(field, ",").stream().map(String::trim).collect(Collectors.toList());
        return Response.success(serviceModelService.getServiceModelFiledValue(fieldList));
    }

}
