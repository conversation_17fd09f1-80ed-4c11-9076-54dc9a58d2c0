package com.bqd.model.esbnetworkreplay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: 录制信息页 表格数据dto
 * @Author: wang<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class EsbRecordInfoDto extends EsbRecordInfo {
    private Long reqtStartTime;
    private Long reqtEndTime;
}
