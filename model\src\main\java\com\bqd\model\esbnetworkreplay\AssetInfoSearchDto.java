package com.bqd.model.esbnetworkreplay;

import com.bqd.model.common.OraclePagedCommonParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssetInfoSearchDto extends OraclePagedCommonParam {
    private String interfaceId;
    private String subjectDomain;
    private String serviceProvider;
    private String interfaceType;
    private String startTime;
    private String endTime;
}
