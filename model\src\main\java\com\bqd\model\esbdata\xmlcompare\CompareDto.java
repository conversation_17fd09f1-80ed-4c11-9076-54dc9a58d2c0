package com.bqd.model.esbdata.xmlcompare;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-12-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompareDto {
    private String xml1;
    private String xml2;
    private List<String> ignoreField;
}
