package com.bqd.dboraclealpha.esbnetworkreplay.mapper;

import com.bqd.model.esbnetworkreplay.RequestResendDBInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface RequestResendDbInfoMapper {
    List<RequestResendDBInfo> selectAllOdrByTimeDesc();

    void insert(RequestResendDBInfo requestResendDBInfo);

    void updateById(RequestResendDBInfo requestResendDBInfo);

    void deleteById(String id);

    RequestResendDBInfo selectById(String id);
}
