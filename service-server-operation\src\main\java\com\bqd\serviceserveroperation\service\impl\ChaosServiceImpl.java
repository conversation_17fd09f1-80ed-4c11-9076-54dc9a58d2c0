package com.bqd.serviceserveroperation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.base.rpc.chaos.ChaosExecutingInfoMapperRpc;
import com.bqd.base.rpc.chaos.ChaosServerMapperRpc;
import com.bqd.model.chaos.*;
import com.bqd.model.common.CommonReqtDto;
import com.bqd.model.common.PageDto;
import com.bqd.model.servermanagement.ServerInfo;
import com.bqd.base.rpc.serveroperation.ServerInfoMapper;
import com.bqd.serviceserveroperation.service.ChaosService;
import com.bqd.serviceserveroperation.utils.ServerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-05
 */
@Service
@Slf4j
public class ChaosServiceImpl implements ChaosService {

    @Autowired
    private ServerInfoMapper serverInfoMapper;
    @Autowired
    private ChaosExecutingInfoMapperRpc chaosExecutingInfoMapperRpc;
    @Autowired
    private ChaosServerMapperRpc chaosServerMapperRpc;

    /**
     * 添加chaos服务器
     *
     * @param serverInfoId 服务器信息id
     */
    @Override
    public void addChaosServer(String serverInfoId) {
        ServerInfo serverInfo = getServerInfoById(serverInfoId);
        if (!serverInfo.getUsername().equalsIgnoreCase("root")) {
            throw new CustomizedException("请使用root用户");
        }
        chaosServerMapperRpc.insert(new ChaosServer(serverInfoId, -1));
    }

    /**
     * 获取chaos服务器列表
     *
     * @param pageNo         页码
     * @param pageSize       每页大小
     * @param chaosServerDto 查询条件
     * @return 分页结果
     */
    @Override
    public PageDto<ChaosServerDto> chaosServerList(Integer pageNo, Integer pageSize, ChaosServerDto chaosServerDto) {
        int totalCount = chaosServerMapperRpc.countDto(chaosServerDto);
        int startRow = PageUtil.getStart(pageNo, pageSize) + 1;
        int endRow = PageUtil.getEnd(pageNo, pageSize);
        List<ChaosServerDto> chaosServerDtoList = chaosServerMapperRpc.selectDtoByCondition(startRow, endRow, chaosServerDto);
        return new PageDto<>(pageNo, pageSize, PageUtil.totalPage(totalCount, pageSize), totalCount, chaosServerDtoList);
    }

    /**
     * 下载chaos脚本
     *
     * @param scriptName 脚本名称
     * @return 脚本输入流
     */
    @Override
    public InputStream downloadScript(String scriptName) {
        try {
            ClassPathResource classPathResource = new ClassPathResource("files/chaos/" + scriptName);
            return classPathResource.getInputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 设置chaos服务器
     *
     * @param serverInfoId 服务器信息id
     */
    @Override
    public void setup(String serverInfoId) {
        ServerInfo serverInfo = getServerInfoById(serverInfoId);
        List<String> commandList = new ArrayList<>();
        commandList.add("cd /root");
        commandList.add("rm -rf chaos-scripts");
        commandList.add("mkdir chaos-scripts");
        commandList.add("cd chaos-scripts");
        commandList.add("wget http://**************:25999/api/service/server-operation/chaos/downloadScripts?scriptName=chaos-1.7.2.tar.gz -O chaos-1.7.2.tar.gz");
        commandList.add("tar -zxvf chaos-1.7.2.tar.gz");
        commandList.add("/root/chaos-scripts/chaosblade-1.7.2/start-chaos-server.sh");
        ServerUtils.executeCommand(serverInfo, ServerUtils.buildCommand(commandList));
    }

    /**
     * 获取正在执行的chaos列表
     *
     * @param type 类型
     * @return 正在执行的chaos列表
     */
    @Override
    public List<ChaosExecutingInfoDto> getExecutingList(String type) {
        long currentSeconds = DateUtil.currentSeconds();
        ChaosExecutingInfo chaosExecutingInfo = new ChaosExecutingInfo(null, type, null, null, null, currentSeconds, null, null);
        return chaosExecutingInfoMapperRpc.selectByCondition(chaosExecutingInfo).stream().map(item -> {
            ChaosExecutingInfoDto chaosExecutingInfoDto = BeanUtil.copyProperties(item, ChaosExecutingInfoDto.class);
            chaosExecutingInfoDto.setSecondsRemain((int) (chaosExecutingInfoDto.getEstEndTime() - currentSeconds));
            chaosExecutingInfoDto.setStartTimeString(DateUtil.date(chaosExecutingInfoDto.getStartTime() * 1000).toString());
            chaosExecutingInfoDto.setEstEndTimeString(DateUtil.date(chaosExecutingInfoDto.getEstEndTime() * 1000).toString());
            return chaosExecutingInfoDto;
        }).collect(Collectors.toList());
    }

    /**
     * 获取默认网卡名称
     *
     * @param serverInfoId 服务器信息id
     * @return 默认网卡名称
     */
    @Override
    public String getDefaultNicName(String serverInfoId) {
        ServerInfo serverInfo = getServerInfoById(serverInfoId);
        return ServerUtils.executeCommand(serverInfo, "ip -o -4 addr show | awk -v ip=" + serverInfo.getServerIp() + " '$4 ~ ip {print $2}'").trim();
    }

    /**
     * 注入chaos
     *
     * @param chaosInjectionDto 注入参数
     */
    @Override
    public void injection(ChaosInjectionDto chaosInjectionDto) {
        ServerInfo serverInfo = getServerInfoById(chaosInjectionDto.getServerInfoId());
        JSONObject createResp = sendChaosCommand(serverInfo.getServerIp(), chaosBladeCmdBuilder(chaosInjectionDto.getChaosCommandPrefix(), chaosInjectionDto.getParamMap()));
        if (!StrUtil.equals(createResp.getStr("code"), "200")) {
            throw new CustomizedException("执行失败", "resp: " + createResp);
        }
        String id = createResp.getStr("result");
        //执行成功，查询状态获取实际执行时间
        JSONObject statusResp = sendChaosCommand(serverInfo.getServerIp(), "status " + id);
        long startTime = DateUtil.parse(statusResp.getJSONObject("result").getStr("CreateTime")).getTime() / 1000;
        long duration = Long.parseLong(chaosInjectionDto.getParamMap().get("--timeout"));
        long estEndTime = startTime + duration;
        ChaosExecutingInfo chaosExecutingInfo = new ChaosExecutingInfo(id, chaosInjectionDto.getChaosType(), serverInfo.getServerIp(), startTime, duration, estEndTime, chaosInjectionDto.getTargetInfo(), chaosInjectionDto.getServerInfoId());
        chaosExecutingInfoMapperRpc.insert(chaosExecutingInfo);
    }

    /**
     * 检查chaos服务器状态
     *
     * @param serverInfoId 服务器信息id
     * @return 状态
     */
    @Override
    public int checkStatus(String serverInfoId) {
        ServerInfo serverInfo = getServerInfoById(serverInfoId);
        try {
            JSONObject versionResp = sendChaosCommand(serverInfo.getServerIp(), "version");
            if (StrUtil.equals(versionResp.getStr("code"), "200")) {
                chaosServerMapperRpc.updateById(new ChaosServer(serverInfoId, 1));
                return 1;
            }
            chaosServerMapperRpc.updateById(new ChaosServer(serverInfoId, 0));
            return 0;
        } catch (Exception e) {
            chaosServerMapperRpc.updateById(new ChaosServer(serverInfoId, 0));
            return 0;
        }
    }

    /**
     * 销毁chaos
     *
     * @param id 执行id
     */
    @Override
    public void destroy(String id) {
        ChaosExecutingInfo chaosExecutingInfo = chaosExecutingInfoMapperRpc.selectByCondition(new ChaosExecutingInfo(id, null, null, null, null, null, null, null)).get(0);
        if (StrUtil.equals(chaosExecutingInfo.getChaosType(), "http-oneway-loss")) {
            destroyHttpOnewayLoss(chaosExecutingInfo);
            return;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("destroy", id);
        JSONObject resp = sendChaosCommand(chaosExecutingInfo.getServerIp(), chaosBladeCmdBuilder("", paramMap));
        //返回代码不为200，说明销毁失败
        if (!StrUtil.equals(resp.getStr("code"), "200")) {
            throw new CustomizedException("故障销毁失败", "resp: " + resp);
        }
        chaosExecutingInfo.setEstEndTime((long) -1);
        chaosExecutingInfoMapperRpc.updateById(chaosExecutingInfo);
    }

    /**
     * 设置http单向丢包
     *
     * @param commonReqtDto 请求参数
     */
    @Override
    public void httpOnewayLoss(CommonReqtDto commonReqtDto) {
        ServerInfo serverInfo = getServerInfoById(commonReqtDto.getParam1());
        String remoteIp = commonReqtDto.getParam2();
        String remotePort = commonReqtDto.getParam3();
        ServerUtils.executeCommand(serverInfo, StrUtil.format("iptables -A OUTPUT -p tcp -d {} --dport {} --tcp-flags SYN SYN -j ACCEPT", remoteIp, remotePort));
        ServerUtils.executeCommand(serverInfo, StrUtil.format("iptables -A INPUT -p tcp -s {} --sport {} --tcp-flags SYN,ACK SYN,ACK -j ACCEPT", remoteIp, remotePort));
        ServerUtils.executeCommand(serverInfo, "iptables -A INPUT -s " + remoteIp + " -p tcp --sport " + remotePort + " -m state --state ESTABLISHED -j DROP");
        ChaosExecutingInfo chaosExecutingInfo = new ChaosExecutingInfo(IdUtil.nanoId(16).toLowerCase(), "http-oneway-loss", serverInfo.getServerIp(), DateUtil.currentSeconds(), -1L, 9999999999L, StrUtil.format("请求{}:{}响应丢包100%", remoteIp, remotePort), serverInfo.getId());
        chaosExecutingInfoMapperRpc.insert(chaosExecutingInfo);
    }

    private void destroyHttpOnewayLoss(ChaosExecutingInfo chaosExecutingInfo) {
        String serverInfoId = chaosExecutingInfo.getServerInfoId();
        ServerInfo serverInfo = getServerInfoById(serverInfoId);
        String[] ipPort = extractIpPort(chaosExecutingInfo.getTargetInfo());
        if (ipPort == null) {
            throw new CustomizedException("ip或端口号异常");
        }
        ServerUtils.executeCommand(serverInfo, StrUtil.format("iptables -D OUTPUT -p tcp -d {} --dport {} --tcp-flags SYN SYN -j ACCEPT", ipPort[0], ipPort[1]));
        ServerUtils.executeCommand(serverInfo, StrUtil.format("iptables -D INPUT -p tcp -s {} --sport {} --tcp-flags SYN,ACK SYN,ACK -j ACCEPT", ipPort[0], ipPort[1]));
        ServerUtils.executeCommand(serverInfo, "iptables -D INPUT -s " + ipPort[0] + " -p tcp --sport " + ipPort[1] + " -m state --state ESTABLISHED -j DROP");
        chaosExecutingInfo.setEstEndTime((long) -1);
        chaosExecutingInfoMapperRpc.updateById(chaosExecutingInfo);
    }

    // 从字符串中提取 IP:Port 信息
    private String[] extractIpPort(String input) {
        // 正则表达式：匹配 IP:Port 格式，例如 "0.0.0.0:8080"
        String regex = "(\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}):(\\d+)";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            String ip = matcher.group(1);
            String portStr = matcher.group(2);

            // 可选：检查端口是否在合法范围内（1~65535）
            int port;
            try {
                port = Integer.parseInt(portStr);
                if (port < 1 || port > 65535) {
                    return null; // 端口不合法
                }
            } catch (NumberFormatException e) {
                return null; // 端口不是数字
            }

            return new String[]{ip, String.valueOf(port)};
        }

        return null; // 没有找到匹配项
    }

    /**
     * 获取服务器信息
     *
     * @param serverInfoId 服务器信息id
     * @return 服务器信息
     */
    private ServerInfo getServerInfoById(String serverInfoId) {
        ServerInfo serverInfo = new ServerInfo();
        serverInfo.setId(serverInfoId);
        List<ServerInfo> serverInfoList = serverInfoMapper.selectByCondition(serverInfo);
        if (serverInfoList.size() != 1) {
            throw new CustomizedException("服务器信息错误");
        }
        if (!serverInfoList.get(0).getUsername().equalsIgnoreCase("root")) {
            throw new CustomizedException("必须以root用户登录");
        }
        return serverInfoList.get(0);
    }

    /**
     * 构建chaos命令
     *
     * @param cmdPrefix 命令前缀
     * @param paramMap  参数
     * @return 命令
     */
    private static String chaosBladeCmdBuilder(String cmdPrefix, Map<String, String> paramMap) {
        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            if (StrUtil.isBlank(entry.getValue())) {
                continue;
            }
            stringBuilder.append(" ").append(entry.getKey()).append(" ").append(entry.getValue());
        }
        return stringBuilder.insert(0, cmdPrefix).toString();
    }

    /**
     * 发送chaos命令
     *
     * @param serverIp 服务器ip
     * @param command  命令
     * @return 响应
     */
    private JSONObject sendChaosCommand(String serverIp, String command) {
        log.info("发送chaos命令：{}", command);
        String resp = HttpUtil.get("http://" + serverIp + ":32917" + "/chaosblade?cmd=" + command);
        log.info("chaos命令响应：{}", resp);
        return JSONUtil.parseObj(resp);
    }

    @Override
    public void deleteById(String id) {
        ServerInfo serverInfo = getServerInfoById(id);
        stopAndRemoveScript(serverInfo);
        chaosServerMapperRpc.deleteById(id);
    }

    private void stopAndRemoveScript(ServerInfo serverInfo) {
        ServerUtils.executeCommand(serverInfo, "kill -9 $(ps -ef | grep chaosblade-1.7.2/blade | grep -v grep | awk '{print $2}')");
        ServerUtils.executeCommand(serverInfo, "rm -rf /root/chaos-scripts/");
    }

}
