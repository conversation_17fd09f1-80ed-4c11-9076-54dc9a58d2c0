package com.bqd.dboraclealpha.esbnetworkreplay.mapper;

import com.bqd.model.esbnetworkreplay.EsbRecordDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EsbRecordDetailMapper {
    EsbRecordDetail selectByIdAndColName(@Param("recordId") String recordId, @Param("colNameList") List<String> colNameList);

    void insert(EsbRecordDetail esbRecordDetail);

    void deleteById(String recordId);

    EsbRecordDetail selectById(String recordId);
}
