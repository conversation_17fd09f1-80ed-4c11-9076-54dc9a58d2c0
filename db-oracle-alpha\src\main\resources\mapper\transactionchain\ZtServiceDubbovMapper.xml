<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.transactionchain.mapper.ZtServiceDubbovMapper">
    <resultMap id="serviceDubbov" type="com.bqd.model.transactionchain.ZtServiceDubbov">
        <result property="serviceRef" column="SERVICE_REF"/>
        <result property="serviceGroup" column="SERVICE_GROUP"/>
        <result property="syscode" column="SYSCODE"/>
        <result property="beanRef" column="BEAN_REF"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="serviceName" column="SERVICE_NAME"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO SERVICE_DUBBOV (SERVICE_REF, SERVICE_GROUP, SYSCODE, BEAN_REF, DESCRIPTION, SERVICE_NAME)
        VALUES (#{serviceRef}, #{serviceGroup}, #{syscode}, #{beanRef}, #{description}, #{serviceName})
    </insert>

    <delete id="truncateTable">
        TRUNCATE TABLE SERVICE_DUBBOV
    </delete>

    <select id="selectByServiceGroup" resultMap="serviceDubbov">
        SELECT *
        FROM SERVICE_DUBBOV
        WHERE SERVICE_GROUP = #{serviceGroup}
    </select>

    <select id="selectByLikeServiceGroup" resultMap="serviceDubbov">
        SELECT *
        FROM SERVICE_DUBBOV
        WHERE SERVICE_GROUP LIKE CONCAT('%', CONCAT(#{serviceGroup}, '%'))
    </select>

    <select id="selectByLikeDescription" resultMap="serviceDubbov">
        SELECT *
        FROM SERVICE_DUBBOV
        WHERE DESCRIPTION LIKE CONCAT('%', CONCAT(#{description}, '%'))
    </select>

    <select id="selectByBeanRefAndServiceName" resultMap="serviceDubbov">
        SELECT *
        FROM SERVICE_DUBBOV
        WHERE BEAN_REF = #{beanRef}
          AND SERVICE_NAME = #{serviceName}
    </select>
</mapper>