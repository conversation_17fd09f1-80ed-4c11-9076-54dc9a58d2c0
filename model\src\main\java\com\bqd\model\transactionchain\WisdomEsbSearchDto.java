package com.bqd.model.transactionchain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WisdomEsbSearchDto {
    private String interfaceId;
    private String interfaceName;
    private List<ServiceInfo> serviceInfoList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceInfo{
        private String serviceGroup;
        private String description;
        private List<IntegrateServiceInfo> integrateServiceInfoList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IntegrateServiceInfo {
        private String mvcId;
        private String description;
        private String bId;
        private List<TradeNameInfo> tradeNameInfoList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradeNameInfo {
        private String tradeName;
        private String filePath;
        private String integrateCallFrom;
    }
}
