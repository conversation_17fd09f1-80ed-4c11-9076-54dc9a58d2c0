package com.bqd.serviceenr.service;

import com.bqd.model.esbnetworkreplay.EsbDeduplicateIgnoreDto;

import java.util.List;

public interface DeduplicateService {
    List<String> fetchIgnoredField(String interfaceId);

    void updateIgnoredField(EsbDeduplicateIgnoreDto esbDeduplicateIgnoreDto);

    List<String> fetchIgnoredFieldGlobal();

    void addIgnoredFieldGlobal(String ignoredField);

    void deleteIgnoredFieldGlobal(String ignoredField);

    void deduplicate(String interfaceId);

    void internalDeduplicate(String interfaceId);
}
