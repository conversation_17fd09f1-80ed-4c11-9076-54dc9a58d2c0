package com.bqd.integratedtester.sibstranscheck.service.impl;

import cn.hutool.json.JSONObject;
import com.bqd.integratedtester.sibstranscheck.utils.SibsTransCheckUtils;
import org.dom4j.Element;

import java.util.HashSet;
import java.util.Set;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-05-11
 */
public class SibsRespCheck extends SibsXmlJsonCheckAbstract {

    public SibsRespCheck(String requestXml, String requestJson) {
        super(requestXml, requestJson);
    }

    public boolean appHeadCheck() {
        return super.appHeadCheck("响应");
    }

    public boolean sysHeadCheck(Set<String> sysHeadCompareIgnoreSet) {
        Element appHdr = xml.element("appHdr").createCopy();
        SibsTransCheckUtils.xmlRemoveEmptyTag(appHdr);
        appHdr.remove(appHdr.element("totalNum"));
        appHdr.remove(appHdr.element("pgupOrPgdn"));
        appHdr.remove(appHdr.element("totalRows"));
        appHdr.remove(appHdr.element("totalFlag"));
        appHdr.remove(appHdr.element("currentNum"));
        JSONObject convertedJsonObject = SibsTransCheckUtils.xmlToJsonObject(appHdr.asXML(), "appHdr");
        HashSet<String> objectToArrayTagsSet = new HashSet<>();
        objectToArrayTagsSet.add("ret");
        objectToArrayTagsSet.add("confirmRet");
        SibsTransCheckUtils.jsonObjectToArray(convertedJsonObject, objectToArrayTagsSet);

        JSONObject sysHead = json.getJSONObject("sysHead");
        sysHead.remove("innerCallFlag"); //all
        sysHead.remove("finalInnerflag"); //all
        sysHead.remove("messageType"); //all
        sysHead.remove("messageCode"); //all
        sysHead.remove("innerServiceFlag"); //all
        sysHead.remove("programId"); //all
        sysHead.remove("branchPermissionsFlag"); //all
        sysHead.remove("serviceCode"); //all
        sysHead.remove("prvdSysId"); //all? 600016

        for (String ignoreTag : sysHeadCompareIgnoreSet){
            convertedJsonObject.remove(ignoreTag);
            sysHead.remove(ignoreTag);
        }
        boolean result = SibsTransCheckUtils.jsonIsEqual(convertedJsonObject.toString(), sysHead.toString(), this);
        if (!result){
            errMsg += "<b>[sysHead校验] 响应sysHead校验失败</b><br/>";
        }
        return result;
    }

    public boolean bodyCheck(boolean jsonRemoveEmptyTag, Set<String> objectToArrayTagsSet, Set<String> ignoreTagsSet) {
        return super.bodyCheck("响应", jsonRemoveEmptyTag, objectToArrayTagsSet, ignoreTagsSet);
    }

}