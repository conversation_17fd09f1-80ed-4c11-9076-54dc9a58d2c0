package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbReplayPlanMapper;
import com.bqd.model.esbnetworkreplay.EsbReplayPlan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/enr/esbReplayPlan")
public class EsbReplayPlanController {

    @Autowired
    private EsbReplayPlanMapper esbReplayPlanMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody EsbReplayPlan esbReplayPlan) {
        esbReplayPlanMapper.insert(esbReplayPlan);
    }

    @GetMapping("/selectAll")
    public List<EsbReplayPlan> selectAll() {
        return esbReplayPlanMapper.selectAll();
    }

    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String id) {
        esbReplayPlanMapper.deleteById(id);
    }

}
