package com.bqd.model.transactionchain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WisdomTradeNameSearchDto {
    private String tradeName;
    private String filePath;
    private List<IntegrateServiceInfo> integrateServiceInfoList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IntegrateServiceInfo {
        private String operationType;
        private String description;
        private String bId;
        private String callFrom;
        private List<ServiceInfo> serviceInfoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ServiceInfo {
        private String serviceName;
        private String serviceGroup;
        private String description;
        private String beanRef;
        private List<ESBInfo> esbInfoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ESBInfo {
        private String interfaceId;
        private String interfaceName;
    }
}
