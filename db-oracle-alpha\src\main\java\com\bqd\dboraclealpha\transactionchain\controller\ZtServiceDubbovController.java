package com.bqd.dboraclealpha.transactionchain.controller;

import com.bqd.dboraclealpha.transactionchain.mapper.ZtServiceDubbovMapper;
import com.bqd.model.transactionchain.ZtServiceDubbov;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
@RestController
@RequestMapping("/transactionChain/zt/dubbov")
public class ZtServiceDubbovController implements com.bqd.base.rpc.transactionchain.ZtServiceDubbovMapper {

    @Autowired
    private ZtServiceDubbovMapper ztServiceDubbovMapper;

    @Override
    @PostMapping("/insert")
    public void insert(@RequestBody ZtServiceDubbov serviceDubbov) {
        ztServiceDubbovMapper.insert(serviceDubbov);
    }

    @Override
    @GetMapping("/selectByServiceGroup")
    public ZtServiceDubbov selectByServiceGroup(@RequestParam String serviceGroup) {
        return ztServiceDubbovMapper.selectByServiceGroup(serviceGroup);
    }

    @Override
    @GetMapping("/selectByLikeServiceGroup")
    public List<ZtServiceDubbov> selectByLikeServiceGroup(@RequestParam String serviceGroup) {
        return ztServiceDubbovMapper.selectByLikeServiceGroup(serviceGroup);
    }

    @Override
    @GetMapping("/selectByLikeDescription")
    public List<ZtServiceDubbov> selectByLikeDescription(@RequestParam String description) {
        return ztServiceDubbovMapper.selectByLikeDescription(description);
    }

    @Override
    @GetMapping("/selectByBeanRefAndServiceName")
    public ZtServiceDubbov selectByBeanRefAndServiceName(@RequestParam String beanRef, @RequestParam String serviceName) {
        return ztServiceDubbovMapper.selectByBeanRefAndServiceName(beanRef, serviceName);
    }
}
