<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbReplaceFieldMapper">

    <resultMap id="esbReplaceVariable" type="com.bqd.model.esbnetworkreplay.EsbReplaceField">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
        <result column="TAG" property="tag" javaType="java.lang.String"/>
        <result column="VALUE" property="value" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectByInterfaceId" resultMap="esbReplaceVariable">
        SELECT *
        FROM ESB_REPLACE_FIELD
        WHERE INTERFACE_ID = #{interfaceId}
    </select>

    <delete id="deleteById">
        DELETE
        FROM ESB_REPLACE_FIELD
        WHERE ID = #{id}
    </delete>

    <insert id="insert">
        INSERT INTO ESB_REPLACE_FIELD (ID, INTERFACE_ID, TAG, VALUE)
        VALUES (#{id}, #{interfaceId}, #{tag}, #{value})
    </insert>

</mapper>