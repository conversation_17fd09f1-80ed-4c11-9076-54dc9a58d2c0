package com.bqd.integratedtester.authoritypacket.service;

import com.bqd.model.authoritypacket.GeneratePacketDto;
import com.bqd.model.authoritypacket.GeneratePacketLLMDto;
import com.bqd.model.authoritypacket.PacketCommonInfo;
import com.bqd.model.authoritypacket.XmlStructureQueryDto;
import com.bqd.model.common.XmlTreeDto;

import java.io.File;
import java.io.InputStream;
import java.util.List;
import java.util.function.Consumer;

public interface AuthorityPacketService {
    /**
     * 根据模板文件名，获取并解析xml，返回xml树结构
     * @param xmlStructureQueryDto
     * @return
     */
    XmlTreeDto getXmlStructureTree(XmlStructureQueryDto xmlStructureQueryDto);

    /**
     * 生成报文包
     * @param generatePacketDto
     * @return
     */
    String generatePacket(GeneratePacketDto generatePacketDto, Consumer<File> consumer);

    /**
     * 生成报文包 - 高法
     * @param generatePacketDto
     * @return
     */
    String generatePacketGf(GeneratePacketDto generatePacketDto);

    /**
     * 获取下载报文包的输入流
     * @param packetZipName
     */
    InputStream downloadZipPacket(String packetZipName);

    /**
     * 通过ftp上传报文包
     * @param packetZipName
     * @param remotePath
     */
    void ftpUpload(String packetZipName, String remotePath);

    /**
     * 保存常用信息
     * @param packetCommonInfo
     */
    void saveCommonInfo(PacketCommonInfo packetCommonInfo);

    /**
     * 获取常用信息
     * @param packetCommonInfo
     * @return
     */
    List<PacketCommonInfo> getCommonInfo(PacketCommonInfo packetCommonInfo);

    /**
     * 修改常用信息
     * @param packetCommonInfo
     */
    void editCommonInfo(PacketCommonInfo packetCommonInfo);

    /**
     * 删除常用信息
     * @param id
     */
    void deleteCommonInfo(String id);

    /**
     * 由llm生成xml报文
     * @param generatePacketLLMDto
     * @return
     */
    String llmGenerate(GeneratePacketLLMDto generatePacketLLMDto);
}
