<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.projectmanagement.mapper.GitCredentialMapper">

    <resultMap id="gitCredential" type="com.bqd.model.projectmanagement.GitCredential">
        <id column="ID" property="id" javaType="String"/>
        <result column="NAME" property="name" javaType="String"/>
        <result column="DESCRIPTION" property="description" javaType="String"/>
        <result column="USERNAME" property="username" javaType="String"/>
        <result column="PASSWORD" property="password" javaType="String"/>
    </resultMap>

    <select id="selectByCondition" resultMap="gitCredential">
        SELECT * FROM GIT_CREDENTIAL WHERE 1=1
        <if test="id != null and id.length() != 0">
            AND ID = #{id}
        </if>
        <if test="name != null and name.length() != 0">
            AND NAME = #{name,jdbcType=VARCHAR}
        </if>
        <if test="description != null and description.length() != 0">
            AND DESCRIPTION = #{description,jdbcType=VARCHAR}
        </if>
        <if test="username != null and username.length() != 0">
            AND USERNAME = #{username,jdbcType=VARCHAR}
        </if>
        <if test="password != null and password.length() != 0">
            AND PASSWORD = #{password,jdbcType=VARCHAR}
        </if>
    </select>

    <insert id="insert">
        INSERT INTO GIT_CREDENTIAL(ID, NAME, DESCRIPTION, USERNAME, PASSWORD)
        VALUES (#{id}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR})
    </insert>

    <delete id="deleteById">
        DELETE
        FROM GIT_CREDENTIAL
        WHERE ID = #{id}
    </delete>

    <update id="updateById">
        UPDATE GIT_CREDENTIAL
        SET NAME        = #{name,jdbcType=VARCHAR},
            DESCRIPTION = #{description,jdbcType=VARCHAR},
            USERNAME    = #{username,jdbcType=VARCHAR},
            PASSWORD    = #{password,jdbcType=VARCHAR}
        WHERE ID = #{id}
    </update>
</mapper>