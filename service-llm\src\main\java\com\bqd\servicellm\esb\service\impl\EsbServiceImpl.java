package com.bqd.servicellm.esb.service.impl;

import com.bqd.base.rpc.esbdata.EsbServiceModelMapper;
import com.bqd.model.esbdata.EsbServiceModel;
import com.bqd.servicellm.esb.service.EsbService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-21
 */
@Service
public class EsbServiceImpl implements EsbService {

    @Autowired
    private EsbServiceModelMapper esbServiceModelMapper;

    @Override
    public List<EsbServiceModel> serviceModel(EsbServiceModel esbServiceModel) {
        return esbServiceModelMapper.selectByCondition(esbServiceModel);
    }
}
