<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbRecordInfoMapper">

    <resultMap id="esbRecordInfo" type="com.bqd.model.esbnetworkreplay.EsbRecordInfo">
        <id column="RECORD_ID" property="recordId" javaType="java.lang.String"/>
        <result column="HOST" property="host" javaType="java.lang.String"/>
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
        <result column="REQUEST_TIME" property="requestTime" javaType="java.lang.Long"/>
        <result column="RESPONSE_TIME" property="responseTime" javaType="java.lang.Long"/>
        <result column="SVC_CORR_ID" property="svcCorrId" javaType="java.lang.String"/>
        <result column="CHANNEL_ID" property="channelId" javaType="java.lang.String"/>
        <result column="TRANS_CDE" property="transCde" javaType="java.lang.String"/>
        <result column="CSMR_ID" property="csmrId" javaType="java.lang.String"/>
        <result column="VERSION_NUMBER" property="versionNumber" javaType="java.lang.String"/>
        <result column="ESB_RESP_MSG" property="esbRespMsg" javaType="java.lang.String"/>
        <result column="ESB_RESP_CODE" property="esbRespCode" javaType="java.lang.String"/>
        <result column="PROVIDER_ID" property="providerId" javaType="java.lang.String"/>
    </resultMap>

    <resultMap id="esbRecordInfoDetailDto" type="com.bqd.model.esbnetworkreplay.EsbRecordInfoDetailDto" extends="esbRecordInfo">
        <result column="REQUEST_BODY" property="requestBody" javaType="java.lang.String"/>
        <result column="RESPONSE_BODY" property="responseBody" javaType="java.lang.String"/>
    </resultMap>

    <resultMap id="enrCommonRespDto" type="com.bqd.model.esbnetworkreplay.ENRCommonRespDto">
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
        <result column="INTERFACE_NAME" property="interfaceName" javaType="java.lang.String"/>
        <result column="SERVICE_PROVIDER" property="serviceProvider" javaType="java.lang.String"/>
        <result column="SUBJECT_DOMAIN" property="subjectDomain" javaType="java.lang.String"/>
        <result column="INTERFACE_TYPE" property="interfaceType" javaType="java.lang.String"/>
    </resultMap>

    <resultMap id="recordInfoDto" type="com.bqd.model.esbnetworkreplay.EsbRecordInfoDto"
               extends="enrCommonRespDto">
        <result column="COUNT_NUM" property="recordCount" javaType="java.lang.String"/>
    </resultMap>

    <resultMap id="recordSearchFilterDto" type="com.bqd.model.esbnetworkreplay.RecordSearchFilterDto"
               extends="enrCommonRespDto">
        <result column="SVC_CORR_ID" property="svcCorrId" javaType="java.lang.String"/>
        <result column="CHANNEL_ID" property="channelId" javaType="java.lang.String"/>
        <result column="TRANS_CDE" property="transCde" javaType="java.lang.String"/>
        <result column="CSMR_ID" property="csmrId" javaType="java.lang.String"/>
        <result column="VERSION_NUMBER" property="versionNumber" javaType="java.lang.String"/>
        <result column="ESB_RESP_MSG" property="esbRespMsg" javaType="java.lang.String"/>
        <result column="ESB_RESP_CODE" property="esbRespCode" javaType="java.lang.String"/>
        <result column="PROVIDER_ID" property="providerId" javaType="java.lang.String"/>
    </resultMap>

    <resultMap id="recordInfoDetailDto" type="com.bqd.model.esbnetworkreplay.EsbRecordInfoDetailDto">
        <id column="ID" property="recordInfoId" javaType="java.lang.String"/>
        <result column="INTERFACE_ID" property="interfaceId" javaType="java.lang.String"/>
        <result column="URL" property="url" javaType="java.lang.String"/>
        <result column="CSMR_ID" property="csmrId" javaType="java.lang.String"/>
        <result column="VERSION_NUMBER" property="versionNumber" javaType="java.lang.String"/>
        <result column="TRANS_CDE" property="transCde" javaType="java.lang.String"/>
        <result column="CHANNEL_ID" property="channelId" javaType="java.lang.String"/>
        <result column="ESB_RESP_MSG" property="esbRespMsg" javaType="java.lang.String"/>
        <result column="ESB_RESP_CODE" property="esbRespCode" javaType="java.lang.String"/>
        <result column="REQUEST_TIME" property="requestTime" javaType="java.lang.String"/>
        <result column="RESPONSE_TIME" property="responseTime" javaType="java.lang.String"/>
    </resultMap>

    <sql id="recordInfoInnerJoinServiceModel">
        SELECT t1.INTERFACE_ID,
        t1.COUNT_NUM,
        t2.INTERFACE_NAME,
        t2.SUBJECT_DOMAIN,
        t2.SERVICE_PROVIDER,
        t2.INTERFACE_TYPE
        FROM (SELECT INTERFACE_ID, COUNT(*) as COUNT_NUM
        FROM ESB_RECORD_INFO
        <where>
            <if test="startTime != null and startTime.length() != 0">
                REQUEST_TIME >= #{startTime}
            </if>
            <if test="endTime != null and endTime.length() != 0">
                AND #{endTime} >= REQUEST_TIME
            </if>
        </where>
        GROUP BY INTERFACE_ID) t1
        INNER JOIN ESB_SERVICE_MODEL t2 ON t1.INTERFACE_ID = t2.INTERFACE_ID
    </sql>

    <sql id="condition">
        <where>
            <if test="interfaceId != null and interfaceId.length() != 0">
                INTERFACE_ID = #{interfaceId}
            </if>
            <if test="subjectDomain != null and subjectDomain.length() != 0">
                AND SUBJECT_DOMAIN = #{subjectDomain}
            </if>
            <if test="serviceProvider != null and serviceProvider.length() != 0">
                AND SERVICE_PROVIDER = #{serviceProvider}
            </if>
            <if test="interfaceType != null and interfaceType.length() != 0">
                AND INTERFACE_TYPE = #{interfaceType}
            </if>
        </where>
    </sql>

    <select id="countByCondition" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM ESB_RECORD_INFO WHERE 1=1
        <if test="recordId != null">
            AND RECORD_ID = #{recordId}
        </if>
        <if test="host != null and host.length() != 0">
            AND HOST = #{host}
        </if>
        <if test="interfaceId != null and interfaceId.length() != 0">
            AND INTERFACE_ID = #{interfaceId}
        </if>
        <if test="reqtStartTime != null and reqtStartTime != 0">
            AND REQUEST_TIME >= #{reqtStartTime}
        </if>
        <if test="reqtEndTime != null and reqtEndTime != 0">
            AND #{reqtEndTime} >= REQUEST_TIME
        </if>
        <if test="svcCorrId != null and svcCorrId.length() != 0">
            AND SVC_CORR_ID = #{svcCorrId}
        </if>
        <if test="channelId != null and channelId.length() != 0">
            AND CHANNEL_ID = #{channelId}
        </if>
        <if test="transCde != null and transCde.length() != 0">
            AND TRANS_CDE = #{transCde}
        </if>
        <if test="csmrId != null and csmrId.length() != 0">
            AND CSMR_ID = #{csmrId}
        </if>
        <if test="versionNumber != null and versionNumber.length() != 0">
            AND VERSION_NUMBER = #{versionNumber}
        </if>
        <if test="esbRespMsg != null and esbRespMsg.length() != 0">
            AND ESB_RESP_MSG = #{esbRespMsg}
        </if>
        <if test="esbRespCode != null and esbRespCode.length() != 0">
            AND ESB_RESP_CODE = #{esbRespCode}
        </if>
        <if test="providerId != null and providerId.length() != 0">
            AND PROVIDER_ID = #{providerId}
        </if>
    </select>

    <select id="selectByCondition" resultMap="esbRecordInfo">
        SELECT * FROM (
        SELECT ROWNUM rn, t1.* FROM (
        SELECT * FROM ESB_RECORD_INFO WHERE 1=1
        <if test="recordInfoDto.recordId != null">
            AND RECORD_ID = #{recordInfoDto.recordId}
        </if>
        <if test="recordInfoDto.host != null and recordInfoDto.host.length() != 0">
            AND HOST = #{recordInfoDto.host}
        </if>
        <if test="recordInfoDto.interfaceId != null and recordInfoDto.interfaceId.length() != 0">
            AND INTERFACE_ID = #{recordInfoDto.interfaceId}
        </if>
        <if test="recordInfoDto.reqtStartTime != null and recordInfoDto.reqtStartTime != 0">
            AND REQUEST_TIME >= #{recordInfoDto.reqtStartTime}
        </if>
        <if test="recordInfoDto.reqtEndTime != null and recordInfoDto.reqtEndTime != 0">
            AND #{recordInfoDto.reqtEndTime} >= REQUEST_TIME
        </if>
        <if test="recordInfoDto.svcCorrId != null and recordInfoDto.svcCorrId.length() != 0">
            AND SVC_CORR_ID = #{recordInfoDto.svcCorrId}
        </if>
        <if test="recordInfoDto.channelId != null and recordInfoDto.channelId.length() != 0">
            AND CHANNEL_ID = #{recordInfoDto.channelId}
        </if>
        <if test="recordInfoDto.transCde != null and recordInfoDto.transCde.length() != 0">
            AND TRANS_CDE = #{recordInfoDto.transCde}
        </if>
        <if test="recordInfoDto.csmrId != null and recordInfoDto.csmrId.length() != 0">
            AND CSMR_ID = #{recordInfoDto.csmrId}
        </if>
        <if test="recordInfoDto.versionNumber != null and recordInfoDto.versionNumber.length() != 0">
            AND VERSION_NUMBER = #{recordInfoDto.versionNumber}
        </if>
        <if test="recordInfoDto.esbRespMsg != null and recordInfoDto.esbRespMsg.length() != 0">
            AND ESB_RESP_MSG = #{recordInfoDto.esbRespMsg}
        </if>
        <if test="recordInfoDto.esbRespCode != null and recordInfoDto.esbRespCode.length() != 0">
            AND ESB_RESP_CODE = #{recordInfoDto.esbRespCode}
        </if>
        <if test="recordInfoDto.providerId != null and recordInfoDto.providerId.length() != 0">
            AND PROVIDER_ID = #{recordInfoDto.providerId}
        </if>
        ORDER BY REQUEST_TIME DESC) t1)
        <if test="startRow != null and endRow != null">
            WHERE rn >= #{startRow} AND #{endRow} >= rn
        </if>
    </select>

    <select id="selectDistinctByColName" resultMap="esbRecordInfo">
        SELECT DISTINCT
        <if test="colNameList != null and colNameList.size() != 0">
            <foreach collection="colNameList" item="colName" separator=",">
                ${colName}
            </foreach>
        </if>
        FROM ESB_RECORD_INFO
    </select>

    <delete id="deleteById">
        DELETE
        FROM ESB_RECORD_INFO
        WHERE RECORD_ID = #{recordId}
    </delete>

    <select id="selectInfoDetailByCondition" resultMap="esbRecordInfoDetailDto">
        SELECT * FROM (SELECT * FROM ESB_RECORD_INFO WHERE 1=1
        <if test="recordId != null and recordId.length() != 0">
            AND RECORD_ID = #{recordId}
        </if>
        <if test="host != null and host.length() != 0">
            AND HOST = #{host}
        </if>
        <if test="interfaceId != null and interfaceId.length() != 0">
            AND INTERFACE_ID = #{interfaceId}
        </if>
        <if test="reqtStartTime != null and reqtStartTime != 0">
            AND REQUEST_TIME >= #{reqtStartTime}
        </if>
        <if test="reqtEndTime != null and reqtEndTime != 0">
            AND #{reqtEndTime} >= REQUEST_TIME
        </if>
        <if test="svcCorrId != null and svcCorrId.length() != 0">
            AND SVC_CORR_ID = #{svcCorrId}
        </if>
        <if test="channelId != null and channelId.length() != 0">
            AND CHANNEL_ID = #{channelId}
        </if>
        <if test="transCde != null and transCde.length() != 0">
            AND TRANS_CDE = #{transCde}
        </if>
        <if test="csmrId != null and csmrId.length() != 0">
            AND CSMR_ID = #{csmrId}
        </if>
        <if test="versionNumber != null and versionNumber.length() != 0">
            AND VERSION_NUMBER = #{versionNumber}
        </if>
        <if test="esbRespMsg != null and esbRespMsg.length() != 0">
            AND ESB_RESP_MSG = #{esbRespMsg}
        </if>
        <if test="esbRespCode != null and esbRespCode.length() != 0">
            AND ESB_RESP_CODE = #{esbRespCode}
        </if>
        <if test="providerId != null and providerId.length() != 0">
            AND PROVIDER_ID = #{providerId}
        </if>) t1 INNER JOIN ESB_RECORD_DETAIL t2 ON t1.RECORD_ID = t2.RECORD_ID ORDER BY t1.REQUEST_TIME DESC
    </select>







    <select id="selectFieldValue" resultMap="recordSearchFilterDto">
        SELECT DISTINCT
        <if test="fieldList != null and fieldList.size() != 0">
            <foreach collection="fieldList" item="field" separator=",">
                ${field}
            </foreach>
        </if>
        <if test="fieldList == null or fieldList.size() == 0">
            *
        </if>
        FROM (SELECT t1.*,
        t2.INTERFACE_TYPE,
        t2.SERVICE_PROVIDER,
        t2.SUBJECT_DOMAIN,
        t2.INTERFACE_NAME,
        t2.RELEASE_RANGE
        FROM (SELECT * FROM ESB_RECORD_INFO
        <where>
            <if test="interfaceId != null and interfaceId.length() != 0">
                INTERFACE_ID = #{interfaceId}
            </if>
        </where>
        ) t1
        INNER JOIN ESB_SERVICE_MODEL t2 ON t1.INTERFACE_ID = t2.INTERFACE_ID)
    </select>

    <select id="selectPagedByCondition" resultMap="recordInfoDto">
        SELECT * FROM (
        SELECT ROWNUM ROW_NUM, t3.*
        FROM (SELECT *
        FROM (
        <include refid="recordInfoInnerJoinServiceModel"/>
        ORDER BY t1.COUNT_NUM DESC)
        <include refid="condition"/>
        ) t3)
        WHERE ROW_NUM >= #{startRow}
        AND #{endRow} >= ROW_NUM
    </select>

    <select id="countInfoDetailByCondition" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM ESB_RECORD_INFO WHERE INTERFACE_ID = #{interfaceId}
        <if test="channelId != null and channelId.length() != 0">
            AND CHANNEL_ID = #{channelId}
        </if>
        <if test="csmrId != null and csmrId.length() != 0">
            AND CSMR_ID = #{csmrId}
        </if>
        <if test="transCde != null and transCde.length() != 0">
            AND TRANS_CDE = #{transCde}
        </if>
        <if test="esbRespCode != null and esbRespCode.length() != 0">
            AND ESB_RESP_CODE = #{esbRespCode}
        </if>
        <if test="esbRespMsg != null and esbRespMsg.length() != 0">
            AND ESB_RESP_MSG = #{esbRespMsg}
        </if>
        <if test="versionNumber != null and versionNumber.length() != 0">
            AND VERSION_NUMBER = #{versionNumber}
        </if>
        <if test="startTime != null and startTime.length() != 0">
            AND REQUEST_TIME >= #{startTime}
        </if>
        <if test="endTime != null and endTime.length() != 0">
            AND #{endTime} >= REQUEST_TIME
        </if>
    </select>

    <select id="selectPagedInfoDetailByCondition" resultMap="recordInfoDetailDto">
        SELECT * FROM (
        SELECT ROWNUM ROW_NUM, t1.* FROM (
        SELECT ID,
        INTERFACE_ID,
        HOST,
        CSMR_ID,
        VERSION_NUMBER,
        TRANS_CDE,
        CHANNEL_ID,
        ESB_RESP_MSG,
        ESB_RESP_CODE,
        REQUEST_TIME,
        RESPONSE_TIME
        FROM ESB_RECORD_INFO WHERE INTERFACE_ID = #{interfaceId}
        <if test="channelId != null and channelId.length() != 0">
            AND CHANNEL_ID = #{channelId}
        </if>
        <if test="csmrId != null and csmrId.length() != 0">
            AND CSMR_ID = #{csmrId}
        </if>
        <if test="transCde != null and transCde.length() != 0">
            AND TRANS_CDE = #{transCde}
        </if>
        <if test="esbRespCode != null and esbRespCode.length() != 0">
            AND ESB_RESP_CODE = #{esbRespCode}
        </if>
        <if test="esbRespMsg != null and esbRespMsg.length() != 0">
            AND ESB_RESP_MSG = #{esbRespMsg}
        </if>
        <if test="versionNumber != null and versionNumber.length() != 0">
            AND VERSION_NUMBER = #{versionNumber}
        </if>
        <if test="startTime != null and startTime.length() != 0">
            AND REQUEST_TIME >= #{startTime}
        </if>
        <if test="endTime != null and endTime.length() != 0">
            AND #{endTime} >= REQUEST_TIME
        </if>
        ORDER BY REQUEST_TIME DESC) t1)
        WHERE ROW_NUM >= #{startRow} AND #{endRow} >= ROW_NUM
    </select>

    <insert id="insert">
        INSERT INTO ESB_RECORD_INFO (ID, HOST, INTERFACE_ID, REQUEST_TIME, RESPONSE_TIME, SVC_CORR_ID, CHANNEL_ID,
                                     TRANS_CDE, CSMR_ID, VERSION_NUMBER, ESB_RESP_MSG, ESB_RESP_CODE,
                                     PROVIDER_ID)
        VALUES (#{id}, #{host}, #{interfaceId}, #{requestTime,jdbcType=BIGINT}, #{responseTime,jdbcType=BIGINT},
                #{svcCorrId,jdbcType=VARCHAR},
                #{channelId,jdbcType=VARCHAR}, #{transCde,jdbcType=VARCHAR},
                #{csmrId,jdbcType=VARCHAR}, #{versionNumber,jdbcType=VARCHAR},
                #{esbRespMsg,jdbcType=VARCHAR}, #{esbRespCode,jdbcType=VARCHAR}, #{providerId,jdbcType=VARCHAR})
    </insert>

    <select id="selectIdByInterfaceIdAndTime" resultType="java.lang.String">
        SELECT *
        FROM ESB_RECORD_INFO
        WHERE INTERFACE_ID = #{interfaceId}
        <if test="startTime != null and startTime.length() != 0">
            AND REQUEST_TIME >= #{startTime}
        </if>
        <if test="endTime != null and endTime.length() != 0">
            AND #{endTime} >= REQUEST_TIME
        </if>
    </select>

    <select id="selectByTime" resultMap="esbRecordInfo">
        SELECT *
        FROM ESB_RECORD_INFO
        WHERE REQUEST_TIME >= #{startTime}
          AND #{endTime} >= REQUEST_TIME
    </select>

    <select id="selectPagedRecordInfoByCondition" resultMap="recordInfoDetailDto">
        SELECT * FROM (
        SELECT ROWNUM ROW_NUM, t4.* FROM (
        SELECT t3.*
        FROM (SELECT t1.*, t2.INTERFACE_TYPE, t2.SERVICE_PROVIDER, t2.INTERFACE_NAME, t2.SUBJECT_DOMAIN
        FROM (SELECT *
        FROM ESB_RECORD_INFO
        WHERE 1=1
        <if test="interfaceId != null and interfaceId.length() != 0">
            AND INTERFACE_ID = #{interfaceId}
        </if>
        <if test="startTime != null and startTime.length() != 0">
            AND REQUEST_TIME >= #{startTime}
        </if>
        <if test="endTime != null and endTime.length() != 0">
            AND #{endTime} >= REQUEST_TIME
        </if>
        ) t1
        INNER JOIN ESB_SERVICE_MODEL t2 ON t1.INTERFACE_ID = t2.INTERFACE_ID
        WHERE 1=1
        <if test="subjectDomain != null and subjectDomain.length() != 0">
            AND t2.SUBJECT_DOMAIN = #{subjectDomain}
        </if>
        <if test="serviceProvider != null and serviceProvider.length() != 0">
            AND t2.SERVICE_PROVIDER = #{serviceProvider}
        </if>
        <if test="interfaceType != null and interfaceType.length() != 0">
            AND t2.INTERFACE_TYPE = #{interfaceType}
        </if>
        ) t3 ORDER BY REQUEST_TIME) t4) WHERE ROW_NUM >= #{startRow} AND #{endRow}
        >= ROW_NUM
    </select>

    <select id="selectByConditionInOrder" resultMap="esbRecordInfo">
        SELECT *
        FROM ESB_RECORD_INFO
        WHERE 1=1
        <if test="startTime != null and startTime.length() != 0">
            AND REQUEST_TIME >= #{startTime}
        </if>
        <if test="endTime != null and endTime.length() != 0">
            AND #{endTime} >= REQUEST_TIME
        </if>
        ORDER BY REQUEST_TIME ASC
    </select>
</mapper>