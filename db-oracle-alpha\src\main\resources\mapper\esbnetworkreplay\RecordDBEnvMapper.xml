<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.RecordDbEnvMapper">
    
    <resultMap id="recordDBEnv" type="com.bqd.model.esbnetworkreplay.RecordDBEnv">
        <id property="id" column="id" javaType="java.lang.String"/>
        <result property="envName" column="env_name" javaType="java.lang.String"/>
        <result property="url" column="url" javaType="java.lang.String"/>
        <result property="userName" column="user_name" javaType="java.lang.String"/>
        <result property="password" column="password" javaType="java.lang.String"/>
        <result property="createTime" column="create_time" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectAllOrderByCreateTimeDESC" resultMap="recordDBEnv">
        SELECT *
        FROM ESB_DB_ENV ORDER BY CREATE_TIME DESC
    </select>

    <insert id="insert">
        INSERT INTO ESB_DB_ENV (ID, ENV_NAME, URL, USER_NAME, PASSWORD, CREATE_TIME)
        VALUES (#{id}, #{envName}, #{url}, #{userName}, #{password}, #{createTime})
    </insert>

    <update id="updateById">
        UPDATE ESB_DB_ENV
        SET ENV_NAME  = #{envName,jdbcType=VARCHAR},
            URL       = #{url,jdbcType=VARCHAR},
            USER_NAME = #{userName,jdbcType=VARCHAR},
            PASSWORD  = #{password,jdbcType=VARCHAR}
        WHERE ID = #{id}
    </update>

    <delete id="deleteById">
        DELETE
        FROM ESB_DB_ENV
        WHERE ID = #{id}
    </delete>

    <select id="selectById" resultMap="recordDBEnv">
        SELECT *
        FROM ESB_DB_ENV
        WHERE ID = #{id}
    </select>

</mapper>