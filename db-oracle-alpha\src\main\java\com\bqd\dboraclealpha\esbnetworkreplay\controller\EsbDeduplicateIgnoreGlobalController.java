package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbDeduplicateIgnoreGlobalMapper;
import com.bqd.model.esbnetworkreplay.EsbDeduplicateIgnoreGlobal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-03-07
 */
@RestController
@RequestMapping("/enr/esbDeduplicateIgnoreGlobal")
public class EsbDeduplicateIgnoreGlobalController {

    @Autowired
    private EsbDeduplicateIgnoreGlobalMapper esbDeduplicateIgnoreGlobalMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody EsbDeduplicateIgnoreGlobal esbDeduplicateIgnoreGlobal) {
        esbDeduplicateIgnoreGlobalMapper.insert(esbDeduplicateIgnoreGlobal);
    }

    @PostMapping("/selectByCondition")
    public List<EsbDeduplicateIgnoreGlobal> selectByCondition(@RequestBody EsbDeduplicateIgnoreGlobal esbDeduplicateIgnoreGlobal) {
        return esbDeduplicateIgnoreGlobalMapper.selectByCondition(esbDeduplicateIgnoreGlobal);
    }

    @PostMapping("/deleteByCondition")
    public void deleteByCondition(@RequestBody EsbDeduplicateIgnoreGlobal esbDeduplicateIgnoreGlobal) {
        esbDeduplicateIgnoreGlobalMapper.deleteByCondition(esbDeduplicateIgnoreGlobal);
    }

}
