<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.transactionchain.mapper.WisdomTradeNameMapper">

    <resultMap id="wisdomTradeName" type="com.bqd.model.transactionchain.WisdomTradeName">
        <id property="id" column="ID" javaType="java.lang.String"/>
        <result property="tradeName" column="TRADE_NAME" javaType="java.lang.String"/>
        <result property="filePath" column="FILE_PATH" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO WISDOM_TRADE_NAME (ID, TRADE_NAME, FILE_PATH)
        VALUES (#{id}, #{tradeName}, #{filePath})
    </insert>

    <delete id="truncate">
        TRUNCATE TABLE WISDOM_TRADE_NAME
    </delete>

    <select id="selectByLike" resultMap="wisdomTradeName">
        SELECT *
        FROM WISDOM_TRADE_NAME
        WHERE TRADE_NAME LIKE CONCAT('%', CONCAT(#{tradeName}, '%'))
    </select>

    <select id="selectById" resultMap="wisdomTradeName">
        SELECT *
        FROM WISDOM_TRADE_NAME
        WHERE ID = #{id}
    </select>
</mapper>