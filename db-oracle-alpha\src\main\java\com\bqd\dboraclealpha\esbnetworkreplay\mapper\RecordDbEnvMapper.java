package com.bqd.dboraclealpha.esbnetworkreplay.mapper;

import com.bqd.model.esbnetworkreplay.RecordDBEnv;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface RecordDbEnvMapper {
    List<RecordDBEnv> selectAllOrderByCreateTimeDESC();

    void insert(RecordDBEnv recordDBEnv);

    void updateById(RecordDBEnv recordDBEnv);

    void deleteById(String id);

    RecordDBEnv selectById(String id);
}
