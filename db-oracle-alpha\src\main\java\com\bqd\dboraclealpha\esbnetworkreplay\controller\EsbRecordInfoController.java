package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbRecordInfoMapper;
import com.bqd.model.esbnetworkreplay.*;
import com.bqd.model.esbnetworkreplay.EsbRecordInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/enr/esbRecordInfo")
public class EsbRecordInfoController {

    @Autowired
    private EsbRecordInfoMapper esbRecordInfoMapper;

    /**
     * 根据条件查询记录总数
     * @param recordInfoDto
     * @return
     */
    @PostMapping("/countByCondition")
    public int countByCondition(@RequestBody EsbRecordInfoDto recordInfoDto) {
        return esbRecordInfoMapper.countByCondition(recordInfoDto);
    }

    /**
     * 根据条件查询记录 - 分页
     * @param startRow
     * @param endRow
     * @param recordInfoDto
     * @return
     */
    @PostMapping("/selectByCondition")
    public List<EsbRecordInfo> selectByCondition(@RequestParam(required = false) Integer startRow, @RequestParam(required = false) Integer endRow, @RequestBody EsbRecordInfoDto recordInfoDto) {
        return esbRecordInfoMapper.selectByCondition(startRow, endRow, recordInfoDto);
    }

    /**
     * 根据列名获取该列数据（去重）
     * @param colNameList
     * @return
     */
    @PostMapping("/selectDistinctByColName")
    public List<EsbRecordInfo> selectDistinctByColName(@RequestBody List<String> colNameList) {
        return esbRecordInfoMapper.selectDistinctByColName(colNameList);
    }

    /**
     * 根据id删除记录
     * @param recordId
     */
    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String recordId) {
        esbRecordInfoMapper.deleteById(recordId);
    }

    /**
     * 插入记录
     * @param esbRecordInfo
     */
    @PostMapping("/insert")
    public void insert(@RequestBody EsbRecordInfo esbRecordInfo) {
        esbRecordInfoMapper.insert(esbRecordInfo);
    }

    /**
     * 根据条件查询录制信息和详情
     * @param esbRecordInfoDto
     * @return
     */
    @PostMapping("/selectInfoDetailByCondition")
    public List<EsbRecordInfoDetailDto> selectInfoDetailByCondition(@RequestBody EsbRecordInfoDto esbRecordInfoDto) {
        return esbRecordInfoMapper.selectInfoDetailByCondition(esbRecordInfoDto);
    }

}
