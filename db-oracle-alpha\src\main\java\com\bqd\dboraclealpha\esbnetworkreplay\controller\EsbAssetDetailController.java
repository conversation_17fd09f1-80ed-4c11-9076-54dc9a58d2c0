package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbAssetDetailMapper;
import com.bqd.model.esbnetworkreplay.AssetDetailDto;
import com.bqd.model.esbnetworkreplay.EsbAssetDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/enr/esbAssetDetail")
public class EsbAssetDetailController {

    @Autowired
    private EsbAssetDetailMapper esbAssetDetailMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody EsbAssetDetail esbAssetDetail) {
        esbAssetDetailMapper.insert(esbAssetDetail);
    }

    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String assetId) {
        esbAssetDetailMapper.deleteById(assetId);
    }

    @GetMapping("/selectFieldById")
    public EsbAssetDetail selectFieldById(@RequestParam("id") String id, @RequestParam("fieldList") List<String> fieldList) {
        return esbAssetDetailMapper.selectFieldById(id, fieldList);
    }

    @GetMapping("/selectByInterfaceIdAndVersionNumber")
    public List<AssetDetailDto> selectByInterfaceIdAndVersionNumber(@RequestParam("interfaceId") String interfaceId, @RequestParam("versionNumber") String versionNumber) {
        return esbAssetDetailMapper.selectByInterfaceIdAndVersionNumber(interfaceId, versionNumber);
    }

}
