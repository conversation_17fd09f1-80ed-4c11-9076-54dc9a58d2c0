package com.bqd.dboraclealpha.transactionchain.controller;

import com.bqd.dboraclealpha.transactionchain.mapper.WisdomBlMapper;
import com.bqd.model.transactionchain.WisdomBl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
@RestController
@RequestMapping("/transactionChain/wisdom/bl")
public class WisdomBlController implements com.bqd.base.rpc.transactionchain.WisdomBlMapper {

    @Autowired
    private WisdomBlMapper wisdomBlMapper;

    @Override
    @PostMapping("/insert")
    public void insert(@RequestBody WisdomBl wisdomBl) {
        wisdomBlMapper.insert(wisdomBl);
    }

    @Override
    @GetMapping("/selectByBId")
    public List<WisdomBl> selectByBId(@RequestParam String bId) {
        return wisdomBlMapper.selectByBId(bId);
    }

    @Override
    @GetMapping("/selectByLikeLogicId")
    public List<WisdomBl> selectByLikeLogicId(@RequestParam String serviceGroup) {
        return wisdomBlMapper.selectByLikeLogicId(serviceGroup);
    }
}
