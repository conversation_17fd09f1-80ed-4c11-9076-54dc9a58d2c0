package com.bqd.dboraclealpha.transactionchain.controller;

import com.bqd.dboraclealpha.transactionchain.mapper.TcCssMenuMapper;
import com.bqd.model.transactionchain.TcCssMenu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-14
 */
@RestController
@RequestMapping("/transactionChain/cssMenu")
public class TcCssMenuController {

    @Autowired
    private TcCssMenuMapper tcCssMenuMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody TcCssMenu tcCssMenu) {
        tcCssMenuMapper.insert(tcCssMenu);
    }

    @PostMapping("/selectByCondition")
    public List<TcCssMenu> selectByCondition(@RequestBody TcCssMenu tcCssMenu) {
        return tcCssMenuMapper.selectByCondition(tcCssMenu);
    }

}
