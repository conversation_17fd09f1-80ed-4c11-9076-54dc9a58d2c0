package com.bqd.serviceserveroperation.service;

import com.bqd.model.common.PageDto;
import com.bqd.model.servermanagement.ServerInfo;

import java.util.List;

public interface ServerManagementService {
    void addServer(ServerInfo serverInfo);

    PageDto<ServerInfo> getByPageInfo(int pageNo, int pageSize, ServerInfo serverInfo);

    List<ServerInfo> getByCondition(ServerInfo serverInfo);

    void updateById(ServerInfo serverInfo);

    void deleteById(String id);
}
