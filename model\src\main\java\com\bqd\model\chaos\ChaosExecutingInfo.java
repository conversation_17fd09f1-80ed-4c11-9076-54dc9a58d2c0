package com.bqd.model.chaos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 正在执行的混沌信息
 * <AUTHOR>
 * @CreateTime 2025-01-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChaosExecutingInfo {
    /**
     * id
     */
    private String id;

    /**
     * 混沌类型
     */
    private String chaosType;

    /**
     * 故障服务器ip
     */
    private String serverIp;

    /**
     * 故障开始时间，10位数字时间戳
     */
    private Long startTime;

    /**
     * 故障持续时间，单位秒
     */
    private Long duration;

    /**
     * 预计结束时间，10位数字时间戳
     */
    private Long estEndTime;

    /**
     * 目标信息
     */
    private String targetInfo;

    /**
     * 服务器信息id
     */
    private String serverInfoId;
}
