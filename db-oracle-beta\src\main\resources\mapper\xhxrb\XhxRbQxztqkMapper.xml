<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclebeta.xhx.mapper.XhxRbQxztqkMapper">
    <resultMap id="xhxRbQxztqk" type="com.bqd.model.xhxrb.XhxRbQxztqk">
        <result column="SYSTEM_NAME" property="systemName" javaType="java.lang.String"/>
        <result column="OPEN_COUNT" property="openCount" javaType="java.lang.Integer"/>
        <result column="ASSIGNED_COUNT" property="assignedCount" javaType="java.lang.Integer"/>
        <result column="REPAIRING_COUNT" property="repairingCount" javaType="java.lang.Integer"/>
        <result column="REPAIRED_COUNT" property="repairedCount" javaType="java.lang.Integer"/>
        <result column="REOPEN_COUNT" property="reopenCount" javaType="java.lang.Integer"/>
        <result column="DXQQR_COUNT" property="dxqqrCount" javaType="java.lang.Integer"/>
        <result column="DQRXHX_COUNT" property="dqrxhxCount" javaType="java.lang.Integer"/>
        <result column="DXG_COUNT" property="dxgCount" javaType="java.lang.Integer"/>
        <result column="SUSPEND_COUNT" property="suspendCount" javaType="java.lang.Integer"/>
        <result column="DZC_COUNT" property="dzcCount" javaType="java.lang.Integer"/>
        <result column="TQCSZC_COUNT" property="tqcszcCount" javaType="java.lang.Integer"/>
        <result column="TQPMOZC_COUNT" property="tqpmozcCount" javaType="java.lang.Integer"/>
        <result column="NEW_COUNT" property="newCount" javaType="java.lang.Integer"/>
        <result column="REJECT_COUNT" property="rejectCount" javaType="java.lang.Integer"/>
        <result column="DFC_COUNT" property="dfcCount" javaType="java.lang.Integer"/>
        <result column="CLOSED_COUNT" property="closedCount" javaType="java.lang.Integer"/>
        <result column="JJQR_COUNT" property="jjqrCount" javaType="java.lang.Integer"/>
        <result column="TOTAL_COUNT" property="totalCount" javaType="java.lang.Integer"/>
    </resultMap>

    <select id="selectAll" resultMap="xhxRbQxztqk">
        SELECT *
        FROM T_B_XHX_RB_QXZTQK
    </select>

    <update id="pcdInsertSystemName">
        CALL QXZTQK_INSERT_SYSTEM_NAME(#{roundName})
    </update>

    <update id="pcdUpdateCount">
        CALL QXZTQK_COUNT(#{roundName}, #{zt}, #{updateColumn})
    </update>

    <update id="pcdUpdateTotalCount">
        CALL QXZTQK_TOTAL_COUNT(#{roundName})
    </update>
</mapper>
