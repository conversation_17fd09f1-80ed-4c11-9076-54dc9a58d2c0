package com.bqd.integratedtester.sibstranscheck.service;

import com.bqd.model.common.PageDto;
import com.bqd.model.sibstranscheck.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-07-05
 */
public interface SibsTransCheckService {
    String uploadAndSave(MultipartFile multipartFile);

    void removeUploadFile(String id);

    List<SibsFileUpload> getUploadFileList();

    void parseToDB(String id);

    void clearHttpDataDB();

    void clearCheckResultDB();

    void sibsTransCheck();

    PageDto<SibsHttpDataInfo> getPagedHttpDataInfo(int pageNo, int pageSize, String seqNo, String transTimestamp);

    SibsHttpData getHttpDataByInfoId(String infoId);

    PageDto<SibsTransCheckResult> getPagedTransCheckResult(int pageNo, int pageSize);

    SibsTransCheckFailed getFailedInfoByResultId(String resultId);

    List<SibsTransCheckConfig> getCheckConfigListByType(String type);

    void addCheckConfig(SibsTransCheckConfig sibsTransCheckConfig);

    void deleteCheckConfigById(String id);
}
