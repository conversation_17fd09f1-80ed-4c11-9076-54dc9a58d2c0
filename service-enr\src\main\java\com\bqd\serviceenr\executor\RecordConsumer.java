package com.bqd.serviceenr.executor;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.bqd.base.rpc.esbnetworkreplay.EsbRecordDetailMapper;
import com.bqd.base.rpc.esbnetworkreplay.EsbRecordInfoMapper;
import com.bqd.base.tools.EsbTool;
import com.bqd.base.tools.XmlTool;
import com.bqd.model.esbnetworkreplay.EsbRecordDetail;
import com.bqd.model.esbnetworkreplay.HttpPacketDto;
import com.bqd.model.esbnetworkreplay.EsbRecordInfo;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.dom4j.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-03-04
 */
@Component
@RocketMQMessageListener(consumerGroup = "enr-reocrd", topic = "net-pcap-agent")
public class RecordConsumer implements RocketMQListener<HttpPacketDto> {

    @Autowired
    private EsbRecordInfoMapper esbRecordInfoMapper;
    @Autowired
    private EsbRecordDetailMapper esbRecordDetailMapper;

    @Override
    public void onMessage(HttpPacketDto httpPacketDto) {
        String recordId = IdUtil.fastSimpleUUID();
        String interfaceId = EsbTool.extractInterfaceIdFromUrl(httpPacketDto.getRequest().split("\r\n")[0]);
        String requestBody = EsbTool.extractRequestBody(httpPacketDto.getRequest());
        String responseBody = EsbTool.extractResponseBody(httpPacketDto.getResponse());
        if (StrUtil.hasBlank(interfaceId, requestBody, responseBody)) {
            return;
        }
        String host = EsbTool.extractHostFromPacket(httpPacketDto.getRequest());

        EsbRecordInfo esbRecordInfo = new EsbRecordInfo(recordId, host, interfaceId, httpPacketDto.getRequestTime(), httpPacketDto.getResponseTime(), null, null, null, null, null, null, null, null);
        EsbRecordDetail esbRecordDetail = new EsbRecordDetail(recordId, requestBody, responseBody);

        insertPreHandler(esbRecordInfo, esbRecordDetail);
        esbRecordInfoMapper.insert(esbRecordInfo);
        esbRecordDetailMapper.insert(esbRecordDetail);
    }

    /**
     * 插入前预处理
     * @param esbRecordInfo
     * @param esbRecordDetail
     */
    private void insertPreHandler(EsbRecordInfo esbRecordInfo, EsbRecordDetail esbRecordDetail) {
        String responseBody = esbRecordDetail.getResponseBody();
        Document document = XmlTool.readXmlFromStr(responseBody);
        String respCde = document.selectSingleNode("/resp/svcHdr/respCde").getText();
        String respMsg = document.selectSingleNode("/resp/svcHdr/respMsg").getText();
        esbRecordInfo.setEsbRespCode(respCde);
        esbRecordInfo.setEsbRespMsg(respMsg);
    }

}
