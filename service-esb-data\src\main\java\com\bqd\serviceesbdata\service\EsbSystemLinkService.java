package com.bqd.serviceesbdata.service;

import com.bqd.model.common.TreeDto;

import java.util.List;

public interface EsbSystemLinkService {

    /**
     * 解析ESB服务模型
     */
    void parseEsbServiceModel();

    /**
     * 解析ESB系统调用链路
     */
    void parseSystemLink();

    /**
     * 解析ESL配置项
     */
    void parseEslServerConfig();

    /**
     * 获取配置项列表
     *
     * @return
     */
    List<TreeDto> getConfigList();

    /**
     * 根据配置项ID获取该配置项下的系统(服务提供方)列表
     *
     * @param configId
     */
    List<TreeDto> getESLTreeListByConfigId(String configId);

    /**
     * 获取ESB接口列表
     *
     * @return
     */
    List<TreeDto> getESBInterfaceList();

    /**
     * 获取指定接口id的发布范围
     *
     * @return
     */
    TreeDto getESLTreeListByInterfaceId(String interfaceId);

    /**
     * 获取指定服务提供方的发布范围
     * @param serviceProvider
     * @return
     */
    List<TreeDto> getESLTreeListByServiceProvider(String serviceProvider);

}
