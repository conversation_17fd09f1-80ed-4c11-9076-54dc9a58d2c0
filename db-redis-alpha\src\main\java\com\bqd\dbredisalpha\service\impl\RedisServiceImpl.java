package com.bqd.dbredisalpha.service.impl;

import com.bqd.dbredisalpha.service.RedisService;
import com.bqd.model.redis.RedisDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-27
 */
@Service
public class RedisServiceImpl implements RedisService {

    @Autowired
    private JedisPool jedisPool;

    @Override
    public void setString(RedisDto redisDto) {
        Jedis jedis = jedisPool.getResource();
        jedis.set(redisDto.getKey(), redisDto.getValue());
        if (redisDto.getTtl() > 0) {
            jedis.expire(redisDto.getKey(), redisDto.getTtl());
        }
        jedis.close();
    }

    @Override
    public RedisDto getString(String key) {
        Jedis jedis = jedisPool.getResource();
        String value = jedis.get(key);
        Long ttl = jedis.ttl(key);
        return new RedisDto(key, value, ttl);
    }
}
