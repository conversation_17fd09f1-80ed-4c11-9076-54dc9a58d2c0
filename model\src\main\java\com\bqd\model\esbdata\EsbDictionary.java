package com.bqd.model.esbdata;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Getter
public class EsbDictionary {
    /**
     * 接口ID
     */
    private String interfaceId;

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 主题域
     */
    private String subjectDomain;

    /**
     * 发布范围
     */
    private String releaseRange;

    /**
     * 服务提供方
     */
    private String serviceProvider;

    /**
     * 接口类型代码
     */
    private String interfaceTypeCode;
}
