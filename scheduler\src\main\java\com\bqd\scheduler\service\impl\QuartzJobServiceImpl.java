package com.bqd.scheduler.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.model.scheduler.TriggerInfoDto;
import com.bqd.scheduler.service.QuartzJobService;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
@Service
public class QuartzJobServiceImpl implements QuartzJobService {

    @Autowired
    private Scheduler scheduler;

    @Override
    public List<TriggerInfoDto> getJobsByCondition(TriggerInfoDto reqtTriggerInfoDto) {
        List<TriggerInfoDto> triggerInfoDtoList = new ArrayList<>();
        try {
            // 获取所有JobGroup
            for (String groupName : scheduler.getJobGroupNames()) {
                // 获取组内所有JobKey
                for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(groupName))) {
                    //获取组和姓名
                    String group = jobKey.getGroup();
                    String name = jobKey.getName();
                    JobDetail jobDetail = scheduler.getJobDetail(jobKey);
                    String jobClassname = jobDetail.getJobClass().getName();
                    String description = jobDetail.getDescription();

                    if (StrUtil.isNotBlank(reqtTriggerInfoDto.getJobName()) && !StrUtil.equals(name, reqtTriggerInfoDto.getJobName())) {
                        continue;
                    }

                    //触发器信息
                    for (Trigger trigger : scheduler.getTriggersOfJob(jobKey)) {
                        if (StrUtil.isNotBlank(reqtTriggerInfoDto.getStatus()) && !StrUtil.equals(scheduler.getTriggerState(trigger.getKey()).name(), reqtTriggerInfoDto.getStatus())) {
                            continue;
                        }
                        if (trigger instanceof CronTrigger) {
                            CronTrigger cronTrigger = (CronTrigger) trigger;
                            TriggerInfoDto triggerInfoDto = new TriggerInfoDto(
                                    trigger.getKey().getName(),
                                    trigger.getKey().getGroup(),
                                    name,
                                    group,
                                    jobClassname,
                                    cronTrigger.getCronExpression(),
                                    scheduler.getTriggerState(cronTrigger.getKey()).name(),
                                    description,
                                    DateUtil.format(cronTrigger.getPreviousFireTime(), "yyyy-MM-dd HH:mm:ss"),
                                    DateUtil.format(cronTrigger.getNextFireTime(), "yyyy-MM-dd HH:mm:ss"));
                            triggerInfoDtoList.add(triggerInfoDto);
                        }
                    }
                }
            }
            return triggerInfoDtoList;
        } catch (Exception e) {
            throw new RuntimeException("获取定时任务信息出错");
        }
    }

    @Override
    public void updateTrigger(TriggerInfoDto triggerInfoDto) {
        try {
            JobKey jobKey = new JobKey(triggerInfoDto.getJobName(), triggerInfoDto.getJobGroup());
            TriggerKey triggerKey = new TriggerKey(triggerInfoDto.getTriggerName(), triggerInfoDto.getTriggerGroup());

            // 1. 检查任务是否存在
            if (!scheduler.checkExists(jobKey)) {
                throw new CustomizedException("任务不存在");
            }

            // 2. 获取原任务详情
            JobDetail oldJobDetail = scheduler.getJobDetail(jobKey);

            // 3. 构建新的JobDetail
            JobBuilder jobBuilder = oldJobDetail.getJobBuilder();

            // 更新任务描述
            if (triggerInfoDto.getDescription() != null) {
                jobBuilder.withDescription(triggerInfoDto.getDescription());
            }

            //构建新的JobDetail
            JobDetail newJobDetail = jobBuilder.build();

            // 4. 更新Cron表达式（如果有）
            Trigger trigger = scheduler.getTrigger(triggerKey);
            if (trigger instanceof CronTrigger) {
                CronTrigger cronTrigger = (CronTrigger) trigger;
                if (triggerInfoDto.getCronExpression() != null) {
                    CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(triggerInfoDto.getCronExpression());
                    Trigger newTrigger = cronTrigger.getTriggerBuilder().withSchedule(scheduleBuilder).build();

                    // 重新调度任务
                    scheduler.rescheduleJob(triggerKey, newTrigger);
                }
            }

            // 5. 更新JobDetail
            scheduler.addJob(newJobDetail, true);

            // 6. 更新任务状态（如果有）
            if (triggerInfoDto.getStatus() != null) {
                updateTriggerStatus(triggerKey, triggerInfoDto.getStatus());
            }
        } catch (Exception e) {
            throw new RuntimeException("更新定时任务出错", e);
        }
    }

    @Override
    public void updateTriggerStatus(TriggerKey triggerKey, String status) {
        try {
            if (Trigger.TriggerState.NORMAL.name().equals(status)) {
                scheduler.resumeTrigger(triggerKey);
            }
            if (Trigger.TriggerState.PAUSED.name().equals(status)) {
                scheduler.pauseTrigger(triggerKey);
            }
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
    }

}
