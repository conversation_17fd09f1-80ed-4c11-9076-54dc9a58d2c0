<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbReplayInfoMapper">

    <resultMap id="esbReplayInfo" type="com.bqd.model.esbnetworkreplay.EsbReplayInfo">
        <id property="id" column="ID" javaType="java.lang.String"/>
        <result property="replayPlanId" column="REPLAY_PLAN_ID" javaType="java.lang.String"/>
        <result property="url" column="URL" javaType="java.lang.String"/>
        <result property="interfaceId" column="INTERFACE_ID" javaType="java.lang.String"/>
        <result property="status" column="STATUS" javaType="java.lang.Integer"/>
        <result property="requestTime" column="REQUEST_TIME" javaType="java.lang.String"/>
        <result property="responseTime" column="RESPONSE_TIME" javaType="java.lang.String"/>
        <result property="versionNumber" column="VERSION_NUMBER" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO ESB_REPLAY_INFO (ID, REPLAY_PLAN_ID, URL, INTERFACE_ID, REQUEST_TIME, RESPONSE_TIME, VERSION_NUMBER)
        VALUES (#{id}, #{replayPlanId}, #{url}, #{interfaceId}, #{requestTime}, #{responseTime}, #{versionNumber,jdbcType=VARCHAR})
    </insert>

    <select id="selectByPlanId" resultMap="esbReplayInfo">
        SELECT *
        FROM ESB_REPLAY_INFO
        WHERE REPLAY_PLAN_ID = #{replayPlanId}
    </select>

    <delete id="deleteById">
        DELETE
        FROM ESB_REPLAY_INFO
        WHERE ID = #{id}
    </delete>

</mapper>
