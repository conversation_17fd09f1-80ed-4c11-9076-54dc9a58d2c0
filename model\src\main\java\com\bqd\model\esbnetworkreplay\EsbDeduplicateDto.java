package com.bqd.model.esbnetworkreplay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-03-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(of = {"deduplicateBody"})
public class EsbDeduplicateDto {
    private EsbAssetInfoDetailDto esbAssetInfoDetailDto;
    private String deduplicateBody;
}
