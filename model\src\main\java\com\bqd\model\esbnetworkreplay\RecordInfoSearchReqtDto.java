package com.bqd.model.esbnetworkreplay;

import com.bqd.model.common.OraclePagedCommonParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 录制信息页 搜索dto
 * @Author: wangzi<PERSON>i
 * @CreateTime: 2024-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordInfoSearchReqtDto extends OraclePagedCommonParam {
    private String interfaceId;
    private String subjectDomain;
    private String serviceProvider;
    private String interfaceType;
    private String startTime;
    private String endTime;
}
