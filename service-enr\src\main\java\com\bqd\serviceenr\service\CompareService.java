package com.bqd.serviceenr.service;

import com.bqd.model.esbnetworkreplay.EsbDeduplicateIgnoreDto;

import java.util.List;

public interface CompareService {
    List<String> getIgnoredField(String interfaceId);

    List<String> getIgnoredFieldGlobal();

    void updateIgnoredField(EsbDeduplicateIgnoreDto esbDeduplicateIgnoreDto);

    void addIgnoredFieldGlobal(String ignoredField);

    void deleteIgnoredFieldGlobal(String ignoredField);
}
