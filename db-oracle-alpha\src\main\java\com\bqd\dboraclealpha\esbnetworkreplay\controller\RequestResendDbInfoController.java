package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.RequestResendDbInfoMapper;
import com.bqd.model.esbnetworkreplay.RequestResendDBInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-13
 */
@RestController
@RequestMapping("/enr/config/resend")
public class RequestResendDbInfoController {

    @Autowired
    private RequestResendDbInfoMapper requestResendDbInfoMapper;

    @GetMapping("/selectAllOdrByTimeDesc")
    public List<RequestResendDBInfo> selectAllOdrByTimeDesc() {
        return requestResendDbInfoMapper.selectAllOdrByTimeDesc();
    }

    @PostMapping("/insert")
    public void insert(@RequestBody RequestResendDBInfo requestResendDBInfo) {
        requestResendDbInfoMapper.insert(requestResendDBInfo);
    }

    @PostMapping("/updateById")
    public void updateById(@RequestBody RequestResendDBInfo requestResendDBInfo) {
        requestResendDbInfoMapper.updateById(requestResendDBInfo);
    }

    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String id) {
        requestResendDbInfoMapper.deleteById(id);
    }

    @GetMapping("/selectById")
    public RequestResendDBInfo selectById(@RequestParam String id) {
        return requestResendDbInfoMapper.selectById(id);
    }

}
