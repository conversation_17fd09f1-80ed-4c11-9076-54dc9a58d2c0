package com.bqd.dboraclebeta.xhx.controller;

import com.bqd.dboraclebeta.xhx.mapper.XhxRbQxztqkMapper;
import com.bqd.model.xhxrb.XhxRbQxztqk;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-25
 */
@RestController
@RequestMapping("/xhxrb/qxztqk")
public class QxztqkController {

    @Autowired
    private XhxRbQxztqkMapper xhxRbQxztqkMapper;

    @GetMapping("/selectAll")
    public List<XhxRbQxztqk> selectAll() {
        return xhxRbQxztqkMapper.selectAll();
    }

    @GetMapping("/pcdInsertSystemName")
    public void pcdInsertSystemName(@RequestParam String roundName) {
        xhxRbQxztqkMapper.pcdInsertSystemName(roundName);
    }

    @GetMapping("/pcdUpdateCount")
    public void pcdUpdateCount(@RequestParam String roundName, @RequestParam String zt, @RequestParam String updateColumn) {
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, zt, updateColumn);
    }

    @GetMapping("/pcdUpdateTotalCount")
    public void pcdUpdateTotalCount(@RequestParam String roundName) {
        xhxRbQxztqkMapper.pcdUpdateTotalCount(roundName);
    }

}
