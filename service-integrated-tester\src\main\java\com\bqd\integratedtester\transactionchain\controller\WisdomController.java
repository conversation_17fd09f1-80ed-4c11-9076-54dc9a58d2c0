package com.bqd.integratedtester.transactionchain.controller;

import com.bqd.base.response.Response;
import com.bqd.integratedtester.transactionchain.service.WisdomService;
import com.bqd.model.transactionchain.WisdomEsbSearchDto;
import com.bqd.model.transactionchain.WisdomIntegrateServiceSearchDto;
import com.bqd.model.transactionchain.WisdomServiceSearchDto;
import com.bqd.model.transactionchain.WisdomTradeNameSearchDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: 智慧网点交易链路查询
 * <AUTHOR>
 * @CreateTime 2025-04-02
 */
@RestController
@RequestMapping("/transactionChain/wisdom")
public class WisdomController {

    @Autowired
    private WisdomService wisdomService;

    /**
     * 解析前端-前置
     */
    @GetMapping("/parseFront")
    public Response parseFront() {
        wisdomService.parseFront();
        return Response.success();
    }

    /**
     * 解析前置-中台
     */
    @GetMapping("/parseIntegratedService")
    public Response parseIfpIntegratedService() {
        wisdomService.parseIfpIntegratedService();
        return Response.success();
    }

    /**
     * 根据【交易名称】搜索
     * @param tradeName
     * @return
     */
    @GetMapping("/searchTradeName")
    public Response searchTradeName(@RequestParam("tradeName") String tradeName) {
        List<WisdomTradeNameSearchDto> wisdomTradeNameSearchDtoList = wisdomService.searchTradeName(tradeName);
        return Response.success(wisdomTradeNameSearchDtoList);
    }

    /**
     * 根据【前置id或描述】搜索
     * @param mvcId
     * @param description
     * @return
     */
    @GetMapping("/searchIntegrateService")
    public Response searchIntegratedService(@RequestParam(required = false) String mvcId,
                                            @RequestParam(required = false) String description) {
        List<WisdomIntegrateServiceSearchDto> wisdomIntegrateServiceSearchDtoList = wisdomService.searchIntegrateService(mvcId, description);
        return Response.success(wisdomIntegrateServiceSearchDtoList);
    }

    /**
     * 根据【中台id或描述】搜索
     * @param serviceGroup
     * @param description
     * @return
     */
    @GetMapping("/searchService")
    public Response searchService(@RequestParam(required = false) String serviceGroup,
                                  @RequestParam(required = false) String description) {
        List<WisdomServiceSearchDto> wisdomServiceSearchDtoList = wisdomService.searchService(serviceGroup, description);
        return Response.success(wisdomServiceSearchDtoList);
    }

    /**
     * 根据ESB接口ID搜索
     * @param interfaceId
     * @return
     */
    @GetMapping("/searchEsb")
    public Response searchEsb(@RequestParam("interfaceId") String interfaceId) {
        List<WisdomEsbSearchDto> wisdomEsbSearchDtoList = wisdomService.searchEsb(interfaceId);
        return Response.success(wisdomEsbSearchDtoList);
    }

}
