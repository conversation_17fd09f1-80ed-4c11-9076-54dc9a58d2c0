package com.bqd.model.esbnetworkreplay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-07-31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ENRCommonRespDto {
    private String interfaceId;
    private String interfaceName;
    private String serviceProvider;
    private String subjectDomain;
    private String interfaceType;
}
