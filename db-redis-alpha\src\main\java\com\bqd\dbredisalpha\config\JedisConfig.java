package com.bqd.dbredisalpha.config;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Redis连接
 */
@Configuration
@Slf4j
public class JedisConfig {
    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private int port;

    @Value("${spring.redis.password}")
    private String password;

    @Value("${spring.redis.jedis.pool.max-active}")
    private int maxActive;

    @Value("${spring.redis.jedis.pool.max-idle}")
    private int maxIdle;

    @Value("${spring.redis.jedis.pool.min-idle}")
    private int minIdle;

    @Bean
    public JedisPool jedisPool() {
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxIdle(maxIdle);
        jedisPoolConfig.setMaxTotal(maxActive);
        jedisPoolConfig.setMinIdle(minIdle);
        JedisPool jedisPool = null;
        try {
            jedisPool = new JedisPool(jedisPoolConfig, host, port, 10000, password);
            if (ObjectUtil.isNull(jedisPool)) {
                throw new RuntimeException();
            }
            log.info("JedisPool连接成功");
            return jedisPool;
        } catch (Exception e) {
            log.error("JedisPool连接失败", e);
            if (jedisPool != null) {
                jedisPool.close();
            }
            throw new RuntimeException();
        }
    }
}
