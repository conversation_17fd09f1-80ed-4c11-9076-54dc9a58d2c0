package com.bqd.enummodel;

import lombok.Getter;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-01-14
 */
@Getter
public enum FinancialStatementColorEnum {
    SECOND_ZERO("#D9F02D"),
    FIRST_ZERO("#D47BE7"),
    VALUE_DIFF("#7BB9E7"),
    VALUE_EMPTY("#fad3d9"),
    SECOND_NEW_ROW("#3BE764"),
    SECOND_DELETE_ROW("#EA2020"),
    SECOND_SAME_NAME("#BC8F8F"),
    SECOND_NUM_DEDUPLICATE("#40E0D0");

    private final String colorCode;

    FinancialStatementColorEnum(String colorCode) {
        this.colorCode = colorCode;
    }
}
