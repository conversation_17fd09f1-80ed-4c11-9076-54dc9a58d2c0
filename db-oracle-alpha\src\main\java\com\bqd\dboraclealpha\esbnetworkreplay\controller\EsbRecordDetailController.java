package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbRecordDetailMapper;
import com.bqd.model.esbnetworkreplay.EsbRecordDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/enr/esbRecordDetail")
public class EsbRecordDetailController {

    @Autowired
    private EsbRecordDetailMapper esbRecordDetailMapper;

    /**
     * 根据infoId和列名获取该条记录的列数据
     * @param recordId
     * @param colNameList
     * @return
     */
    @PostMapping("/selectByIdAndColName")
    public EsbRecordDetail selectByIdAndColName(@RequestParam String recordId, @RequestBody List<String> colNameList) {
        return esbRecordDetailMapper.selectByIdAndColName(recordId, colNameList);
    }

    /**
     * 插入录制详情
     * @param esbRecordDetail
     */
    @PostMapping("/insert")
    public void insert(@RequestBody EsbRecordDetail esbRecordDetail) {
        esbRecordDetailMapper.insert(esbRecordDetail);
    }

    /**
     * 根据infoId删除该条记录
     * @param recordId
     */
    @GetMapping("/deleteById")
    public void deleteById(String recordId) {
        esbRecordDetailMapper.deleteById(recordId);
    }

    @GetMapping("/selectById")
    public EsbRecordDetail selectById(String recordId) {
        return esbRecordDetailMapper.selectById(recordId);
    }
}
