package com.bqd.serviceesbdata.service;

import com.bqd.model.esbdata.EsbServiceModel;

import java.util.List;

public interface ServiceModelService {

    /**
     * 根据接口ID获取服务模型
     * @param interfaceId
     * @return
     */
    EsbServiceModel getByInterfaceId(String interfaceId);

    /**
     * 根据指定字段获取服务模型
     * @param fieldList
     * @return
     */
    List<EsbServiceModel> getServiceModelFiledValue(List<String> fieldList);
}
