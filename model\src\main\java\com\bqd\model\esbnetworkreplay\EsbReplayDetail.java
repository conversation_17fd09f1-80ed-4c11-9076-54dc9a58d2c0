package com.bqd.model.esbnetworkreplay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-08-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsbReplayDetail {
    private String infoId;
    private String replayRequestBody;
    private String rawResponseBody;
    private String replayResponseBody;
}
