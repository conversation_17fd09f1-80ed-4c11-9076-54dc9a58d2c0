package com.bqd.servicellm.demandDoc.service;


import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Log4j2
public class DifyService {


    public String sendRequest(String content) {
        // 构造总请求体
        JSONObject bodyJson = new JSONObject();
        JSONObject inputs = new JSONObject();
        inputs.set("requirement", content); // 转为 JSON 字符串
        bodyJson.set("inputs", inputs);
        bodyJson.set("response_mode", "streaming");
        bodyJson.set("user", "abc-123");

        // 发送请求
        String url = "http://10.238.145.164:8888/v1/workflows/run";
        String token = "app-Mte2CSF4sNMtwL6h9vgzzM4e";
        String result = "";
        try (HttpResponse response = HttpRequest.post(url)
                .header("Authorization", "Bearer " + token)
                .header("Content-Type", ContentType.JSON.getValue())
                .body(bodyJson.toString())
                .execute()) {

            String rawBody = response.body();
            String decodedBody = unicodeToString(rawBody);
//            System.out.println("Status: " + response.getStatus());
//            System.out.println("Decoded Body: " + decodedBody);
            result = extractJsonFromMarkdown(decodedBody);
        }

        return result;
    }

    public static String extractJsonFromMarkdown(String responseString) {
        int index = responseString.indexOf("data: {\"event\": \"workflow_finished\",");

        if (index == -1) {
            log.error("Response does not contain the expected event: workflow_finished. Response: {}", responseString);
            return null;
        }
        responseString = responseString.substring(index + 5).trim();
        log.info("Response: {}", responseString);
        String regex = "```json(.*?)```";
        Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(responseString);

        List<String> jsonStrings = new ArrayList<>();
        while (matcher.find()) {
            int count = matcher.groupCount();
            {
                log.info("count: {}", count);
            }

            String jsonStr = matcher.group(1).trim();
            log.info("jsonStr: {}", jsonStr);
            // 移除多余的转义字符
            jsonStr = jsonStr.replaceAll("\\\\n", "").replaceAll("\\\\\"", "\"");
            // 验证 JSON 格式是否正确
            if (jsonStr.startsWith("{") && jsonStr.endsWith("}")) {
                jsonStrings.add(jsonStr);
                JSONObject jsonObject = new JSONObject(jsonStr);
            } else {
                log.error("Extracted JSON string is not a valid JSON object. JSON: {}", jsonStr);
            }
        }

        JSONArray result = new JSONArray();
        for (String jsonString : jsonStrings) {
            JSONObject jsonObject = new JSONObject(jsonString);
            JSONArray testCases = jsonObject.getJSONArray("test_cases");
            result.addAll(testCases);
        }

//        System.out.println(result);
        return result.toString();
    }


    public String unicodeToString(String unicodeStr) {
        StringBuilder sb = new StringBuilder();
        int i = 0;
        while (i < unicodeStr.length()) {
            char c = unicodeStr.charAt(i++);
            if (c == '\\' && i < unicodeStr.length()) {
                char next = unicodeStr.charAt(i++);
                if (next == 'u' && i + 4 <= unicodeStr.length()) {
                    String hex = unicodeStr.substring(i, i + 4);
                    sb.append((char) Integer.parseInt(hex, 16));
                    i += 4;
                } else {
                    sb.append(c).append(next);
                }
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }


}
