package com.bqd.model.esbdata;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-11-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsbFieldEnum {
    private String id;
    private String interfaceId;
    private String infoId;
    private String field;
    private String enumValue;
    private String content;
}
