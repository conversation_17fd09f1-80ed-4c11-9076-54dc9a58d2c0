package com.bqd.serviceenr.controller;

import cn.hutool.core.util.StrUtil;
import com.bqd.base.response.Response;
import com.bqd.model.esbnetworkreplay.AssetInfoDetailSearchDto;
import com.bqd.model.esbnetworkreplay.AssetInfoSearchDto;
import com.bqd.serviceenr.service.AssetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-08-07
 */
@RestController
@RequestMapping("/asset")
public class AssetController {

    @Autowired
    private AssetService assetService;

    @GetMapping("/getAssetInfoFieldValue")
    public Response getAssetInfoFieldValue(@RequestParam("field") String field) {
        List<String> fieldList = StrUtil.split(field, ",", true, true);
        return Response.success(assetService.getAssetInfoFieldValue(fieldList));
    }

    @GetMapping("/getInfoFieldValueByInterfaceId")
    public Response getInfoFieldValueByInterfaceId(@RequestParam("interfaceId") String interfaceId, String field) {
        return Response.success(assetService.getInfoFieldValueByInterfaceId(interfaceId, field));
    }

    @PostMapping("/getPagedAssetInfo")
    public Response getPagedAssetInfo(@RequestBody AssetInfoSearchDto assetInfoSearchDto) {
        return Response.success(assetService.getPagedAssetInfo(assetInfoSearchDto));
    }

    @PostMapping("/getPagedAssetInfoDetail")
    public Response getPagedAssetInfoDetail(@RequestBody AssetInfoDetailSearchDto assetInfoDetailSearchDto) {
        return Response.success(assetService.getPagedAssetInfoDetail(assetInfoDetailSearchDto));
    }

    @GetMapping("/getAssetDetailFieldValue")
    public Response getAssetDetailFieldValue(@RequestParam("id") String id, String field) {
        List<String> fieldList = StrUtil.split(field, ",", true, true);
        return Response.success(assetService.getAssetDetailFieldValue(id, fieldList));
    }

    @GetMapping("/deleteAsset")
    public Response deleteAsset(@RequestParam("id") String id) {
        assetService.deleteAsset(id);
        return Response.success();
    }

}
