package com.bqd.integratedtester.authoritypacket.controller;

import cn.hutool.core.io.IoUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.base.response.Response;
import com.bqd.integratedtester.authoritypacket.service.AuthorityPacketService;
import com.bqd.model.authoritypacket.GeneratePacketDto;
import com.bqd.model.authoritypacket.GeneratePacketLLMDto;
import com.bqd.model.authoritypacket.PacketCommonInfo;
import com.bqd.model.authoritypacket.XmlStructureQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

/**
 * @Description: 有权机关报文自动发送
 * <AUTHOR>
 * @CreateTime 2025-01-02
 */
@RestController
@RequestMapping("/authorityPacket")
@Slf4j
public class AuthorityPacketController {

    @Autowired
    private AuthorityPacketService authorityPacketService;

    /**
     * 根据模板文件名，获取并解析xml，返回xml树结构
     *
     * @param xmlStructureQueryDto
     * @return
     */
    @PostMapping("/getXmlStructureTree")
    public Response getXmlStructure(@RequestBody XmlStructureQueryDto xmlStructureQueryDto) {
        return Response.success(authorityPacketService.getXmlStructureTree(xmlStructureQueryDto));
    }

    /**
     * 生成报文包
     *
     * @param generatePacketDto
     * @return
     */
    @PostMapping("/generatePacket")
    public Response generatePacket(@RequestBody GeneratePacketDto generatePacketDto) {
        return Response.success(authorityPacketService.generatePacket(generatePacketDto, file -> {}));
    }

    /**
     * 生成报文包 - 高法
     * @param generatePacketDto
     * @return
     */
    @PostMapping("/generatePacketGf")
    public Response generatePacketGf(@RequestBody GeneratePacketDto generatePacketDto) {
        return Response.success(authorityPacketService.generatePacketGf(generatePacketDto));
    }

    /**
     * 根据报文压缩包名称，下载报文包
     *
     * @param packetZipName
     * @param response
     * @return
     */
    @GetMapping("/downloadZipPacket")
    public void downloadZipPacket(@RequestParam String packetZipName, HttpServletResponse response) {
        InputStream inputStream = null;
        ServletOutputStream outputStream = null;
        try {
            response.addHeader("Content-Disposition", "attachment;filename=" + packetZipName);
            response.setContentType("application/octet-stream");
            inputStream = authorityPacketService.downloadZipPacket(packetZipName);
            outputStream = response.getOutputStream();
            outputStream.write(IoUtil.readBytes(inputStream));
            outputStream.flush();
            outputStream.close();
            inputStream.close();
        } catch (IOException e) {
            throw new CustomizedException("报文包下载失败");
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                log.error("报文包下载，关闭流失败");
            }
        }
    }

    /**
     * 通过ftp上传报文包到服务器
     * @param packetZipName
     * @param remotePath
     * @return
     */
    @GetMapping("/ftpUpload")
    public Response ftpUpload(@RequestParam String packetZipName, @RequestParam String remotePath) {
        authorityPacketService.ftpUpload(packetZipName, remotePath);
        return Response.success();
    }

    /**
     * 保存常用信息
     * @return
     */
    @PostMapping("/saveCommonInfo")
    public Response saveCommonInfo(@RequestBody PacketCommonInfo packetCommonInfo) {
        authorityPacketService.saveCommonInfo(packetCommonInfo);
        return Response.success();
    }

    /**
     * 获取常用信息
     * @param packetCommonInfo
     * @return
     */
    @PostMapping("/getCommonInfo")
    public Response getCommonInfo(@RequestBody PacketCommonInfo packetCommonInfo) {
        return Response.success(authorityPacketService.getCommonInfo(packetCommonInfo));
    }

    /**
     * 修改常用信息
     * @param packetCommonInfo
     * @return
     */
    @PostMapping("/editCommonInfo")
    public Response editCommonInfo(@RequestBody PacketCommonInfo packetCommonInfo) {
        authorityPacketService.editCommonInfo(packetCommonInfo);
        return Response.success();
    }

    /**
     * 删除常用信息
     * @param id
     * @return
     */
    @GetMapping("/deleteCommonInfo")
    public Response deleteCommonInfo(@RequestParam String id) {
        authorityPacketService.deleteCommonInfo(id);
        return Response.success();
    }

    /**
     * 使用llm生成xml文档
     * @param generatePacketLLMDto
     * @return
     */
    @PostMapping("/llmGenerate")
    public Response llmGenerate(@RequestBody GeneratePacketLLMDto generatePacketLLMDto) {
        return Response.success(authorityPacketService.llmGenerate(generatePacketLLMDto));
    }

}
