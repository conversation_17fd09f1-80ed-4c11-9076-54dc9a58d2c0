package com.bqd.serviceenr.service;

import com.bqd.model.common.PageDto;
import com.bqd.model.esbnetworkreplay.*;
import com.bqd.model.esbnetworkreplay.EsbRecordDetail;
import com.bqd.model.esbnetworkreplay.EsbRecordInfo;

import java.util.List;

public interface RecordManagementService {
    PageDto<EsbRecordInfo> fetchInfoByCondition(Integer pageNo, Integer pageSize, EsbRecordInfoDto recordInfoDto);

    List<EsbRecordInfo> fetchInfoColData(List<String> colNameList);

    EsbRecordDetail fetchDetailColData(String recordId, List<String> fieldList);

    void deleteRecord(String interfaceId, String infoId, Long reqtStartTime, Long reqtEndTime);
}
