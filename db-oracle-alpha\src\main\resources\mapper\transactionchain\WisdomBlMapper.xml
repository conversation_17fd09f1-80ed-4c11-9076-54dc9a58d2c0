<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.transactionchain.mapper.WisdomBlMapper">

    <resultMap id="wisdomBl" type="com.bqd.model.transactionchain.WisdomBl">
        <result property="bId" column="B_ID"/>
        <result property="actionDesc" column="ACTION_DESC"/>
        <result property="logicId" column="LOGIC_ID"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO WISDOM_BL (B_ID, ACTION_DESC, LOGIC_ID)
        VALUES (#{bId}, #{actionDesc}, #{logicId})
    </insert>

    <delete id="truncateTable">
        TRUNCATE TABLE WISDOM_BL
    </delete>

    <select id="selectByBId" resultMap="wisdomBl">
        SELECT *
        FROM WISDOM_BL
        WHERE B_ID = #{bId}
    </select>

    <select id="selectByLikeLogicId" resultMap="wisdomBl">
        SELECT *
        FROM WISDOM_BL
        WHERE LOGIC_ID LIKE CONCAT('%', CONCAT(#{logicId}, '%'))
    </select>
</mapper>