package com.bqd.scheduler.controller;

import com.bqd.base.response.Response;
import com.bqd.scheduler.service.KettleSchedulerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-01-15
 */
@RestController
@RequestMapping("/kettle")
public class KettleSchedulerController {

    @Autowired
    private KettleSchedulerService kettleSchedulerService;

    /**
     * 新核心日报抽数
     * @return
     */
    @GetMapping("/xhxRb")
    public Response xhxRb() {
        kettleSchedulerService.xhxRb();
        return Response.success();
    }

}
