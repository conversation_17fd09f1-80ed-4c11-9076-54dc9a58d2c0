package com.bqd.model.esbdata;

import com.bqd.model.common.OraclePagedCommonParam;
import lombok.*;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-11-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
public class EsbFieldEnumDto extends OraclePagedCommonParam {
    private String id;
    private String interfaceId;
    private String infoId;
    private String field;
    private String enumValue;
    private String content;
}
