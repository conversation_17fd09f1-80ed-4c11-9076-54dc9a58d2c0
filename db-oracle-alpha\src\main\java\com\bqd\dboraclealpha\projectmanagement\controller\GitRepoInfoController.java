package com.bqd.dboraclealpha.projectmanagement.controller;

import com.bqd.dboraclealpha.projectmanagement.mapper.GitRepoInfoMapper;
import com.bqd.model.projectmanagement.GitRepoInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-09
 */
@RestController
@RequestMapping("/projectManagement/gitRepoInfo")
public class GitRepoInfoController implements com.bqd.base.rpc.projectmanagement.GitRepoInfoMapper {

    @Autowired
    private GitRepoInfoMapper gitRepoInfoMapper;

    @Override
    @PostMapping("/insert")
    public void insert(@RequestBody GitRepoInfo gitInfo) {
        gitRepoInfoMapper.insert(gitInfo);
    }

    @Override
    @PostMapping("/selectByCondition")
    public List<GitRepoInfo> selectByCondition(@RequestBody GitRepoInfo gitInfo) {
        return gitRepoInfoMapper.selectByCondition(gitInfo);
    }

    @Override
    @PostMapping("/updateById")
    public void updateById(@RequestBody GitRepoInfo gitRepoInfo) {
        gitRepoInfoMapper.updateById(gitRepoInfo);
    }

    @Override
    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String id) {
        gitRepoInfoMapper.deleteById(id);
    }
}
