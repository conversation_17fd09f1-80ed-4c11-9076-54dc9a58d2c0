package com.bqd.dboraclealpha.esbnetworkreplay.mapper;

import com.bqd.model.esbnetworkreplay.AssetDetailDto;
import com.bqd.model.esbnetworkreplay.EsbAssetDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EsbAssetDetailMapper {
    void insert(EsbAssetDetail assetDetail);

    void deleteById(String assetId);

    EsbAssetDetail selectFieldById(@Param("id") String id, @Param("fieldList") List<String> fieldList);

    List<AssetDetailDto> selectByInterfaceIdAndVersionNumber(@Param("interfaceId") String interfaceId, @Param("versionNumber") String versionNumber);
}
