package com.bqd.integratedtester.financialstatement.controller;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.bqd.base.response.Response;
import com.bqd.enummodel.FinancialStatementTypeEnum;
import com.bqd.integratedtester.financialstatement.service.FinancialStatementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * @Description: 计划财务部-资金报表比对（智慧办公报表比对/协同办公报表比对）
 * @Author: wangzirui
 * @CreateTime: 2024-12-23
 */
@RestController
@RequestMapping("/financialStatement")
public class FinancialStatementController {

    @Autowired
    private FinancialStatementService financialStatementService;

    /**
     * 上传文件接口
     *
     * @param fileIndex
     * @param multipartFile
     * @return
     */
    @PostMapping("/upload")
    public Response upload(@RequestParam("fileIndex") String fileIndex, @RequestParam(required = false) String uploadId, @RequestParam("file") MultipartFile multipartFile) {
        try {
            String fileStr = IoUtil.read(multipartFile.getInputStream(), StandardCharsets.UTF_8);
            return Response.success(financialStatementService.upload(fileIndex, uploadId, fileStr));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除上传的文件
     *
     * @param fileIndex
     * @return
     */
    @GetMapping("/cleanUpload")
    public Response cleanUpload(@RequestParam String uploadId, @RequestParam("fileIndex") String fileIndex) {
        financialStatementService.cleanUpload(uploadId, fileIndex);
        return Response.success();
    }

    /**
     * 获取可比对的报表类型
     * @return
     */
    @GetMapping("/getCompareTypes")
    public Response getCompareTypes() {
        return Response.success(financialStatementService.getCompareTypes());
    }

    /**
     * 报表比对
     * @param fileType
     * @param uploadId
     * @param valueDiff
     * @return
     */
    @GetMapping("/compare")
    public Response compare(@RequestParam String fileType, @RequestParam String uploadId, @RequestParam Integer valueDiff) {
        String result = null;
        //资金状况日报表_支行/资金状况日报表_分行
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.FUND_STATUS.getValue())) {
            result = financialStatementService.fundStatusCompare(uploadId, valueDiff);
        }
        //资产负债简报（人民银行口径）
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.BALANCE_SHEET_PBOC.getValue())) {
            result = financialStatementService.balanceSheetComparePboc(uploadId, valueDiff);
        }
        //资产负债简报（银保监口径）
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.BALANCE_SHEET_BIR.getValue())) {
            result = financialStatementService.balanceSheetCompareBir(uploadId, valueDiff);
        }
        //资金状况日报表-全行
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.FUND_STATUS_ALL.getValue())) {
            result = financialStatementService.fundStatusAllCompare(uploadId, valueDiff);
        }
        //零售--存款日报表/零售--存款日报表_含网点
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.RETAIL_DEPOSIT.getValue())) {
            result = financialStatementService.retailDepositCompare(uploadId, valueDiff);
        }
        //零售-金融资产报表日报表
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.RETAIL_FINANCIAL_ASSET.getValue())) {
            result = financialStatementService.retailFinancialAssetCompare(uploadId, valueDiff);
        }
        //零售客户月日均金融资产分层日报表
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.RETAIL_CUSTOMER_AVG_ASSET.getValue())) {
            result = financialStatementService.retailCustomerAvgAssetCompare(uploadId, valueDiff);
        }
        //个贷--个人贷款日报表/个贷--个人普惠贷款日报表
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.PERSONAL_LOAN.getValue())) {
            result = financialStatementService.personalLoanCompare(uploadId, valueDiff);
        }
        //各分支机构日均存款情况表
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.BRANCH_DAILY_DEPOSITS.getValue())) {
            result = financialStatementService.branchDailyDepositsCompare(uploadId, valueDiff);
        }
        //指标变化明细日报表
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.INDICATOR_CHANGE.getValue())) {
            result = financialStatementService.indicatorChangeCompare(uploadId, valueDiff);
        }
        //信用风险监测日报表
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.CREDIT_RISK_MONITORING.getValue())) {
            result = financialStatementService.creditRiskMonitoringCompare(uploadId, valueDiff);
        }
        //全行大客户列表/分支行大客户列表
        if (StrUtil.equals(fileType, FinancialStatementTypeEnum.MAJOR_CUSTOMER.getValue())) {
            result = financialStatementService.majorCustomerCompare(uploadId, valueDiff);
        }
        return Response.success(result);
    }

    /**
     * 清除已上传文件的缓存
     * @return
     */
    @GetMapping("/clearUploadCache")
    public Response clearUploadCache() {
        financialStatementService.clearUploadCache();
        return Response.success();
    }

}
