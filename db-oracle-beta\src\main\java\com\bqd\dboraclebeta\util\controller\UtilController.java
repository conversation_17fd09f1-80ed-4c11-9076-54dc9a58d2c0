package com.bqd.dboraclebeta.util.controller;

import com.bqd.dboraclebeta.util.mapper.UtilMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-25
 */
@RestController
@RequestMapping("/util")
public class UtilController {

    @Autowired
    private UtilMapper utilMapper;

    @GetMapping("/truncateTable")
    public void truncateTable(@RequestParam String tableName) {
        utilMapper.truncateTable(tableName);
    }

}
