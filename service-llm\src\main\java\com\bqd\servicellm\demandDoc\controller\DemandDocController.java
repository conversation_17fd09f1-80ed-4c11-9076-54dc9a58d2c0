package com.bqd.servicellm.demandDoc.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.bqd.base.response.Response;
import com.bqd.servicellm.demandDoc.service.DemandDocService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

@RestController
@RequestMapping("/demandDoc")
@Log4j2
public class DemandDocController {



    @Autowired
    @Qualifier("externalTaskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    DemandDocService demandDocService;

    @PostMapping("/upload")
    public Response uploadWord(@RequestParam("file") MultipartFile file) throws IOException {
        log.info("Received file: {}", file.getOriginalFilename());
        byte[] fileBytes = file.getBytes();
        String fileName = file.getOriginalFilename();
        demandDocService.processWordAsync(fileBytes, fileName);
        return Response.success("文件接收成功，正在异步处理！");
    }

    @GetMapping("/getFileList")
    public Response getFileList(@RequestParam int page, @RequestParam int size) {
        return Response.success(demandDocService.getFileList(page, size));
    }

    @GetMapping("/deleteFileById")
    public Response deleteFileById(@RequestParam String id) {
        int result = demandDocService.deleteFileById(id);
        if (result == 1) {
            return Response.success("删除成功！");
        } else {
            return Response.fail("删除失败！");
        }
    }

    @GetMapping("/getDetailById")
    public Response getDetailById(@RequestParam String id) {
        JSONArray result = demandDocService.getDetailById(id);
        if (result != null) {
            return Response.success(result);
        }
        return Response.fail("获取详情失败！");
    }

    @GetMapping("/getFunctionDetailById")
    public Response getFunctionDetailById(@RequestParam String id) {
        JSONArray result = demandDocService.getFunctionDetailById(id);
        if (result != null) {
            return Response.success(result);
        }
        return Response.fail("获取详情失败！");
    }

    @PostMapping("/updateFunctionPoint")
    public Response updateFunctionPoint(@RequestBody Map<String, Object> payload) {
        String id = (String) payload.get("id");
        String content = (String) payload.get("content");
        int result = demandDocService.updateFunctionPoint(id, content);
        return result == 1 ? Response.success("更新成功！") : Response.fail("更新失败！");
    }


    @PostMapping("/generateTestCase")
    public Response generateTestCase(@RequestBody List<String> taskList) {
        for (String taskData : taskList) {
            demandDocService.processCaseGenTask(taskData); // 异步执行
        }
        // 立即返回响应，不等待任务执行
        return Response.success("任务已提交，后台正在处理");
    }

    @GetMapping("/getTestCaseByFunctionId")
    public Response getTestCaseByFunctionId(@RequestParam String functionId) {
        JSONObject testCasesByFunctionId = demandDocService.getTestCasesByFunctionId(functionId);
        if (testCasesByFunctionId != null) {
            return Response.success(testCasesByFunctionId);
        }
        return Response.fail("获取详情失败！");
    }

    @GetMapping("/threadPoolStatus")
    public Map<String, Object> getThreadPoolStatus() {
        ThreadPoolExecutor executor = taskExecutor.getThreadPoolExecutor();

        Map<String, Object> status = new HashMap<>();
        status.put("corePoolSize", executor.getCorePoolSize());
        status.put("maximumPoolSize", executor.getMaximumPoolSize());
        status.put("activeCount", executor.getActiveCount()); // 正在执行的任务数量
        status.put("queueSize", executor.getQueue().size()); // 队列中等待的任务数量
        status.put("completedTaskCount", executor.getCompletedTaskCount()); // 已完成任务数
        status.put("totalTaskCount", executor.getTaskCount()); // 总任务数（已完成 + 执行中 + 等待中）

        return status;
    }

}
