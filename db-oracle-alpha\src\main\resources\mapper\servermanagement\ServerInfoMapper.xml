<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.servermanagement.mapper.ServerInfoMapper">

    <resultMap id="serverInfo" type="com.bqd.model.servermanagement.ServerInfo">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="SERVER_NAME" property="serverName" javaType="java.lang.String"/>
        <result column="SERVER_IP" property="serverIp" javaType="java.lang.String"/>
        <result column="USERNAME" property="username" javaType="java.lang.String"/>
        <result column="PW" property="pw" javaType="java.lang.String"/>
        <result column="ENVIRONMENT" property="environment" javaType="java.lang.String"/>
        <result column="AUTH_TYPE" property="authType" javaType="java.lang.String"/>
        <result column="REMARK" property="remark" javaType="java.lang.String"/>
        <result column="ADD_TIME" property="addTime" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO SERVER_INFO
        VALUES (#{id}, #{serverName,jdbcType=VARCHAR}, #{serverIp,jdbcType=VARCHAR}, #{username,jdbcType=VARCHAR},
                #{pw,jdbcType=VARCHAR}, #{environment,jdbcType=VARCHAR}, #{authType,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP})
    </insert>

    <select id="count" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM SERVER_INFO
        WHERE 1=1
        <if test="id != null and id.length() != 0">
            AND ID = #{id}
        </if>
        <if test="serverName != null and serverName.length() != 0">
            AND SERVER_NAME LIKE CONCAT('%', CONCAT(#{serverName}, '%'))
        </if>
        <if test="environment != null and environment.length() != 0">
            AND ENVIRONMENT = #{environment}
        </if>
        <if test="serverIp != null and serverIp.length() != 0">
            AND SERVER_IP = #{serverIp}
        </if>
        <if test="username != null and username.length() != 0">
            AND USERNAME = #{username}
        </if>
        <if test="pw != null and pw.length() != 0">
            AND PW = #{pw}
        </if>
        <if test="authType != null and authType.length() != 0">
            AND AUTH_TYPE = #{authType}
        </if>
        <if test="remark != null and remark.length() != 0">
            AND REMARK = #{remark}
        </if>
        <if test="addTime != null">
            AND ADD_TIME = #{addTime}
        </if>
    </select>

    <select id="selectPaged" resultMap="serverInfo">
        SELECT *
        FROM (SELECT ROWNUM as ROW_NUM, t1.* FROM SERVER_INFO t1 WHERE 1=1
        <if test="serverInfo.id != null and serverInfo.id.length() != 0">
            AND ID = #{serverInfo.id}
        </if>
        <if test="serverInfo.serverName != null and serverInfo.serverName.length() != 0">
            AND SERVER_NAME LIKE CONCAT('%', CONCAT(#{serverInfo.serverName}, '%'))
        </if>
        <if test="serverInfo.environment != null and serverInfo.environment.length() != 0">
            AND ENVIRONMENT = #{serverInfo.environment}
        </if>
        <if test="serverInfo.serverIp != null and serverInfo.serverIp.length() != 0">
            AND SERVER_IP = #{serverInfo.serverIp}
        </if>
        <if test="serverInfo.username != null and serverInfo.username.length() != 0">
            AND USERNAME = #{serverInfo.username}
        </if>
        <if test="serverInfo.pw != null and serverInfo.pw.length() != 0">
            AND PW = #{serverInfo.pw}
        </if>
        <if test="serverInfo.authType != null and serverInfo.authType.length() != 0">
            AND AUTH_TYPE = #{serverInfo.authType}
        </if>
        <if test="serverInfo.remark != null and serverInfo.remark.length() != 0">
            AND REMARK = #{serverInfo.remark}
        </if>
        <if test="serverInfo.addTime != null">
            AND ADD_TIME = #{serverInfo.addTime}
        </if>
        )
        WHERE ROW_NUM >= #{startRow}
        and #{endRow} >= ROW_NUM
    </select>

    <select id="selectByCondition" resultMap="serverInfo">
        SELECT * FROM SERVER_INFO WHERE 1=1
        <if test="id != null and id.length() != 0">
            AND ID = #{id}
        </if>
        <if test="serverName != null and serverName.length() != 0">
            AND SERVER_NAME LIKE CONCAT('%', CONCAT(#{serverName}, '%'))
        </if>
        <if test="environment != null and environment.length() != 0">
            AND ENVIRONMENT = #{environment}
        </if>
        <if test="serverIp != null and serverIp.length() != 0">
            AND SERVER_IP = #{serverIp}
        </if>
        <if test="username != null and username.length() != 0">
            AND USERNAME = #{username}
        </if>
        <if test="pw != null and pw.length() != 0">
            AND PW = #{pw}
        </if>
        <if test="authType != null and authType.length() != 0">
            AND AUTH_TYPE = #{authType}
        </if>
        <if test="remark != null and remark.length() != 0">
            AND REMARK = #{remark}
        </if>
        <if test="addTime != null">
            AND ADD_TIME = #{addTime}
        </if>
    </select>

    <update id="updateById">
        UPDATE SERVER_INFO
        <set>
            SERVER_NAME = #{serverName,jdbcType=VARCHAR},
            SERVER_IP = #{serverIp,jdbcType=VARCHAR},
            USERNAME = #{username,jdbcType=VARCHAR},
            PW = #{pw,jdbcType=VARCHAR},
            ENVIRONMENT = #{environment,jdbcType=VARCHAR},
            AUTH_TYPE = #{authType,jdbcType=VARCHAR},
            REMARK = #{remark,jdbcType=VARCHAR},
            ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
        </set>
        WHERE ID = #{id}
    </update>

    <delete id="deleteById">
        DELETE
        FROM SERVER_INFO
        WHERE ID = #{id}
    </delete>

</mapper>