package com.bqd.serviceenr.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import com.bqd.model.common.PageDto;
import com.bqd.model.esbnetworkreplay.*;
import com.bqd.base.rpc.esbnetworkreplay.EsbAssetDetailMapper;
import com.bqd.base.rpc.esbnetworkreplay.EsbAssetInfoMapper;
import com.bqd.serviceenr.service.AssetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-25
 */
@Service
public class AssetServiceImpl implements AssetService {

    @Autowired
    private EsbAssetInfoMapper esbAssetInfoMapper;
    @Autowired
    private EsbAssetDetailMapper esbAssetDetailMapper;

    @Override
    public List<EsbAssetInfoDto> getAssetInfoFieldValue(List<String> fieldList) {
        return esbAssetInfoMapper.selectFieldValue(fieldList).stream().filter(ObjectUtil::isNotNull).collect(Collectors.toList());
    }

    @Override
    public List<String> getInfoFieldValueByInterfaceId(String interfaceId, String field) {
        return esbAssetInfoMapper.selectFieldValueByInterfaceId(interfaceId, field);
    }

    @Override
    public PageDto<EsbAssetInfoDto> getPagedAssetInfo(AssetInfoSearchDto assetInfoSearchDto) {
        int totalCount = esbAssetInfoMapper.countByCondition(assetInfoSearchDto);
        assetInfoSearchDto.setStartRow(PageUtil.getStart(assetInfoSearchDto.getPageNo(), assetInfoSearchDto.getPageSize()) + 1);
        assetInfoSearchDto.setEndRow(PageUtil.getEnd(assetInfoSearchDto.getPageNo(), assetInfoSearchDto.getPageSize()));
        int pageCount = PageUtil.totalPage(totalCount, assetInfoSearchDto.getPageSize());
        List<EsbAssetInfoDto> esbAssetInfoDtoList = esbAssetInfoMapper.selectPagedByCondition(assetInfoSearchDto);
        return new PageDto<>(assetInfoSearchDto.getPageNo(), assetInfoSearchDto.getPageSize(), pageCount, totalCount, esbAssetInfoDtoList);
    }

    @Override
    public PageDto<EsbAssetInfo> getPagedAssetInfoDetail(AssetInfoDetailSearchDto assetInfoDetailSearchDto) {
        int totalCount = esbAssetInfoMapper.countInfoDetailByCondition(assetInfoDetailSearchDto);
        assetInfoDetailSearchDto.setStartRow(PageUtil.getStart(assetInfoDetailSearchDto.getPageNo(), assetInfoDetailSearchDto.getPageSize()));
        assetInfoDetailSearchDto.setEndRow(PageUtil.getEnd(assetInfoDetailSearchDto.getPageNo(), assetInfoDetailSearchDto.getPageSize()));
        int pageCount = PageUtil.totalPage(totalCount, assetInfoDetailSearchDto.getPageSize());
        List<EsbAssetInfo> esbAssetInfoList = esbAssetInfoMapper.selectInfoDetailPagedByCondition(assetInfoDetailSearchDto);
        return new PageDto<>(assetInfoDetailSearchDto.getPageNo(), assetInfoDetailSearchDto.getPageSize(), pageCount, totalCount, esbAssetInfoList);
    }

    @Override
    public EsbAssetDetail getAssetDetailFieldValue(String id, List<String> fieldList) {
        return esbAssetDetailMapper.selectFieldById(id, fieldList);
    }

    @Override
    public void deleteAsset(String id) {
        esbAssetInfoMapper.deleteById(id);
        esbAssetDetailMapper.deleteById(id);
    }
}
