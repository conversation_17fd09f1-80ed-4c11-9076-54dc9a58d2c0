package com.bqd.dboraclealpha.transactionchain.controller;

import com.bqd.dboraclealpha.transactionchain.mapper.WisdomMvcMapper;
import com.bqd.model.transactionchain.WisdomMvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
@RestController
@RequestMapping("/transactionChain/wisdom/mvc")
public class WisdomMvcController implements com.bqd.base.rpc.transactionchain.WisdomMvcMapper {

    @Autowired
    private WisdomMvcMapper wisdomMvcMapper;

    @Override
    @PostMapping("/insert")
    public void insert(@RequestBody WisdomMvc wisdomMvc) {
        wisdomMvcMapper.insert(wisdomMvc);
    }

    @Override
    @GetMapping("/selectAll")
    public List<WisdomMvc> selectAll() {
        return wisdomMvcMapper.selectAll();
    }

    @Override
    @GetMapping("/selectByMvcId")
    public WisdomMvc selectByMvcId(@RequestParam String mvcId) {
        return wisdomMvcMapper.selectByMvcId(mvcId);
    }

    @Override
    @GetMapping("/selectByLikeMvcId")
    public List<WisdomMvc> selectByLikeMvcId(@RequestParam String mvcId) {
        return wisdomMvcMapper.selectByLikeMvcId(mvcId);
    }

    @Override
    @GetMapping("/selectByLikeDescription")
    public List<WisdomMvc> selectByLikeDescription(@RequestParam String description) {
        return wisdomMvcMapper.selectByLikeDescription(description);
    }

    @Override
    @GetMapping("/selectByBId")
    public WisdomMvc selectByBId(@RequestParam String bId) {
        return wisdomMvcMapper.selectByBId(bId);
    }

    @Override
    @GetMapping("/selectZtByQz")
    public List<WisdomMvc> selectZtByQz(@RequestParam String qzName) {
        return wisdomMvcMapper.selectZtByQz(qzName);
    }
}
