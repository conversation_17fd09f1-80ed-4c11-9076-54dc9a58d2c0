package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.RecordDbEnvMapper;
import com.bqd.model.esbnetworkreplay.RecordDBEnv;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-13
 */
@RestController
@RequestMapping("/enr/config/dbEnv")
public class RecordDbEnvController {

    @Autowired
    private RecordDbEnvMapper recordDbEnvMapper;

    @GetMapping("/selectAllOrderByCreateTimeDESC")
    public List<RecordDBEnv> selectAllOrderByCreateTimeDESC() {
        return recordDbEnvMapper.selectAllOrderByCreateTimeDESC();
    }

    @PostMapping("/insert")
    public void insert(@RequestBody RecordDBEnv recordDBEnv) {
        recordDbEnvMapper.insert(recordDBEnv);
    }

    @PostMapping("/updateById")
    public void updateById(@RequestBody RecordDBEnv recordDBEnv) {
        recordDbEnvMapper.updateById(recordDBEnv);
    }

    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String id) {
        recordDbEnvMapper.deleteById(id);
    }

    @GetMapping("/selectById")
    public RecordDBEnv selectById(@RequestParam String id) {
        return recordDbEnvMapper.selectById(id);
    }

}
