package com.bqd.serviceserveroperation;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@EnableFeignClients(basePackages = "com.bqd.base.rpc")
@ComponentScan(basePackages = {"com.bqd.serviceserveroperation", "com.bqd.base"})
public class ServiceServerOperationApplication {

    public static void main(String[] args) {
        SpringApplication.run(ServiceServerOperationApplication.class, args);
    }

}
