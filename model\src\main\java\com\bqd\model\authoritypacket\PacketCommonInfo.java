package com.bqd.model.authoritypacket;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-08
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PacketCommonInfo {
    /**
     * id
     */
    private String id;

    /**
     * 常用信息名称
     */
    private String infoName;

    /**
     * 模板报文包类型
     */
    private String packetType;

    /**
     * 模板标签名称
     */
    private String tagName;

    /**
     * 常用信息内容
     */
    private String infoContent;
}
