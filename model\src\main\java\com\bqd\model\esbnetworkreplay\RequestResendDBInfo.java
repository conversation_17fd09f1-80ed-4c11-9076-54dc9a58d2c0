package com.bqd.model.esbnetworkreplay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-07-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RequestResendDBInfo {
    private String id;
    private String databaseName;
    private String connectionUrl;
    private String userName;
    private String password;
    private String tableName;
    private String insertTime;
}
