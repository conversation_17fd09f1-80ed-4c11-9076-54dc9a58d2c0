package com.bqd.serviceserveroperation.controller;

import cn.hutool.core.io.IoUtil;
import com.bqd.base.response.Response;
import com.bqd.model.chaos.ChaosInjectionDto;
import com.bqd.model.chaos.ChaosServerDto;
import com.bqd.model.common.CommonReqtDto;
import com.bqd.serviceserveroperation.service.ChaosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

/**
 * @Description: 混沌工程控制台
 * @Author: wangzirui
 * @CreateTime: 2024-12-05
 */
@RestController
@RequestMapping("/chaos")
public class ChaosController {

    @Autowired
    private ChaosService chaosService;

    /**
     * 添加混沌测试服务器
     * 
     * @param serverInfoId 服务器信息ID
     * @return Response
     */
    @GetMapping("/addChaosServer")
    public Response addChaosServer(@RequestParam String serverInfoId) {
        chaosService.addChaosServer(serverInfoId);
        return Response.success();
    }

    /**
     * 获取混沌测试服务器列表
     * 
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param chaosServerDto 混沌测试服务器查询条件
    **/
    @PostMapping("/list")
    public Response chaosServerList(@RequestParam Integer pageNo, @RequestParam Integer pageSize, @RequestBody ChaosServerDto chaosServerDto) {
        return Response.success(chaosService.chaosServerList(pageNo, pageSize, chaosServerDto));
    }

    /**
     * 下载混沌测试脚本
     *
     * @param scriptName
     * @param response
     */
    @GetMapping("/downloadScripts")
    public void downloadScript(@RequestParam String scriptName, HttpServletResponse response) {
        response.reset();
        response.setHeader("Content-Disposition", "attachment;filename=" + scriptName);
        try {
            InputStream inputStream = chaosService.downloadScript(scriptName);
            ServletOutputStream outputStream = response.getOutputStream();
            response.setContentType("application/octet-stream");
            outputStream.write(IoUtil.readBytes(inputStream));
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 一键部署
     *
     * @param serverInfoId
     * @return
     */
    @GetMapping("/setup")
    public Response setup(@RequestParam String serverInfoId) {
        chaosService.setup(serverInfoId);
        return Response.success();
    }

    /**
     * 根据类型获取正在执行的混沌事件
     *
     * @param type
     * @return
     */
    @GetMapping("/getExecutingList")
    public Response getExecutingList(@RequestParam(required = false) String type) {
        return Response.success(chaosService.getExecutingList(type));
    }

    /**
     * 获取默认网卡名称
     *
     * @param serverInfoId
     * @return
     */
    @GetMapping("/getDefaultNicName")
    public Response getDefaultNicName(@RequestParam String serverInfoId) {
        return Response.success(chaosService.getDefaultNicName(serverInfoId));
    }

    /**
     * 混沌故障注入
     *
     * @param chaosInjectionDto
     * @return
     */
    @PostMapping("/injection")
    public Response injection(@RequestBody ChaosInjectionDto chaosInjectionDto) {
        chaosService.injection(chaosInjectionDto);
        return Response.success();
    }

    /**
     * http网络单向丢包
     *
     * @param commonReqtDto
     * @return
     */
    @PostMapping("/httpOnewayLoss")
    public Response httpOnewayLoss(@RequestBody CommonReqtDto commonReqtDto) {
        chaosService.httpOnewayLoss(commonReqtDto);
        return Response.success();
    }

    /**
     * 销毁故障
     *
     * @param id
     * @return
     */
    @GetMapping("/destroy")
    public Response destroy(@RequestParam String id) {
        chaosService.destroy(id);
        return Response.success();
    }

    /**
     * 检查探针状态
     *
     * @param serverInfoId 服务器ID
     * @return
     */
    @GetMapping("/checkStatus")
    public Response checkStatus(@RequestParam String serverInfoId) {
        return Response.success(chaosService.checkStatus(serverInfoId));
    }

    @GetMapping("/delete/{id}")
    public Response deleteById(@PathVariable String id) {
        chaosService.deleteById(id);
        return Response.success();
    }

}
