package com.bqd.serviceserveroperation.controller;

import com.bqd.base.response.Response;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-12-09
 */
@RestController
@RequestMapping("/")
public class HealthCheckController {

    @GetMapping("/healthCheck")
    public Response healthCheck() {
        return Response.success("service-server-operation");
    }

}
