<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.dbmanagement.mapper.DbConnectionInfoMapper">

    <resultMap id="dbConnectionInfo" type="com.bqd.model.dbmanagement.DbConnectionInfo">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="DB_NAME" property="dbName" javaType="java.lang.String"/>
        <result column="DB_TYPE" property="dbType" javaType="java.lang.String"/>
        <result column="URL" property="url" javaType="java.lang.String"/>
        <result column="USERNAME" property="username" javaType="java.lang.String"/>
        <result column="PASSWORD" property="password" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectByCondition" resultMap="dbConnectionInfo">
        SELECT * FROM DB_CONNECTION_INFO WHERE 1=1
        <if test="id != null">
            AND ID = #{id}
        </if>
        <if test="dbName != null and dbName.length != 0">
            AND DB_NAME = #{dbName}
        </if>
        <if test="dbType != null and dbType.length != 0">
            AND DB_TYPE = #{dbType}
        </if>
        <if test="url != null and url.length != 0">
            AND URL = #{url}
        </if>
        <if test="username != null and username.length != 0">
            AND USERNAME = #{username}
        </if>
        <if test="password != null and password.length != 0">
            AND PASSWORD = #{password}
        </if>

    </select>

    <insert id="insert">
        INSERT INTO DB_CONNECTION_INFO(ID, DB_NAME, DB_TYPE, URL, USERNAME, PASSWORD)
        VALUES (#{id}, #{dbName,jdbcType=VARCHAR}, #{dbType,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},
                #{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR})
    </insert>

    <update id="updateById">
        UPDATE DB_CONNECTION_INFO
        <set>
            <if test="dbName != null and dbName.length != 0">
                DB_NAME = #{dbName,jdbcType=VARCHAR},
            </if>
            <if test="dbType != null and dbType.length != 0">
                DB_TYPE = #{dbType,jdbcType=VARCHAR},
            </if>
            <if test="url != null and url.length != 0">
                URL = #{url,jdbcType=VARCHAR},
            </if>
            <if test="username != null and username.length != 0">
                USERNAME = #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null and password.length != 0">
                PASSWORD = #{password,jdbcType=VARCHAR},
            </if>

        </set>
        WHERE ID = #{id}
    </update>

    <delete id="deleteById">
        DELETE
        FROM DB_CONNECTION_INFO
        WHERE ID = #{id}
    </delete>

</mapper>
