server:
  port: 25999
  servlet:
    context-path: /api

spring:
  profiles:
    active: dev
  application:
    name: gateway
  cloud:
    nacos:
      discovery:
        server-addr: 10.238.145.164:8848
        username: nacos
        password: nacos
    gateway:
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origin-patterns:
              - "*"
            allowedHeaders: "*"
            allowedMethods: "*"
            allow-credentials: true
      routes:
        - id: service-esb-data # 路由编号
          uri: lb://service-esb-data # 路由到的目标地址
          predicates: # 断言，路由匹配条件
            - Path=/api/service/esb-data/**
          filters: # 过滤器，对请求进行拦截
            - RewritePath=/api/service/esb-data/(?<segment>.*), /service/esb-data/$\{segment}

        - id: service-server-operation
          uri: lb://service-server-operation
          predicates:
            - Path=/api/service/server-operation/**
          filters:
            - RewritePath=/api/service/server-operation/(?<segment>.*), /service/server-operation/$\{segment}

        - id: service-enr
          uri: lb://service-enr
          predicates:
            - Path=/api/service/enr/**
          filters:
            - RewritePath=/api/service/enr/(?<segment>.*), /service/enr/$\{segment}

        - id: service-integrated-tester
          uri: lb://service-integrated-tester
          predicates:
            - Path=/api/service/integrated-tester/**
          filters:
            - RewritePath=/api/service/integrated-tester/(?<segment>.*), /service/integrated-tester/$\{segment}

        - id: service-llm
          uri: lb://service-llm
          predicates:
            - Path=/api/service/llm/**
          filters:
            - RewritePath=/api/service/llm/(?<segment>.*), /service/llm/$\{segment}

        - id: scheduler
          uri: lb://scheduler
          predicates:
            - Path=/api/scheduler/**
          filters:
            - StripPrefix=1