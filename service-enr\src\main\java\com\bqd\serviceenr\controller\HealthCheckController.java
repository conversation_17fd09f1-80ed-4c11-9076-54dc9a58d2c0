package com.bqd.serviceenr.controller;

import com.bqd.base.response.Response;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 健康检查
 * @Author: wang<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-12-12
 */
@RestController
@RequestMapping("/")
public class HealthCheckController {

    @GetMapping("/healthCheck")
    public Response healthCheck() {
        return Response.success("service-enr");
    }

}
