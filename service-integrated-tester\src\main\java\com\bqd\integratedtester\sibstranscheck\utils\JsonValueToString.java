package com.bqd.integratedtester.sibstranscheck.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;

/**
 * @Description: 将给定json中的值全部转为字符串类型
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-05-13
 */
public class JsonValueToString {
    public static JSONObject deepCopyJson(JSONObject originalJson) {
        JSONObject copy = new JSONObject();
        for (String key : originalJson.keySet()) {
            Object value = originalJson.get(key);
            if (value instanceof JSONObject) {
                copy.set(key, deepCopyJson((JSONObject) value));
            } else if (value instanceof JSONArray) {
                copy.set(key, deepCopyJson((JSONArray) value));
            } else {
                copy.set(key, String.valueOf(value));
            }
        }
        return copy;
    }

    public static JSONArray deepCopyJson(JSONArray originalArray) {
        JSONArray copy = new JSONArray();
        for (Object value : originalArray) {
            if (value instanceof JSONObject) {
                copy.add(deepCopyJson((JSONObject) value));
            } else if (value instanceof JSONArray) {
                copy.add(deepCopyJson((JSONArray) value));
            } else {
                copy.add(String.valueOf(value));
            }
        }
        return copy;
    }
}
