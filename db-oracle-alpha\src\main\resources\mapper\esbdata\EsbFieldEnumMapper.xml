<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbdata.mapper.EsbFieldEnumMapper">

    <resultMap id="esbFieldEnum" type="com.bqd.model.esbdata.EsbFieldEnum">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="infoId" column="INFO_ID" jdbcType="VARCHAR"/>
        <result property="interfaceId" column="INTERFACE_ID" jdbcType="VARCHAR"/>
        <result property="field" column="FIELD" jdbcType="VARCHAR"/>
        <result property="enumValue" column="ENUM_VALUE" jdbcType="VARCHAR"/>
        <result property="content" column="CONTENT" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO TB_ESB_FIELD_ENUM(ID, INTERFACE_ID, INFO_ID, FIELD, ENUM_VALUE, CONTENT)
        VALUES (#{id}, #{interfaceId,jdbcType=VARCHAR}, #{infoId,jdbcType=VARCHAR}, #{field,jdbcType=VARCHAR},
                #{enumValue,jdbcType=VARCHAR},
                #{content,jdbcType=VARCHAR})
    </insert>

    <select id="selectFieldByInterfaceId" resultType="java.lang.String">
        SELECT DISTINCT FIELD
        FROM TB_ESB_FIELD_ENUM
        WHERE INTERFACE_ID = #{interfaceId}
    </select>

    <select id="countByCondition" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM TB_ESB_FIELD_ENUM WHERE 1=1
        <if test="interfaceId != null and interfaceId.length() != 0">
            AND INTERFACE_ID = #{interfaceId}
        </if>
        <if test="field != null and field.length() != 0">
            AND FIELD = #{field}
        </if>
    </select>

    <select id="pagedByCondition" resultMap="esbFieldEnum">
        SELECT * FROM (
        SELECT ROWNUM AS ROW_NUM, t1.* FROM (
        SELECT * FROM TB_ESB_FIELD_ENUM WHERE
        1 = 1
        <if test="interfaceId != null and interfaceId.length() != 0">
            AND INTERFACE_ID = #{interfaceId}
        </if>
        <if test="field != null and field.length() != 0">
            AND FIELD = #{field}
        </if>) t1) WHERE ROW_NUM >= #{startRow} and #{endRow} >= ROW_NUM
    </select>

    <delete id="deleteByCondition">
        DELETE
        FROM TB_ESB_FIELD_ENUM
        WHERE INTERFACE_ID = #{interfaceId}
        <if test="field != null and field.length() != 0">
            AND FIELD = #{field}
        </if>
    </delete>

</mapper>
