package com.bqd.serviceenr;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@EnableFeignClients(basePackages = {"com.bqd.base.rpc"})
@ComponentScan(basePackages = {"com.bqd.serviceenr", "com.bqd.base"})
public class ServiceEnrApplication {

    public static void main(String[] args) {
        SpringApplication.run(ServiceEnrApplication.class, args);
    }

}
