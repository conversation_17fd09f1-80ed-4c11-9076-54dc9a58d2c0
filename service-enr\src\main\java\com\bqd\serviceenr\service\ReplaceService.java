package com.bqd.serviceenr.service;

import com.bqd.model.esbnetworkreplay.EsbReplaceField;
import com.bqd.model.esbnetworkreplay.EsbReplaceVariable;

import java.util.List;

public interface ReplaceService {
    List<EsbReplaceField> getReplaceField(String interfaceId);

    List<EsbReplaceVariable> getReplaceVariable();

    void deleteReplaceField(String id);

    void addReplaceVaribale(EsbReplaceVariable esbReplaceVariable);

    void deleteReplaceVariable(String id);

    void addReplaceField(EsbReplaceField esbReplaceField);
}
