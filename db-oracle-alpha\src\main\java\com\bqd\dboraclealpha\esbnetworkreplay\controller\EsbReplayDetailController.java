package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbReplayDetailMapper;
import com.bqd.model.esbnetworkreplay.EsbReplayDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/enr/esbReplayDetail")
public class EsbReplayDetailController {

    @Autowired
    private EsbReplayDetailMapper esbReplayDetailMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody EsbReplayDetail esbReplayDetail) {
        esbReplayDetailMapper.insert(esbReplayDetail);
    }

    @GetMapping("/selectByInfoId")
    public EsbReplayDetail selectByInfoId(@RequestParam String infoId) {
        return esbReplayDetailMapper.selectByInfoId(infoId);
    }

    @GetMapping("/deleteByInfoId")
    public void deleteByInfoId(@RequestParam String infoId) {
        esbReplayDetailMapper.deleteByInfoId(infoId);
    }

    @GetMapping("/selectFieldValueByInfoId")
    public EsbReplayDetail selectFieldValueByInfoId(@RequestParam("infoId") String infoId, @RequestParam("field") String field) {
        return esbReplayDetailMapper.selectFieldValueByInfoId(infoId, field);
    }

}
