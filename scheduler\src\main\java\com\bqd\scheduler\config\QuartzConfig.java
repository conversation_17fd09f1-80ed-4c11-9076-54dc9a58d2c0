package com.bqd.scheduler.config;

import com.bqd.scheduler.jobs.FinancialStatementJob;
import com.bqd.scheduler.jobs.XhxKettleJob;
import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.TimeZone;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-01-26
 */
@Configuration
public class QuartzConfig {

    /**
     * 新核心日报kettle抽数任务
     *
     * @return
     */
    @Bean(name = "xhxKettleJD")
    public JobDetail xhxKettleJobDetail() {
        return JobBuilder.newJob(XhxKettleJob.class).withIdentity("xhxKettleJob", "test-center").storeDurably(true).build();
    }

    /**
     * 新核心日报kettle抽数任务触发器，每天17:00:30执行一次
     *
     * @return
     */
    @Bean(name = "xhxKettleTrigger")
    public Trigger xhxKettleTrigger() {
        CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule("30 00 17 * * ?").inTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return TriggerBuilder.newTrigger().forJob(xhxKettleJobDetail())
                .withIdentity("xhxKettleTrigger", "test-center")
                .withSchedule(cronScheduleBuilder)
                .build();
    }

    /**
     * 协同办公报表比对清除缓存任务
     *
     * @return
     */
    @Bean("financialStatementClearCacheJD")
    public JobDetail financialStatementClearCacheJobDetail() {
        return JobBuilder.newJob(FinancialStatementJob.class).withIdentity("financialStatementClearCacheJob", "test-center").storeDurably(true).build();
    }

    /**
     * 协同办公报表比对清除缓存任务触发器，每天00:00:00执行一次
     *
     * @return
     */
    @Bean("financialStatementClearCacheTrigger")
    public Trigger financialStatementClearCacheTrigger() {
        CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule("00 00 00 * * ?").inTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return TriggerBuilder.newTrigger().forJob(financialStatementClearCacheJobDetail())
                .withIdentity("financialStatementClearCacheTrigger", "test-center")
                .withSchedule(cronScheduleBuilder)
                .build();
    }

}
