package com.bqd.serviceesbdata.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.base.rpc.common.RedisAlphaMapper;
import com.bqd.base.tools.XmlTool;
import com.bqd.model.common.TreeDto;
import com.bqd.model.redis.RedisDto;
import com.bqd.model.samplexml.dto.SampleXmlDto;
import com.bqd.model.samplexml.dto.SampleXmlRedisDto;
import com.bqd.serviceesbdata.service.SampleXmlService;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Attribute;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-27
 */
@Service
@Slf4j
public class SampleXmlServiceImpl implements SampleXmlService {

    @Autowired
    private RedisAlphaMapper redisAlphaMapper;

    @Override
    public SampleXmlRedisDto downloadSampleXmlFromRemote(String interfaceId) {
        log.info("正在从ESB服务快速检索平台获取{}样例报文", interfaceId);
        String remoteHost = "http://***********:5000";
        String postResp = HttpUtil.post(remoteHost + "/ESBsearch", "keyword=" + interfaceId + "&keywordOption=ESB&keywordName=&keywordSys=");
        org.jsoup.nodes.Element root = Jsoup.parse(postResp).root();
        Elements resultItemList = root.getElementsByClass("resultItem");
        if (resultItemList.isEmpty()) {
            throw new CustomizedException("ESB检索平台无查询结果");
        }
        Elements ul = resultItemList.get(0).getElementsByTag("ul");
        if (ul.isEmpty()) {
            throw new CustomizedException("ESB检索平台无查询结果，该接口可能为组合服务");
        }
        SampleXmlRedisDto sampleXmlRedisDto = new SampleXmlRedisDto();
        sampleXmlRedisDto.setSampleXmlDtoList(new ArrayList<>());
        Elements a = ul.get(0).getElementsByTag("a");
        a.forEach(item -> {
            Attribute href = item.attribute("href");
            String name = StrUtil.replace(item.text(), " 下载", "");
            String content = HttpUtil.get(remoteHost + href.getValue());
            sampleXmlRedisDto.getSampleXmlDtoList().add(new SampleXmlDto(name, interfaceId, extractVersion(name), extractType(name), content));
        });
        sampleXmlRedisDto.setUpdateTime(DateUtil.now());
        redisAlphaMapper.setString(new RedisDto("esbSampleXml:" + interfaceId, JSONUtil.toJsonStr(sampleXmlRedisDto), -1));
        log.info("样例报文获取成功");
        return sampleXmlRedisDto;
    }

    private String extractVersion(String input) {
        // 正则表达式匹配V开头后面紧跟三位数字
        String regex = "V\\d{3}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group().toLowerCase(); // 返回匹配到的内容
        } else {
            return null; // 或者返回一个默认值，比如空字符串
        }
    }

    private String extractType(String input) {
        // 正则表达式匹配reqt或resp
        String regex = "(reqt|resp)";
        Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            return matcher.group().toLowerCase(); // 返回匹配到的内容
        } else {
            return null; // 或者返回一个默认值，比如空字符串
        }
    }

    @Override
    public SampleXmlRedisDto getValueByCondition(SampleXmlDto sampleXmlDto) {
        String objStr = redisAlphaMapper.getString("esbSampleXml:" + sampleXmlDto.getInterfaceId()).getValue();
        SampleXmlRedisDto sampleXmlRedisDto;
        if (StrUtil.isBlank(objStr)) {
            sampleXmlRedisDto = downloadSampleXmlFromRemote(sampleXmlDto.getInterfaceId());
        } else {
            sampleXmlRedisDto = JSONUtil.toBean(objStr, SampleXmlRedisDto.class);
        }
        sampleXmlRedisDto.setSampleXmlDtoList(sampleXmlRedisDto.getSampleXmlDtoList().stream()
                .filter(item -> {
                    if (StrUtil.isNotBlank(sampleXmlDto.getType())) {
                        return sampleXmlDto.getType().equals(item.getType());
                    }
                    return true;
                }).filter(item -> {
                    if (StrUtil.isNotBlank(sampleXmlDto.getVersionNumber())) {
                        return sampleXmlDto.getVersionNumber().equals(item.getVersionNumber());
                    }
                    return true;
                }).filter(item -> {
                    if (StrUtil.isNotBlank(sampleXmlDto.getName())) {
                        return sampleXmlDto.getName().equals(item.getName());
                    }
                    return true;
                }).collect(Collectors.toList()));
        return sampleXmlRedisDto;
    }

    @Override
    public SampleXmlDto getSampleXml(String interfaceId, String name) {
        String objStr = redisAlphaMapper.getString("esbSampleXml:" + interfaceId).getValue();
        SampleXmlRedisDto sampleXmlRedisDto;
        if (StrUtil.isBlank(objStr)) {
            sampleXmlRedisDto = downloadSampleXmlFromRemote(interfaceId);
        } else {
            sampleXmlRedisDto = JSONUtil.toBean(objStr, SampleXmlRedisDto.class);
        }
        List<SampleXmlDto> collect = sampleXmlRedisDto.getSampleXmlDtoList().stream().filter(item -> name.equals(item.getName())).collect(Collectors.toList());
        if (collect.size() != 1) {
            throw new RuntimeException("样例报文名称不存在或不唯一");
        }
        return collect.get(0);
    }

    @Override
    public void updateSampleXmlContent(SampleXmlDto sampleXmlDto) {
        SampleXmlRedisDto sampleXmlRedisDto = getValueByCondition(new SampleXmlDto(null, sampleXmlDto.getInterfaceId(), null, null, null));
        sampleXmlRedisDto.setSampleXmlDtoList(sampleXmlRedisDto.getSampleXmlDtoList().stream().map(item -> {
            if (item.getName().equals(sampleXmlDto.getName())) {
                item.setSampleXml(sampleXmlDto.getSampleXml());
                return item;
            }
            return item;
        }).collect(Collectors.toList()));
        redisAlphaMapper.setString(new RedisDto("esbSampleXml:" + sampleXmlDto.getInterfaceId(), JSONUtil.toJsonStr(sampleXmlRedisDto), -1));
    }

    @Override
    public TreeDto getFieldTree(String interfaceId, String name) {
        String sampleXml = getSampleXml(interfaceId, name).getSampleXml();
        Element rootElement = XmlTool.readXmlFromStr(sampleXml).getRootElement();
        TreeDto treeDto = new TreeDto();
        fieldTreeHelper(rootElement, "/", treeDto);
        return treeDto;
    }

    private void fieldTreeHelper(Element rootElement, String prefix, TreeDto treeDto) {
        treeDto.setId(prefix + rootElement.getName());
        treeDto.setLabel(rootElement.getName());
        for (Element element : rootElement.elements()) {
            TreeDto childTreeDto = new TreeDto();
            fieldTreeHelper(element, prefix + rootElement.getName() + "/", childTreeDto);
            if (ObjectUtil.isNull(treeDto.getChildren())) {
                treeDto.setChildren(new ArrayList<>());
            }
            treeDto.getChildren().add(childTreeDto);
        }
    }
}
