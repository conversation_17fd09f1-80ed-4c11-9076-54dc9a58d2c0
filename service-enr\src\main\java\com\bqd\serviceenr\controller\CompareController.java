package com.bqd.serviceenr.controller;

import com.bqd.model.esbnetworkreplay.EsbDeduplicateIgnoreDto;
import com.bqd.base.response.Response;
import com.bqd.serviceenr.service.CompareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-05
 */
@RestController
@RequestMapping("/compare")
public class CompareController {

    @Autowired
    private CompareService compareService;

    @GetMapping("/getIgnoredField")
    public Response getIgnoredField(@RequestParam("interfaceId") String interfaceId) {
        return Response.success(compareService.getIgnoredField(interfaceId));
    }

    @GetMapping("/getIgnoredFieldGlobal")
    public Response getIgnoredFieldGlobal() {
        return Response.success(compareService.getIgnoredFieldGlobal());
    }

    @PostMapping("/updateIgnoredField")
    public Response updateIgnoredField(@RequestBody EsbDeduplicateIgnoreDto esbDeduplicateIgnoreDto) {
        compareService.updateIgnoredField(esbDeduplicateIgnoreDto);
        return Response.success();
    }

    @GetMapping("/addIgnoredFieldGlobal")
    public Response addIgnoredFieldGlobal(@RequestParam("ignoredField") String ignoredField) {
        compareService.addIgnoredFieldGlobal(ignoredField);
        return Response.success();
    }

    @GetMapping("/deleteIgnoredFieldGlobal")
    public Response deleteIgnoredFieldGlobal(@RequestParam("ignoredField") String ignoredField) {
        compareService.deleteIgnoredFieldGlobal(ignoredField);
        return Response.success();
    }

}
