package com.bqd.serviceenr.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.bqd.base.rpc.esbnetworkreplay.*;
import com.bqd.base.tools.XmlTool;
import com.bqd.model.esbnetworkreplay.*;
import com.bqd.serviceenr.service.DeduplicateService;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-25
 */
@Service
@Slf4j
public class DeduplicateServiceImpl implements DeduplicateService {

    @Autowired
    private EsbRecordInfoMapper esbRecordInfoMapper;
    @Autowired
    private EsbDeduplicateIgnoreMapper esbDeduplicateIgnoreMapper;
    @Autowired
    private EsbDeduplicateIgnoreGlobalMapper esbDeduplicateIgnoreGlobalMapper;
    @Autowired
    private EsbAssetInfoMapper esbAssetInfoMapper;
    @Autowired
    private EsbAssetDetailMapper esbAssetDetailMapper;

    /**
     * 获取指定接口的忽略字段列表
     * 根据接口ID查询数据库中的忽略字段配置，返回经过清洗的字段列表（过滤空值并去除空白项）
     *
     * @param interfaceId 接口唯一标识符，用于查询对应的忽略字段配置
     * @return 过滤后的非空字段列表，当无配置时返回空集合
     */
    @Override
    public List<String> fetchIgnoredField(String interfaceId) {
        EsbDeduplicateIgnore esbDeduplicateIgnore = esbDeduplicateIgnoreMapper.selectByInterfaceId(interfaceId);
        if (ObjectUtil.isNull(esbDeduplicateIgnore)) {
            return new ArrayList<>();
        }
        // 将分号分隔的字符串转换为列表，并过滤空白项
        return StrUtil.split(esbDeduplicateIgnore.getIgnoredField(), ";").stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
    }

    /**
     * 更新忽略的字段列表
     * 此方法首先将传入的字段列表转换为字符串，然后根据接口ID检查数据库中是否存在相应的忽略设置
     * 如果存在，则更新忽略字段列表；如果不存在，则插入新的忽略设置
     *
     * @param esbDeduplicateIgnoreDto 包含接口ID和字段列表的DTO对象
     */
    @Override
    public void updateIgnoredField(EsbDeduplicateIgnoreDto esbDeduplicateIgnoreDto) {
        StringBuilder ignoredField = new StringBuilder();
        // 将字段列表转换为以分号分隔的字符串
        for (String field : esbDeduplicateIgnoreDto.getIgnoredFieldList()) {
            ignoredField.append(field).append(";");
        }
        // 根据接口ID查询数据库中的忽略设置
        EsbDeduplicateIgnore esbDeduplicateIgnore = esbDeduplicateIgnoreMapper.selectByInterfaceId(esbDeduplicateIgnoreDto.getInterfaceId());
        // 根据接口ID查询数据库中的忽略设置
        if (ObjectUtil.isNull(esbDeduplicateIgnore)) {
            esbDeduplicateIgnoreMapper.insert(new EsbDeduplicateIgnore(esbDeduplicateIgnoreDto.getInterfaceId(), ignoredField.toString()));
            return;
        }
        // 如果查询结果不为空，则更新现有的忽略设置
        esbDeduplicateIgnore.setIgnoredField(ignoredField.toString());
        esbDeduplicateIgnoreMapper.updateByInterfaceId(esbDeduplicateIgnore);
    }

    /**
     * 获取全局忽略的字段列表
     * 当没有配置任何全局忽略字段时，将返回一个空列表
     *
     * @return 全局忽略的字段列表如果数据库中没有相关配置，则返回空列表
     */
    @Override
    public List<String> fetchIgnoredFieldGlobal() {
        // 查询数据库中所有的全局重复忽略配置
        List<EsbDeduplicateIgnoreGlobal> esbDeduplicateIgnoreGlobalList = esbDeduplicateIgnoreGlobalMapper.selectByCondition(new EsbDeduplicateIgnoreGlobal());
        // 如果查询结果为空，则直接返回一个空列表
        if (CollectionUtil.isEmpty(esbDeduplicateIgnoreGlobalList)) {
            return new ArrayList<>();
        }
        // 将查询结果映射为只包含忽略字段的列表并返回
        return esbDeduplicateIgnoreGlobalList.stream().map(EsbDeduplicateIgnoreGlobal::getIgnoredField).collect(Collectors.toList());
    }

    /**
     * 添加全局忽略字段
     * 插入一个新的EsbDeduplicateIgnoreGlobal对象到数据库中，该对象包含一个唯一ID和一个指定的忽略字段
     *
     * @param ignoredField 要全局忽略的字段名称
     */
    @Override
    public void addIgnoredFieldGlobal(String ignoredField) {
        esbDeduplicateIgnoreGlobalMapper.insert(new EsbDeduplicateIgnoreGlobal(IdUtil.fastUUID(), ignoredField));
    }

    /**
     * 删除全局忽略字段
     * 传入一个包含待删除忽略字段的EsbDeduplicateIgnoreGlobal对象作为参数，根据条件删除记录的操作
     *
     * @param ignoredField 要删除的忽略字段名称
     */
    @Override
    public void deleteIgnoredFieldGlobal(String ignoredField) {
        esbDeduplicateIgnoreGlobalMapper.deleteByCondition(new EsbDeduplicateIgnoreGlobal(null, ignoredField));
    }

    /**
     * 针对指定的接口ID的报文数据去重
     *
     * @param interfaceId 接口ID
     */
    @Override
    public void deduplicate(String interfaceId) {
        //从数据库选择符合条件的记录
        EsbRecordInfoDto esbRecordInfoDto = new EsbRecordInfoDto();
        esbRecordInfoDto.setInterfaceId(interfaceId);
        List<EsbRecordInfoDetailDto> esbRecordInfoDetailDtoList = esbRecordInfoMapper.selectInfoDetailByCondition(esbRecordInfoDto);

        //将查询结果转换为AssetInfoDetailDTO列表
        List<EsbAssetInfoDetailDto> esbAssetInfoDetailDtoList = esbRecordInfoDetailDtoList.stream().map(esbRecordInfoDetailDto -> BeanUtil.copyProperties(esbRecordInfoDetailDto, EsbAssetInfoDetailDto.class)).collect(Collectors.toList());

        //创建去重DTO列表，准备进行去重处理
        List<EsbDeduplicateDto> esbDeduplicateDtoList = esbAssetInfoDetailDtoList.stream().map(esbAssetInfoDetailDto -> new EsbDeduplicateDto(esbAssetInfoDetailDto, null)).collect(Collectors.toList());

        //获取忽略字段
        List<String> ignoredFieldList = fetchIgnoredField(interfaceId);
        ignoredFieldList.addAll(fetchIgnoredFieldGlobal());

        //去重处理
        esbDeduplicateDtoList = deduplicateHandler(esbDeduplicateDtoList, ignoredFieldList);

        //去重完插入到数据库
        deduplicatePostHandler(esbDeduplicateDtoList);
    }

    /**
     * 指定接口ID的报文资产内部去重
     * 主要步骤包括：根据条件从数据库中选择记录、进行去重处理、删除原数据以及插入去重后的数据
     *
     * @param interfaceId 接口ID
     */
    @Override
    public void internalDeduplicate(String interfaceId) {
        //从数据库选择符合条件的记录
        EsbAssetInfoDto esbAssetInfoDto = new EsbAssetInfoDto();
        esbAssetInfoDto.setInterfaceId(interfaceId);
        List<EsbAssetInfoDetailDto> esbAssetInfoDetailDtoList = esbAssetInfoMapper.selectInfoDetailByCondition(esbAssetInfoDto);

        //将查询结果转换为EsbDeduplicateDto列表
        List<EsbDeduplicateDto> esbDeduplicateDtoList = esbAssetInfoDetailDtoList.stream().map(esbAssetInfoDetailDto -> new EsbDeduplicateDto(esbAssetInfoDetailDto, null)).collect(Collectors.toList());

        //获取忽略字段
        List<String> ignoredFieldList = fetchIgnoredField(interfaceId);
        ignoredFieldList.addAll(fetchIgnoredFieldGlobal());

        //去重处理
        esbDeduplicateDtoList = deduplicateHandler(esbDeduplicateDtoList, ignoredFieldList);

        //去重完删除原数据
        for (EsbAssetInfoDetailDto esbAssetInfoDetailDto: esbAssetInfoDetailDtoList) {
            esbAssetInfoMapper.deleteById(esbAssetInfoDetailDto.getAssetId());
            esbAssetDetailMapper.deleteById(esbAssetInfoDetailDto.getAssetId());
        }

        //插入数据库
        deduplicatePostHandler(esbDeduplicateDtoList);
    }

    /**
     * 去重处理器
     * 通过移除指定的忽略字段来实现数据的去重
     *
     * @param esbDeduplicateDtoList ESB脱重数据传输对象列表，包含了需要进行去重处理的数据
     * @param ignoredFieldList 忽略字段列表，指定了在去重处理过程中需要被移除的字段
     * @return 返回处理后的ESB去重数据
     */
    private List<EsbDeduplicateDto> deduplicateHandler(List<EsbDeduplicateDto> esbDeduplicateDtoList, List<String> ignoredFieldList) {
        //遍历要去重的数据，删除忽略字段，并保存到deduplicateBody属性中
        for (EsbDeduplicateDto esbDeduplicateDto : esbDeduplicateDtoList) {
            //获取原始请求体XML字符串
            String deduplicateBody = esbDeduplicateDto.getEsbAssetInfoDetailDto().getRequestBody();
            //将XML字符串读取到Document对象中
            Document document = XmlTool.readXmlFromStr(deduplicateBody);
            //移除Document中指定的忽略字段节点
            XmlTool.removeNodes(document, ignoredFieldList);
            //将XML中的标签转换为结构化标签
            XmlTool.convertToStructuredTag(document);
            //将XML文档展平，只保留根标签，所有子标签平铺
            document = XmlTool.flattenXML(document);
            //对XML文档中的节点进行排序，以确保一致性
            XmlTool.sortXML(document);
            //将处理后的Document对象转换回XML字符串，并设置到ESB去重对象中
            esbDeduplicateDto.setDeduplicateBody(document.asXML());
        }
        //去重后返回
        return esbDeduplicateDtoList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 去重处理后的数据持久化到数据库
     *
     * @param esbDeduplicateDtoList  ESB去重后的列表
     */
    private void deduplicatePostHandler(List<EsbDeduplicateDto> esbDeduplicateDtoList) {
        //插入新数据到数据库
        for (EsbDeduplicateDto esbDeduplicateDto : esbDeduplicateDtoList) {
            //生成唯一的资产ID
            String assetId = IdUtil.fastSimpleUUID();

            //将EsbAssetInfoDetailDto转换为EsbAssetInfo，并设置资产ID
            EsbAssetInfo esbAssetInfo = BeanUtil.copyProperties(esbDeduplicateDto.getEsbAssetInfoDetailDto(), EsbAssetInfo.class);
            esbAssetInfo.setAssetId(assetId);

            //插入资产信息到数据库
            esbAssetInfoMapper.insert(esbAssetInfo);

            //插入资产详细信息到数据库
            esbAssetDetailMapper.insert(new EsbAssetDetail(assetId, esbDeduplicateDto.getEsbAssetInfoDetailDto().getRequestBody(), esbDeduplicateDto.getEsbAssetInfoDetailDto().getResponseBody()));
        }
    }

}
