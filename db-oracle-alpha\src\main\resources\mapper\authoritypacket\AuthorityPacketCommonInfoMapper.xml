<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.authoritypacket.mapper.AuthorityPacketCommonInfoMapper">

    <resultMap id="packetCommonInfo" type="com.bqd.model.authoritypacket.PacketCommonInfo">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="INFO_NAME" property="infoName" javaType="java.lang.String"/>
        <result column="PACKET_TYPE" property="packetType" javaType="java.lang.String"/>
        <result column="TAG_NAME" property="tagName" javaType="java.lang.String"/>
        <result column="INFO_CONTENT" property="infoContent" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        insert into AUTHORITY_PACKET_COMMON_INFO
            (ID, INFO_NAME, PACKET_TYPE, TAG_NAME, INFO_CONTENT)
        values (#{id}, #{infoName}, #{packetType}, #{tagName}, #{infoContent})
    </insert>

    <select id="selectByCondition" resultMap="packetCommonInfo">
        SELECT * FROM AUTHORITY_PACKET_COMMON_INFO WHERE 1=1
        <if test="id != null and id != ''">
            AND ID = #{id}
        </if>
        <if test="infoName != null and infoName != ''">
            AND INFO_NAME = #{infoName}
        </if>
        <if test="packetType != null and packetType != ''">
            AND PACKET_TYPE = #{packetType}
        </if>
        <if test="tagName != null and tagName != ''">
            AND TAG_NAME = #{tagName}
        </if>
    </select>

    <update id="updateById">
        UPDATE AUTHORITY_PACKET_COMMON_INFO
        SET INFO_NAME    = #{infoName,jdbcType=VARCHAR},
            PACKET_TYPE  = #{packetType,jdbcType=VARCHAR},
            TAG_NAME     = #{tagName,jdbcType=VARCHAR},
            INFO_CONTENT = #{infoContent,jdbcType=VARCHAR}
        WHERE ID = #{id}
    </update>

    <delete id="deleteById">
        DELETE
        FROM AUTHORITY_PACKET_COMMON_INFO
        WHERE ID = #{id}
    </delete>

</mapper>
