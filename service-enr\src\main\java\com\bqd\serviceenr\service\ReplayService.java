package com.bqd.serviceenr.service;

import com.bqd.model.esbnetworkreplay.ReplayReqtDto;
import com.bqd.model.esbnetworkreplay.EsbReplayDetail;
import com.bqd.model.esbnetworkreplay.EsbReplayInfo;
import com.bqd.model.esbnetworkreplay.EsbReplayPlan;

import java.util.List;

public interface ReplayService {
    void replay(ReplayReqtDto replayReqtDto);

    void replayInOrder(ReplayReqtDto replayReqtDto);

    void replayByTime(String startTime, String endTime);

    List<EsbReplayPlan> getReplayPlan();

    List<EsbReplayInfo> getReplayInfo(String replayPlanId);

    void deleteReplayPlan(String replayPlanId);

    EsbReplayDetail getReplayDetailFieldValue(String infoId, String field);
}
