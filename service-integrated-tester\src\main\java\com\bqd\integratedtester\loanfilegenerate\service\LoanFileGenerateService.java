package com.bqd.integratedtester.loanfilegenerate.service;

import com.bqd.model.common.CommonReqtListDto;
import com.bqd.model.loanfilegenerate.BusinessApply;
import com.bqd.model.loanfilegenerate.CustomerInfo;

import java.io.InputStream;
import java.util.Map;

public interface LoanFileGenerateService {
    BusinessApply getBusinessApplyEntity(String loanId, String dbConnectionInfoId);

    CustomerInfo getCustomerInfoEntity(String customerId, String dbConnectionInfoId);

    void createDestFolder(String templateDir, String destFolderName);

    void writeCsv(CommonReqtListDto<Map<String, String>> commonReqtListDto);

    InputStream download(String destFolderName);
}
