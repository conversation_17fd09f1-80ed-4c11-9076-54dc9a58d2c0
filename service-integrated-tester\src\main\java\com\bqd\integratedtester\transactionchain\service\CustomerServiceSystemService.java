package com.bqd.integratedtester.transactionchain.service;

import com.bqd.model.transactionchain.SearchEsbResultDto;
import com.bqd.model.transactionchain.SearchMenuResultDto;
import com.bqd.model.transactionchain.TcCssMenu;

import java.util.List;

public interface CustomerServiceSystemService {

    /**
     * 解析客户服务系统
     */
    void parse();

    /**
     * 搜索menuPath/itemText
     *
     * @param tcCssMenu
     * @return
     */
    List<SearchMenuResultDto> searchMenu(TcCssMenu tcCssMenu);

    /**
     * 搜索esb接口号
     * @param interfaceId
     * @return
     */
    List<SearchEsbResultDto> searchEsb(String interfaceId);
}
