package com.bqd.servicellm.accesstestdoc.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.bqd.base.tools.FileTool;
import com.bqd.servicellm.accesstestdoc.service.AccessTestDocService;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-25
 */
@Service
public class AccessTestDocServiceImpl implements AccessTestDocService {

    @Value("${path.access-test-doc}")
    private String uploadPath;

    /**
     * 上传zip文件
     * @param file
     * @return
     */
    @Override
    public String uploadZip(MultipartFile file) {
        String fileId = IdUtil.fastSimpleUUID();
        try {
            File unzipFile = ZipUtil.unzip(file.getInputStream(), new File(uploadPath + File.separator + fileId), Charset.forName("GBK"));
            FileTool.flatten(unzipFile);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileId;
    }

    /**
     * 获取zip文件中的文件名
     * @param uploadId
     * @return
     */
    @Override
    public List<String> fileNameList(String uploadId) {
        File[] fileArr = FileUtil.ls(uploadPath + File.separator + uploadId);
        return new ArrayList<String>() {{
            for (File file : fileArr) {
                add(file.getName());
            }
        }};
    }

    /**
     * 根据uploadId下载fileName文件
     * @param uploadId
     * @param fileName
     * @return
     */
    @Override
    public InputStream downloadFile(String uploadId, String fileName) {
        return FileUtil.getInputStream(uploadPath + File.separator + uploadId + File.separator + fileName);
    }

    /**
     * 获取docx文件内容
     * @param uploadId
     * @param fileName
     * @return
     */
    @Override
    public String docxFileContent(String uploadId, String fileName) {
        try (BufferedInputStream inputStream = FileUtil.getInputStream(uploadPath + File.separator + uploadId + File.separator + fileName)) {
            StringBuilder stringBuilder = new StringBuilder();
            XWPFDocument document = new XWPFDocument(inputStream);
            for (XWPFParagraph paragraph: document.getParagraphs()) {
                stringBuilder.append(paragraph.getText());
            }
            for (XWPFTable table: document.getTables()) {
                for (XWPFTableRow row: table.getRows()) {
                    for (XWPFTableCell cell: row.getTableCells()) {
                        stringBuilder.append(cell.getText());
                    }
                }
            }
            return stringBuilder.toString();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取docx文件内容，并返回json结构化结果
     * @param uploadId
     * @param fileName
     * @return
     */
    @Override
    public String docxStructureContent(String uploadId, String fileName) {
        File file = new File(uploadPath + File.separator + uploadId + File.separator + fileName);
        Map<String, String> result = new LinkedHashMap<>();
        StringBuilder currentContent = new StringBuilder();
        String currentTitle = "";

        try (BufferedInputStream inputStream = FileUtil.getInputStream(file)) {
            XWPFDocument document = new XWPFDocument(inputStream);
            for (XWPFParagraph paragraph: document.getParagraphs()) {
                String styleID = paragraph.getStyleID();
                if (StrUtil.isNotBlank(styleID) && StrUtil.containsAnyIgnoreCase(document.getStyles().getStyle(styleID).getName(), "heading 1")) {
                    if (StrUtil.isNotBlank(currentTitle)) {
                        result.put(currentTitle, currentContent.toString().trim());
                    }
                    currentTitle = paragraph.getText();
                    currentContent = new StringBuilder();
                    continue;
                }
                currentContent.append(paragraph.getText()).append("\n");
            }
            // 最后一段内容要加进去
            if (currentTitle != null) {
                result.put(currentTitle, currentContent.toString().trim());
            }
            return JSONUtil.parseObj(result).toString();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String XlsFileContent(String uploadId, String fileName) {
        return "";
    }

}
