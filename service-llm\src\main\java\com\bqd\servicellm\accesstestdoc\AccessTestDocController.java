package com.bqd.servicellm.accesstestdoc;

import cn.hutool.core.io.IoUtil;
import com.bqd.base.response.Response;
import com.bqd.servicellm.accesstestdoc.service.AccessTestDocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;

/**
 * @Description: 准入测试文档
 * <AUTHOR>
 * @CreateTime 2025-02-25
 */
@RequestMapping("/accessTestDoc")
@RestController
public class AccessTestDocController {

    @Autowired
    private AccessTestDocService accessTestDocService;

    /**
     * 上传zip文件，解压保存并将所有子文件移动到根目录，删除内部文件夹（重名文件将会覆盖）
     * @param file
     * @return
     */
    @PostMapping("/uploadZip")
    public Response uploadZip(@RequestParam("file") MultipartFile file) {
        return Response.success(accessTestDocService.uploadZip(file));
    }

    /**
     * 获取zip文件中的文件名
     * @param uploadId
     * @return
     */
    @GetMapping("/fileNameList")
    public Response fileNameList(@RequestParam("uploadId") String uploadId) {
        return Response.success(accessTestDocService.fileNameList(uploadId));
    }

    /**
     * 根据uploadId下载fileName文件
     * @param uploadId
     * @param fileName
     * @param response
     * @return
     */
    @GetMapping("/downloadFile")
    public void downloadFile(@RequestParam("uploadId") String uploadId, @RequestParam("fileName") String fileName, HttpServletResponse response) {
        try {
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ";filename*=UTF-8''" + URLEncoder.encode(fileName, "UTF-8"));
            InputStream inputStream = accessTestDocService.downloadFile(uploadId, fileName);
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(IoUtil.readBytes(inputStream));
            outputStream.flush();
            inputStream.close();
            outputStream.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取docx文件内容
     * @param uploadId
     * @param fileName
     * @return
     */
    @GetMapping("/docxFileContent")
    public Response docxFileContent(@RequestParam("uploadId") String uploadId, @RequestParam("fileName") String fileName) {
        return Response.success(accessTestDocService.docxFileContent(uploadId, fileName));
    }

    /**
     * 获取docx文件内容，并返回json结构化结果
     * @param uploadId
     * @param fileName
     * @return
     */
    @GetMapping("/docxStructureContent")
    public Response docxStructureContent(@RequestParam("uploadId") String uploadId, @RequestParam("fileName") String fileName) {
        return Response.success(accessTestDocService.docxStructureContent(uploadId, fileName));
    }

    /**
     * 获取xls文件内容
     * @param uploadId
     * @param fileName
     * @return
     */
    @GetMapping("/XlsFileContent")
    public Response XlsFileContent(@RequestParam("uploadId") String uploadId, @RequestParam("fileName") String fileName) {
        return Response.success(accessTestDocService.XlsFileContent(uploadId, fileName));
    }

}
