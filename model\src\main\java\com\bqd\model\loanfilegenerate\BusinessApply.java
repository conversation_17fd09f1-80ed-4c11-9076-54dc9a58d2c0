package com.bqd.model.loanfilegenerate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessApply {
    private String serialno;
    private String occurdate;
    private String customerid;
    private String customername;
    private String businesstype;
    private String productid;
    private String specificsrialno;
    private String occurtype;
    private String revolveflag;
    private String contractsigntype;
    private String contractartificialno;
    private String businesscurrency;
    private String businesssum;
    private String maturitydate;
    private String drawdowntype;
    private String direction;
    private String purposeType;
    private String purposedescription;
    private String vouchtype;
    private String approveorgid;
    private String approveuserid;
    private String approvedate;
    private String approvestatus;
    private String operateorgid;
    private String operateuserid;
    private String operatedate;
    private String inputorgid;
    private String inputuserid;
    private String inputdate;
    private String updatedate;
    private String remark;
    private String tempsaveflag;
    private String systemchannelflag;
    private String accountingorgid;
    private String paymenttype;
    private String businesspriority;
    private String businesstermday;
    private String batchserialno;
    private String businesstermmonth;
    private String salesstore;
    private String salesperson;
    private String activeno;
    private String interestmode;
    private String paymentsource;
    private String prepaymentflag;
    private String usewaydescripe;
    private String usecommpanyname;
    private String usecommpanytype;
    private String usecommpanycerttype;
    private String industor;
    private String contractsigndate;
    private String gxbusinesssum;
    private String gxcontract;
    private String thirdapplyno;
    private String channelno;
    private String phoneno;
    private String usecommpanycertid;
    private String partnercode;
    private String policyno;
    private String inscomtype;
    private String inscompany;
    private String isqdbank;
    private String marriage;
    private String educational;
    private String companynature;
    private String industry;
    private String occupation;
    private String address1;
    private String mobiletelephone;
    private String rulestatus;
    private String putoutflag;
    private String registrationtime;
    private String firstorderapplicationtime;
    private String age;
    private String facerecognitionscore;
    private String creditcardno;
    private String partnersscore;
    private String antifrauddscore;
    private String partnersrating;
    private String partnersapproveflag;
    private String isquerypboc;
    private String creditamount;
    private String isnewcustomer;
    private String credittime;
    private String historyoverdays;
    private String historyoverbuisnesssum;
    private String loanapprovenum15d;
    private String cardaccnum18m;
    private String maxrepayments24m;
    private String maxoverloan3m;
    private String notusedration3m;
    private String loanenquirynum9m;
    private String overaccount9m;
    private String loanquerytime;
    private String firstcreditcardm;
    private String payoffloansum;
    private String earlyopaccunclearedloansum;
    private String norccusagerate;
    private String dcbaddebts;
    private String accuratedcbaddebt;
    private String dcfreeze;
    private String dc3mapp2times;
    private String dc6mapp3times;
    private String dc12mapp4times;
    private String loansecondaryno;
    private String loansuspiciousno;
    private String loanlossno;
    private String loan3mapp2times;
    private String loan6mapp3times;
    private String loan12mapp4times;
    private String normdcnum;
    private String isenforcem;
    private String dcoversum;
    private String loanoverdueamount;
    private String accountrepaym;
    private String crsquery3m;
    private String firstcardage;
    private String maxlinelimit;
    private String pboccrsscore;
    private String lastloantime;
    private String lastloanamount;
    private String lastloanterms;
    private String loanbalance;
    private String payprincipalamt;
    private String loanterms;
    private String overdueterms12m;
    private String overdueamt12m;
    private String loanamt12m;
    private String location;
    private String coreloantype;
    private String orientation;
    private String busname;
    private String mainindustry;
    private String storelevel;
    private String regdate;
    private String turnover;
    private String idcardvaliddate;
    private String idcardaddress;
    private String isinblacklisttd;
    private String isinblacklistzh;
    private String isinblacklistqh;
    private String isinblacklistbr;
    private String iscreditcardbaddebt;
    private String isaccountbaddebt;
    private String iscreditcardoverdue;
    private String bscore;
    private String isbususer;
    private String thirdapplydate;
    private String pcbgreenflag;
    private String relativeserialno;
    private String relativethirdapplyno;
    private String thirdrequestno;
    private String monthlyincome;
    private String quotastatus;
    private String lbsaddress;
    private String provinceflag;
    private String businessincome;
    private String comprehensiveRate;
    private String idcardvalidstartdate;
    private String loancontractno;
    private String quotano;
    private String provinceaddress;
    private String guarantor;
    private String guarantratio;
    private String sessionid;
    private String transactionid;
    private String transactiontype;
    private String bankcard;
    private String frontid;
    private String backid;
    private String facephotoid;
    private String protocalinfo;
    private String location_reason;
    private String creditincrcode;
    private String creditincrflag;
    private String policytransflag;
    private String balancecomcose;
    private String orglendparrate;
    private String isneedauxinfo;
    private String auxinfoqueryurl;
    private String auxeventtype;
    private String callbackurl;
    private String rate;
    private String creditrejectionreason;
    private String creditrequesttime;
    private String repaymethod;
    private String risktype;
    private String deductible;
    private String suggestamteffectivetime;
    private String suggestamtexpiretime;
    private String suggestratemax;
    private String suggestratemin;
    private String suggesttmpamt;
    private String tmpamteffectivetime;
    private String tmpamtexpiretime;
    private String dailyrate;
    private String versionflag;
    private String pcrAuth;
    private String actualfunder;
    private String guarantamt;
    private String enablecreditamt;
    private String enableamteffectivetime;
    private String applytype;
    private String credittag;
    private String isnewindustry;
    private String newindustrycast;
    private String adstime;
}
