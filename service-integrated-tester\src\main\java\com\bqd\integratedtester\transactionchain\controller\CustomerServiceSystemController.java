package com.bqd.integratedtester.transactionchain.controller;

import com.bqd.base.response.Response;
import com.bqd.integratedtester.transactionchain.service.CustomerServiceSystemService;
import com.bqd.model.transactionchain.TcCssMenu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 交易链路查询 - 客户服务系统
 * <AUTHOR>
 * @CreateTime 2025-02-13
 */
@RestController
@RequestMapping("/transactionChain/css")
public class CustomerServiceSystemController {

    @Autowired
    private CustomerServiceSystemService customerServiceSystemService;

    /**
     * 解析客户服务系统
     */
    @GetMapping("/parse")
    public Response parse() {
        customerServiceSystemService.parse();
        return Response.success();
    }

    /**
     * 搜索menuPath/itemText/tid
     * @param tcCssMenu
     * @return
     */
    @PostMapping("/searchMenu")
    public Response searchMenu(@RequestBody TcCssMenu tcCssMenu) {
        return Response.success(customerServiceSystemService.searchMenu(tcCssMenu));
    }

    /**
     * 根据ESB接口号搜索调用该接口的交易
     * @param interfaceId
     * @return
     */
    @GetMapping("/searchEsb")
    public Response searchEsb(@RequestParam String interfaceId) {
        return Response.success(customerServiceSystemService.searchEsb(interfaceId));
    }

}
