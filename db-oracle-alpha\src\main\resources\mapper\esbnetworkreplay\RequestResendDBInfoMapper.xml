<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.RequestResendDbInfoMapper">

    <resultMap id="requestResendDBInfo" type="com.bqd.model.esbnetworkreplay.RequestResendDBInfo">
        <result column="ID" property="id"/>
        <result column="DATABASE_NAME" property="databaseName"/>
        <result column="CONNECTION_URL" property="connectionUrl"/>
        <result column="USER_NAME" property="userName"/>
        <result column="PASSWORD" property="password"/>
        <result column="TABLE_NAME" property="tableName"/>
        <result column="INSERT_TIME" property="insertTime"/>
    </resultMap>

    <select id="selectAllOdrByTimeDesc" resultMap="requestResendDBInfo">
        select *
        from REQUEST_RESEND_DB_INFO
        ORDER BY INSERT_TIME DESC
    </select>

    <insert id="insert">
        INSERT INTO REQUEST_RESEND_DB_INFO (ID, DATABASE_NAME, CONNECTION_URL, USER_NAME, PASSWORD, TABLE_NAME,
                                            INSERT_TIME)
        VALUES (#{id}, #{databaseName}, #{connectionUrl}, #{userName}, #{password}, #{tableName}, #{insertTime})
    </insert>

    <update id="updateById">
        UPDATE REQUEST_RESEND_DB_INFO
        SET DATABASE_NAME  = #{databaseName},
            CONNECTION_URL = #{connectionUrl},
            USER_NAME      = #{userName},
            PASSWORD       = #{password},
            TABLE_NAME     = #{tableName}
        WHERE ID = #{id}
    </update>

    <delete id="deleteById">
        DELETE
        FROM REQUEST_RESEND_DB_INFO
        WHERE ID = #{id}
    </delete>

    <select id="selectById" resultMap="requestResendDBInfo">
        SELECT *
        FROM REQUEST_RESEND_DB_INFO
        WHERE ID = #{id}
    </select>

</mapper>