package com.bqd.servicellm.accesstestdoc.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

public interface AccessTestDocService {
    String uploadZip(MultipartFile file);

    List<String> fileNameList(String uploadId);

    InputStream downloadFile(String uploadId, String fileName);

    String docxFileContent(String uploadId, String fileName);

    String XlsFileContent(String uploadId, String fileName);

    String docxStructureContent(String uploadId, String fileName);
}
