<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.transactionchain.mapper.WisdomOperationTypeMapper">

    <resultMap id="wisdomOperationType" type="com.bqd.model.transactionchain.WisdomOperationType">
        <result property="id" column="ID"/>
        <result property="tradeNameId" column="TRADE_NAME_ID"/>
        <result property="operationType" column="OPERATION_TYPE"/>
        <result property="callFrom" column="CALL_FROM"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO WISDOM_OPERATION_TYPE (ID, TRADE_NAME_ID, OPERATION_TYPE, CALL_FROM)
        VALUES (#{id}, #{tradeNameId}, #{operationType}, #{callFrom})
    </insert>

    <delete id="truncate">
        TRUNCATE TABLE WISDOM_OPERATION_TYPE
    </delete>

    <select id="selectByTradeNameId" resultMap="wisdomOperationType">
        SELECT *
        FROM WISDOM_OPERATION_TYPE
        WHERE TRADE_NAME_ID = #{id}
    </select>

    <select id="selectByLikeOperationType" resultMap="wisdomOperationType">
        SELECT *
        FROM WISDOM_OPERATION_TYPE
        WHERE OPERATION_TYPE LIKE CONCAT('%', CONCAT(#{mvcId}, '%'))
    </select>

    <select id="selectByLikeDescription" resultMap="wisdomOperationType">
        SELECT *
        FROM WISDOM_OPERATION_TYPE
        WHERE DESCRIPTION LIKE CONCAT('%', CONCAT(#{description}, '%'))
    </select>
</mapper>