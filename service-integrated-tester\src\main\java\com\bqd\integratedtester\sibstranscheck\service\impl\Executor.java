package com.bqd.integratedtester.sibstranscheck.service.impl;

import java.util.Set;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-05-14
 */
public class Executor {
    public static String execute(String requestXml,
                                               String requestJson,
                                               String respXml,
                                               String respJson,
                                               Set<String> bodyCheckObjToArrayTags,
                                               Set<String> bodyCompareIgnore,
                                               boolean jsonRemoveEmptyTag,
                                               Set<String> reqtSysHeadIgnore,
                                               Set<String> respSysHeadIgnore) {

        //进行请求报文转换校验
        SibsRequestCheck requestCheck = new SibsRequestCheck(requestXml, requestJson);
        //请求appHead校验
        boolean reqtAppHeadCheck = requestCheck.appHeadCheck();
        if (!reqtAppHeadCheck) {
            return requestCheck.getErrMsg();
        }
        //请求localHead校验
        boolean reqtLocalHeadCheck = requestCheck.localHeadCheck();
        if (!reqtLocalHeadCheck) {
            return requestCheck.getErrMsg();
        }
        //请求sysHead校验
        boolean reqtSysHeadCheck = requestCheck.sysHeadCheck(reqtSysHeadIgnore);
        if (!reqtSysHeadCheck) {
            return requestCheck.getErrMsg();
        }
        //请求body校验
        boolean reqtBodyCheck = requestCheck.bodyCheck(jsonRemoveEmptyTag, bodyCheckObjToArrayTags, bodyCompareIgnore);
        if (!reqtBodyCheck) {
            return requestCheck.getErrMsg();
        }

        //响应报文转换校验
        SibsRespCheck respCheck = new SibsRespCheck(respXml, respJson);
        //响应appHead校验
        boolean respAppHeadCheck = respCheck.appHeadCheck();
        if (!respAppHeadCheck) {
            return respCheck.getErrMsg();
        }
        //响应sysHead校验
        boolean respSysHeadCheck = respCheck.sysHeadCheck(respSysHeadIgnore);
        if (!respSysHeadCheck) {
            return respCheck.getErrMsg();
        }
        //响应body校验
        boolean respBodyCheck = respCheck.bodyCheck(jsonRemoveEmptyTag, bodyCheckObjToArrayTags, bodyCompareIgnore);
        if (!respBodyCheck) {
            return respCheck.getErrMsg();
        }

        return "";
    }

}
