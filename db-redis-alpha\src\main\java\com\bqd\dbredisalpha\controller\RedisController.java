package com.bqd.dbredisalpha.controller;

import com.bqd.dbredisalpha.service.RedisService;
import com.bqd.model.redis.RedisDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-11-27
 */
@RestController
@RequestMapping("/")
public class RedisController {

    @Autowired
    private RedisService redisService;

    @PostMapping("/setString")
    public void setString(@RequestBody RedisDto redisDto) {
        redisService.setString(redisDto);
    }

    @GetMapping("/getString")
    public RedisDto getString(@RequestParam String key) {
        return redisService.getString(key);
    }

}
