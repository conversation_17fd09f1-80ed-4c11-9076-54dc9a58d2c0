package com.bqd.model.llm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName DEMAND_DOC
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DemandDoc implements Serializable {
    private String id;

    private String name;

    private Date createTime;

    private String userId;

    private Integer isProcessing;

    private Object statusMeta;

    private static final long serialVersionUID = 1L;
}