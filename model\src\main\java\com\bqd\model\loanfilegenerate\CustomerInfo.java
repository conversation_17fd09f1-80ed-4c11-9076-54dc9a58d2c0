package com.bqd.model.loanfilegenerate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerInfo {
    private String customerid;
    private String customername;
    private String customertype;
    private String certtype;
    private String certid;
    private String inputorgid;
    private String inputuserid;
    private String inputdate;
    private String remark;
    private String mfcustomerid;
    private String status;
    private String issuecountry;
    private String objectno;
    private String objecttype;
    private String englishname;
    private String datasource;
    private String cstrrisklevel;
    private String taxpayertype;
    private String counterpartytype;
    private String phoneno;
    private String channelno;
    private String coreid;
    private String sibscustomerid;
    private String thirdcustomerno;
    private String photoisconsistent;
}
