package com.bqd.dboraclealpha.servermanagement.controller;

import com.bqd.dboraclealpha.servermanagement.mapper.ServerInfoMapper;
import com.bqd.model.servermanagement.ServerInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2024-12-30
 */
@RestController
@RequestMapping("/serverInfo")
public class ServerInfoController implements com.bqd.base.rpc.serveroperation.ServerInfoMapper {

    @Autowired
    private ServerInfoMapper serverInfoMapper;

    @PostMapping("/insert")
    public void insert(@RequestBody ServerInfo serverInfo) {
        serverInfoMapper.insert(serverInfo);
    }

    @PostMapping("/count")
    public int count(@RequestBody ServerInfo serverInfo) {
        return serverInfoMapper.count(serverInfo);
    }

    @PostMapping("/selectPaged")
    public List<ServerInfo> selectPaged(@RequestParam int startRow, @RequestParam int endRow, @RequestBody ServerInfo serverInfo) {
        return serverInfoMapper.selectPaged(startRow, endRow, serverInfo);
    }

    @PostMapping("/selectByCondition")
    public List<ServerInfo> selectByCondition(@RequestBody ServerInfo serverInfo) {
        return serverInfoMapper.selectByCondition(serverInfo);
    }

    @Override
    @PostMapping("/updateById")
    public void updateById(@RequestBody ServerInfo serverInfo) {
        serverInfoMapper.updateById(serverInfo);
    }

    @GetMapping("/deleteById")
    public void deleteById(@RequestParam String id) {
        serverInfoMapper.deleteById(id);
    }

}
