package com.bqd.model.samplexml.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SampleXmlDto {
    private String name;
    private String interfaceId;
    private String versionNumber;
    private String type;
    private String sampleXml;
}
