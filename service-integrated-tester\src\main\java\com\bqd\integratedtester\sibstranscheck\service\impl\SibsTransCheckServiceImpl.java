package com.bqd.integratedtester.sibstranscheck.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.bqd.base.rpc.common.MysqlAlphaUtilMapper;
import com.bqd.base.rpc.sibstranscheck.*;
import com.bqd.integratedtester.sibstranscheck.service.SibsTransCheckService;
import com.bqd.model.common.PageDto;
import com.bqd.model.sibstranscheck.*;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-07-05
 */
@Service
public class SibsTransCheckServiceImpl implements SibsTransCheckService {

    @Value("${sibs-transfer-check.upload-path}")
    private String uploadPath;
    @Value("${sibs-transfer-check.unzip-path}")
    private String unzipPath;

    @Autowired
    private SibsFileUploadMapper sibsFileUploadMapper;
    @Autowired
    private SibsHttpDataInfoMapper sibsHttpDataInfoMapper;
    @Autowired
    private SibsHttpDataMapper sibsHttpDataMapper;
    @Autowired
    private MysqlAlphaUtilMapper mysqlAlphaUtilMapper;
    @Autowired
    private SibsTransCheckResultMapper sibsTransCheckResultMapper;
    @Autowired
    private SibsTransCheckFailedMapper sibsTransCheckFailedMapper;
    @Autowired
    private SibsTransCheckConfigMapper sibsTransCheckConfigMapper;
    @Autowired
    private SibsTransCheckStatisticMapper sibsTransCheckStatisticMapper;

    @Override
    public String uploadAndSave(MultipartFile multipartFile) {
        try {
            String id = IdUtil.fastSimpleUUID();
            String serverFileName = DateUtil.format(DateTime.now(), "yyyyMMddhhmmssSSS") + "_" + multipartFile.getOriginalFilename();
            String serverFilePath = uploadPath + File.separator + serverFileName;
            multipartFile.transferTo(new File(serverFilePath));
            sibsFileUploadMapper.insert(new SibsFileUpload(id, FileTypeUtil.getTypeByPath(serverFilePath), multipartFile.getOriginalFilename(), serverFileName, serverFilePath, DateUtil.now()));
            return id;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void removeUploadFile(String id) {
        SibsFileUpload sibsFileUpload = sibsFileUploadMapper.selectById(id);
        FileUtil.del(sibsFileUpload.getFilePath());
        sibsFileUploadMapper.deleteById(id);
    }

    @Override
    public List<SibsFileUpload> getUploadFileList() {
        return sibsFileUploadMapper.selectAllByUploadTime();
    }

    @Override
    public void parseToDB(String id) {
        SibsFileUpload sibsFileUpload = sibsFileUploadMapper.selectById(id);
        // 若为zip文件，则调用zip解析方法
        if ("zip".equals(sibsFileUpload.getFileType())) {
            parseZipFile(sibsFileUpload);
            return;
        }
        // 若为json文件，则调用json解析方法
        if ("json".equals(sibsFileUpload.getFileType())) {
            parseJsonFile(sibsFileUpload);
        }
    }

    @Override
    public void clearHttpDataDB() {
        mysqlAlphaUtilMapper.truncateTable("SIBS_HTTP_DATA_INFO");
        mysqlAlphaUtilMapper.truncateTable("SIBS_HTTP_DATA");
    }

    @Override
    public void clearCheckResultDB() {
        List<String> interfaceIdList = sibsTransCheckResultMapper.selectDistinctInterfaceId();
        interfaceIdList.forEach(interfaceId -> {
            int i = sibsTransCheckStatisticMapper.countByInterfaceId(interfaceId);
            if (i == 0) {
                sibsTransCheckStatisticMapper.insert(new SibsTransCheckStatistic(IdUtil.fastSimpleUUID(), interfaceId));
            }
        });
        mysqlAlphaUtilMapper.truncateTable("SIBS_TRANS_CHECK_RESULT");
        mysqlAlphaUtilMapper.truncateTable("SIBS_TRANS_CHECK_FAILED");
    }

    /**
     * 转换校验
     */
    @Override
    public void sibsTransCheck() {
        //从数据库选择请求报文信息（不含报文体）
        List<SibsHttpDataInfo> sibsHttpDataInfoList = sibsHttpDataInfoMapper.selectReqtXml();
        //设置json对象转array标签
        Set<String> bodyCheckObjToArrayTags = new HashSet<>();
        List<SibsTransCheckConfig> jsonToArrayTagList = sibsTransCheckConfigMapper.selectByType("1");
        jsonToArrayTagList.forEach(sibsTransCheckConfig -> bodyCheckObjToArrayTags.add(sibsTransCheckConfig.getContent()));
        //设置是否移除json对象中的空标签
        boolean jsonRemoveEmptyTag = true;
        //获取json body比对忽略标签
        Set<String> bodyCompareIgnore = new HashSet<>();
        List<SibsTransCheckConfig> bodyCompareIgnoreList = sibsTransCheckConfigMapper.selectByType("2");
        bodyCompareIgnoreList.forEach(sibsTransCheckConfig -> bodyCompareIgnore.add(sibsTransCheckConfig.getContent()));
        //获取响应sysHead比对忽略
        Set<String> respSysHeadCompareIgnore = new HashSet<>();
        List<SibsTransCheckConfig> respSysHeadCompareIgnoreList = sibsTransCheckConfigMapper.selectByType("3");
        respSysHeadCompareIgnoreList.forEach(sibsTransCheckConfig -> respSysHeadCompareIgnore.add(sibsTransCheckConfig.getContent()));
        //获取请求sysHead比对忽略
        Set<String> reqtSysHeadCompareIgnore = new HashSet<>();
        List<SibsTransCheckConfig> reqtSysHeadCompareIgnoreList = sibsTransCheckConfigMapper.selectByType("4");
        reqtSysHeadCompareIgnoreList.forEach(sibsTransCheckConfig -> reqtSysHeadCompareIgnore.add(sibsTransCheckConfig.getContent()));

        //遍历请求报文信息
        for (SibsHttpDataInfo sibsHttpDataInfo : sibsHttpDataInfoList) {
            //根据请求报文的seqNo和transTimestamp获取对应的报文
            List<SibsHttpDataInfo> transCheckList = sibsHttpDataInfoMapper.selectBySeqNoAndTransTimestamp(sibsHttpDataInfo.getSeqNo(), sibsHttpDataInfo.getTransTimestamp());
            String id = IdUtil.fastSimpleUUID();
            //若报文数量不为4，校验失败
            if (transCheckList.size() != 4) {
                sibsTransCheckResultMapper.insert(new SibsTransCheckResult(id, sibsHttpDataInfo.getSeqNo(), sibsHttpDataInfo.getTransTimestamp(), sibsHttpDataInfo.getInterfaceId(), "0"));
                sibsTransCheckFailedMapper.insert(new SibsTransCheckFailed(id, "校验失败，seqNo和transTimestamp报文数量不正确"));
                continue;
            }
            //检查结果数据库中是否有该seqNo和transTimestamp的记录
            int count = sibsTransCheckResultMapper.countBySeqNoAndTransTimestamp(sibsHttpDataInfo.getSeqNo(), sibsHttpDataInfo.getTransTimestamp());
            //若有则视为重复，报错
            if (count != 0) {
                sibsTransCheckResultMapper.insert(new SibsTransCheckResult(id, sibsHttpDataInfo.getSeqNo(), sibsHttpDataInfo.getTransTimestamp(), sibsHttpDataInfo.getInterfaceId(), "0"));
                sibsTransCheckFailedMapper.insert(new SibsTransCheckFailed(id, "校验失败，该seqNo和transTimestamp重复"));
                continue;
            }
            String reqtXml = "", reqtJson = "", respJson = "", respXml = "";
            //遍历这4个报文信息，获取具体报文内容，即xml请求响应报文、json请求响应报文
            for (SibsHttpDataInfo info : transCheckList) {
                if ("reqt".equals(info.getHttpType())) {
                    if ("xml".equals(info.getContentType())) {
                        reqtXml = sibsHttpDataMapper.selectByInfoId(info.getId()).getData();
                    }
                    if ("json".equals(info.getContentType())) {
                        reqtJson = sibsHttpDataMapper.selectByInfoId(info.getId()).getData();
                    }
                }
                if ("resp".equals(info.getHttpType())) {
                    if ("xml".equals(info.getContentType())) {
                        respXml = sibsHttpDataMapper.selectByInfoId(info.getId()).getData();
                    }
                    if ("json".equals(info.getContentType())) {
                        respJson = sibsHttpDataMapper.selectByInfoId(info.getId()).getData();
                    }
                }
            }
            //判断是否有为空的情况
            if (StrUtil.hasBlank(reqtXml, reqtJson, respJson, respXml)) {
                sibsTransCheckResultMapper.insert(new SibsTransCheckResult(id, sibsHttpDataInfo.getSeqNo(), sibsHttpDataInfo.getTransTimestamp(), sibsHttpDataInfo.getInterfaceId(), "0"));
                sibsTransCheckFailedMapper.insert(new SibsTransCheckFailed(id, "校验失败，请求或响应报文缺失"));
                continue;
            }
            //执行校验
            String executeResult = Executor.execute(reqtXml, reqtJson, respXml, respJson, bodyCheckObjToArrayTags, bodyCompareIgnore, jsonRemoveEmptyTag, reqtSysHeadCompareIgnore, respSysHeadCompareIgnore);
            //插入校验结果
            if (StrUtil.isBlank(executeResult)) {
                sibsTransCheckResultMapper.insert(new SibsTransCheckResult(id, sibsHttpDataInfo.getSeqNo(), sibsHttpDataInfo.getTransTimestamp(), sibsHttpDataInfo.getInterfaceId(), "1"));
            } else {
                sibsTransCheckResultMapper.insert(new SibsTransCheckResult(id, sibsHttpDataInfo.getSeqNo(), sibsHttpDataInfo.getTransTimestamp(), sibsHttpDataInfo.getInterfaceId(), "0"));
                sibsTransCheckFailedMapper.insert(new SibsTransCheckFailed(id, executeResult));
            }
        }
    }

    @Override
    public PageDto<SibsHttpDataInfo> getPagedHttpDataInfo(int pageNo, int pageSize, String seqNo, String transTimestamp) {
        int totalCount = sibsHttpDataInfoMapper.countBySeqNoAndTransTimestampIfExists(seqNo, transTimestamp);
        int pageCount = PageUtil.totalPage(totalCount, pageSize);
        int startRow = PageUtil.getStart(pageNo, pageSize);
        List<SibsHttpDataInfo> sibsHttpDataList = sibsHttpDataInfoMapper.selectPaged(startRow, pageSize, seqNo, transTimestamp);
        return new PageDto<>(pageNo, pageSize, pageCount, totalCount, sibsHttpDataList);
    }

    @Override
    public SibsHttpData getHttpDataByInfoId(String infoId) {
        return sibsHttpDataMapper.selectByInfoId(infoId);
    }

    @Override
    public PageDto<SibsTransCheckResult> getPagedTransCheckResult(int pageNo, int pageSize) {
        int totalCount = sibsTransCheckResultMapper.count();
        int startRow = PageUtil.getStart(pageNo, pageSize);
        int pageCount = PageUtil.totalPage(totalCount, pageSize);
        List<SibsTransCheckResult> sibsTransCheckResultList = sibsTransCheckResultMapper.selectPagedOBResult(startRow, pageSize);
        return new PageDto<>(pageNo, pageSize, pageCount, totalCount, sibsTransCheckResultList);
    }

    @Override
    public SibsTransCheckFailed getFailedInfoByResultId(String resultId) {
        return sibsTransCheckFailedMapper.selectByResultId(resultId);
    }

    @Override
    public List<SibsTransCheckConfig> getCheckConfigListByType(String type) {
        return sibsTransCheckConfigMapper.selectByType(type);
    }

    @Override
    public void addCheckConfig(SibsTransCheckConfig sibsTransCheckConfig) {
        sibsTransCheckConfig.setId(IdUtil.fastSimpleUUID());
        sibsTransCheckConfigMapper.insert(sibsTransCheckConfig);
    }

    @Override
    public void deleteCheckConfigById(String id) {
        sibsTransCheckConfigMapper.deleteById(id);
    }

    /**
     * zip文件解析方法
     *
     * @param sibsFileUpload
     */
    private void parseZipFile(SibsFileUpload sibsFileUpload) {
        FileUtil.del(unzipPath);
        // 解压时自动创建文件夹
        ZipUtil.unzip(sibsFileUpload.getFilePath(), unzipPath);
        CountDownLatch countDownLatch = new CountDownLatch(FileUtil.ls(unzipPath).length);
        for (File file : FileUtil.ls(unzipPath)) {
            //如果是文件夹，则跳过
            if (file.isDirectory()) {
                countDownLatch.countDown();
                continue;
            }
            ThreadUtil.execute(() -> {
                String fileContent = FileUtil.readString(file, StandardCharsets.UTF_8);
                boolean xmlCheckResult = checkXmlFormat(fileContent);
                boolean jsonCheckResult = checkJsonFormat(fileContent);
                // 若为xml格式，则调用xml解析方法
                if (xmlCheckResult) {
                    parseXmlToDB(fileContent);
                }
                // 若为json格式，则调用json解析方法
                if (jsonCheckResult) {
                    parseJsonToDB(fileContent);
                }
                countDownLatch.countDown();
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private void parseJsonFile(SibsFileUpload sibsFileUpload) {
        String fileContent = FileUtil.readString(sibsFileUpload.getFilePath(), StandardCharsets.UTF_8);
        JSONArray jsonArray = new JSONArray(fileContent);
        CountDownLatch countDownLatch = new CountDownLatch(jsonArray.size());
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            JSONObject http = jsonObject.getJSONObject("_source").getJSONObject("layers").getJSONObject("http");
            String tagName = http.keySet().stream().filter(key -> key.contains("Content-encoded entity body")).findFirst().orElse(null);
            if (StrUtil.isBlank(tagName)) {
                countDownLatch.countDown();
                continue;
            }
            ThreadUtil.execute(() -> {
                String xmlHexData = http.getJSONObject(tagName).getJSONObject("data").getStr("data.data");
                String xmlData = HexUtil.decodeHexStr(xmlHexData.replaceAll(":", ""));
                parseXmlToDB(xmlData);
                countDownLatch.countDown();
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private void parseXmlToDB(String xmlContent) {
        try {
            Document document = new SAXReader().read(new StringReader(xmlContent));
            Element rootElement = document.getRootElement();
            Element svcHdr = rootElement.element("svcHdr");
            if (ObjectUtil.isNull(svcHdr)) {
                return;
            }
            Element svcId = svcHdr.element("svcId");
            if (ObjectUtil.isNull(svcId)) {
                return;
            }
            String serviceId = svcId.getText();
            if (!checkIsSibsIntf(serviceId)) {
                return;
            }
            Element appHdr = rootElement.element("appHdr");
            if (appHdr == null) {
                return;
            }
            Element seqNo = appHdr.element("seqNo");
            if (seqNo == null || StrUtil.isBlank(seqNo.getText())) {
                return;
            }
            Element tranTimestamp = appHdr.element("tranTimestamp");
            if (tranTimestamp == null || StrUtil.isBlank(tranTimestamp.getText())) {
                return;
            }
            String id = IdUtil.fastSimpleUUID();
            SibsHttpDataInfo sibsHttpDataInfo = new SibsHttpDataInfo(id, "xml", rootElement.getName(), seqNo.getText(), tranTimestamp.getText(), svcId.getText());
            SibsHttpData sibsHttpData = new SibsHttpData(id, xmlContent);
            sibsHttpDataInfoMapper.insert(sibsHttpDataInfo);
            sibsHttpDataMapper.insert(sibsHttpData);
        } catch (DocumentException e) {
            throw new RuntimeException(e);
        }
    }

    private void parseJsonToDB(String jsonContent) {
        JSONObject jsonObject = new JSONObject(jsonContent);
        JSONObject sysHead = jsonObject.getJSONObject("sysHead");
        if (ObjectUtil.isNull(sysHead)) {
            return;
        }
        JSONObject localHead = jsonObject.getJSONObject("localHead");
        String httpType;
        if (ObjectUtil.isNull(localHead)) {
            httpType = "resp";
        } else {
            httpType = "reqt";
        }
        String seqNo = sysHead.getStr("seqNo");
        String tranTimestamp = sysHead.getStr("tranTimestamp");
        String id = IdUtil.fastSimpleUUID();
        SibsHttpDataInfo sibsHttpDataInfo = new SibsHttpDataInfo(id, "json", httpType, seqNo, tranTimestamp, null);
        SibsHttpData sibsHttpData = new SibsHttpData(id, jsonContent);
        sibsHttpDataInfoMapper.insert(sibsHttpDataInfo);
        sibsHttpDataMapper.insert(sibsHttpData);
    }

    private boolean checkIsSibsIntf(String interfaceId) {
        if (StrUtil.isBlank(interfaceId)) {
            return false;
        }
        String regex = "^6\\d{5}$";
        return interfaceId.matches(regex);
    }

    private boolean checkXmlFormat(String content) {
        try {
            new SAXReader().read(new StringReader(content));
            return true;
        } catch (DocumentException e) {
            return false;
        }
    }

    private boolean checkJsonFormat(String content) {
        try {
            new JSONObject(content);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

}
