package com.bqd.integratedtester.projectmanagement.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.base.rpc.projectmanagement.GitCredentialMapper;
import com.bqd.base.rpc.projectmanagement.GitRepoInfoMapper;
import com.bqd.integratedtester.projectmanagement.service.GitService;
import com.bqd.model.projectmanagement.GitCredential;
import com.bqd.model.projectmanagement.GitRepoInfo;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.storage.file.FileRepositoryBuilder;
import org.eclipse.jgit.transport.CredentialsProvider;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-09
 */
@Service
@Slf4j
public class GitServiceImpl implements GitService {

    @Value("${git-repo.local-dir}")
    private String gitRepoLocalDir;

    @Autowired
    private GitRepoInfoMapper gitRepoInfoMapper;
    @Autowired
    private GitCredentialMapper gitCredentialMapper;

    @Override
    public List<GitRepoInfo> repositories(GitRepoInfo gitRepoInfo) {
        return gitRepoInfoMapper.selectByCondition(ObjectUtil.isNull(gitRepoInfo) ? new GitRepoInfo() : gitRepoInfo);
    }

    @Override
    public void addRepository(GitRepoInfo gitRepoInfo) {
        GitRepoInfo searchCondition = new GitRepoInfo();
        searchCondition.setRepositoryName(gitRepoInfo.getRepositoryName());
        if (!gitRepoInfoMapper.selectByCondition(searchCondition).isEmpty()) {
            throw new CustomizedException("该名称git仓库已存在");
        }
        gitRepoInfo.setId(IdUtil.fastSimpleUUID());
        gitRepoInfo.setLocalPath(gitRepoLocalDir + File.separator + gitRepoInfo.getRepositoryName());
        gitRepoInfoMapper.insert(gitRepoInfo);
    }

    @Override
    public void updateRepo(GitRepoInfo gitRepoInfo) {
        gitRepoInfoMapper.updateById(gitRepoInfo);
    }

    @Override
    public void deleteRepo(String id) {
        GitRepoInfo gitRepoInfo = new GitRepoInfo();
        gitRepoInfo.setId(id);
        List<GitRepoInfo> gitRepoInfoList = gitRepoInfoMapper.selectByCondition(gitRepoInfo);
        if (gitRepoInfoList.isEmpty()) {
            return;
        }
        gitRepoInfo = gitRepoInfoList.get(0);
        gitRepoInfoMapper.deleteById(id);
        FileUtil.del(gitRepoInfo.getLocalPath());
    }

    @Override
    public void pullOrClone(String id) {
        GitRepoInfo gitRepoInfo = new GitRepoInfo();
        gitRepoInfo.setId(id);
        gitRepoInfo = gitRepoInfoMapper.selectByCondition(gitRepoInfo).get(0);
        updateStatus(gitRepoInfo, "updating");

        GitCredential gitCredential = new GitCredential();
        gitCredential.setId(gitRepoInfo.getCredentialId());
        gitCredential = gitCredentialMapper.selectByCondition(gitCredential).get(0);
        try {
            if (!FileUtil.exist(gitRepoInfo.getLocalPath())) {
                executeClone(gitRepoInfo, gitCredential);
            } else {
                executePull(gitRepoInfo, gitCredential);
            }
            updateStatus(gitRepoInfo, "success");
        } catch (Exception e) {
            updateStatus(gitRepoInfo, "failed");
            throw new CustomizedException("Git同步失败");
        }
    }

    /**
     * 从远程git仓库clone项目
     * @param gitRepoInfo git仓库信息
     */
    private void executeClone(GitRepoInfo gitRepoInfo, GitCredential gitCredential){
        Git git = null;
        //设置git账号密码
        CredentialsProvider provider = new UsernamePasswordCredentialsProvider(gitCredential.getUsername(), gitCredential.getPassword());
        try {
            //执行git clone
            git = Git.cloneRepository()
                    .setURI(gitRepoInfo.getRemoteUrl())
                    .setBranch(gitRepoInfo.getBranchName())
                    .setDirectory(new File(gitRepoInfo.getLocalPath()))
                    .setCredentialsProvider(provider)
                    .setBranch(gitRepoInfo.getBranchName()).call();
            log.info("clone - 从远程仓库【{}】克隆【{}】项目【{}】分支成功，本地路径为【{}】", gitRepoInfo.getRemoteUrl(), gitRepoInfo.getRepositoryName(), gitRepoInfo.getBranchName(), gitRepoInfo.getLocalPath() + File.separator + gitRepoInfo.getRepositoryName());
        } catch (Exception e) {
            log.info("clone - 从远程仓库【{}】克隆【{}】项目【{}】分支到本地路径【{}】失败，错误原因【{}】", gitRepoInfo.getRemoteUrl(), gitRepoInfo.getRepositoryName(), gitRepoInfo.getBranchName(), gitRepoInfo.getLocalPath() + File.separator + gitRepoInfo.getRepositoryName(), e.getMessage());
            throw new RuntimeException(e);
        } finally {
            if(ObjectUtil.isNotNull(git)){
                if (ObjectUtil.isNotNull(git.getRepository())){
                    git.getRepository().close();
                }
                git.close();
            }
        }
    }

    /**
     * 从git拉取代码的核心方法
     * @param gitRepoInfo .git路径
     * @param gitCredential 分支名称
     * @return 拉取代码的结果，成功返回1，失败返回0
     */
    private void executePull(GitRepoInfo gitRepoInfo, GitCredential gitCredential) {
        Git git = null;
        Repository repository;
        File gitFile = new File(gitRepoInfo.getLocalPath() + File.separator + ".git");
        try {
            //设置仓库地址
            repository = new FileRepositoryBuilder().setGitDir(gitFile).build();
            //设置git账号密码
            CredentialsProvider provider = new UsernamePasswordCredentialsProvider(gitCredential.getUsername(), gitCredential.getPassword());
            git = new Git(repository);
            //拉取代码
            git.pull().setCredentialsProvider(provider).setRemoteBranchName(gitRepoInfo.getBranchName()).call();
            log.info("pull - 【{}】成功从git远程仓库拉取【{}】分支的代码", gitRepoInfo.getRepositoryName(), gitRepoInfo.getBranchName());
        } catch (Exception e) {
            log.error("pull - 【{}】从git远程仓库拉取【{}】分支的代码失败，错误原因【{}】", gitRepoInfo.getRepositoryName(), gitRepoInfo.getBranchName(), e.getMessage());
            throw new RuntimeException(e);
        } finally {
            if(ObjectUtil.isNotNull(git)){
                if(ObjectUtil.isNotNull(git.getRepository())){
                    git.getRepository().close();
                }
                git.close();
            }
        }
    }

    private void updateStatus(GitRepoInfo gitRepoInfo, String status){
        gitRepoInfo.setUpdateTime(DateTime.now().toString());
        gitRepoInfo.setUpdateStatus(status);
        gitRepoInfoMapper.updateById(gitRepoInfo);
    }

    @Override
    public List<GitCredential> credentials(GitCredential gitCredential) {
        return gitCredentialMapper.selectByCondition(gitCredential);
    }

    @Override
    public void addCredential(GitCredential gitCredential) {
        gitCredential.setId(IdUtil.fastUUID());
        gitCredentialMapper.insert(gitCredential);
    }

    @Override
    public void deleteCredential(String id) {
        gitCredentialMapper.deleteById(id);
    }

    @Override
    public void updateCredential(GitCredential gitCredential) {
        gitCredentialMapper.updateById(gitCredential);
    }

}
