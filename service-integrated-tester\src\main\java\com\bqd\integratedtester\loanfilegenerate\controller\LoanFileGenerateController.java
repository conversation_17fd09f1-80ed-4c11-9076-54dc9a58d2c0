package com.bqd.integratedtester.loanfilegenerate.controller;

import cn.hutool.core.io.IoUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.base.response.Response;
import com.bqd.integratedtester.loanfilegenerate.service.LoanFileGenerateService;
import com.bqd.model.common.CommonReqtListDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Map;

/**
 * @Description: 网贷放还款文件生成
 * <AUTHOR>
 * @CreateTime 2025-04-14
 */
@RestController
@RequestMapping("/loanFileGenerate")
public class LoanFileGenerateController {

    @Autowired
    private LoanFileGenerateService loanFileGenerateService;

    /**
     * 根据贷款ID和数据库连接信息ID获取业务申请实体。
     *
     * @param loanId             贷款的唯一标识符，用于查询对应的业务申请实体。
     * @param dbConnectionInfoId 数据库连接信息的唯一标识符，用于指定查询的数据库连接。
     * @return 包含业务申请实体的Response对象，表示操作成功。
     */
    @GetMapping("/businessApply/{loanId}")
    public Response getBusinessApplyEntity(@PathVariable String loanId, @RequestParam String dbConnectionInfoId) {
        return Response.success(loanFileGenerateService.getBusinessApplyEntity(loanId, dbConnectionInfoId));
    }

    /**
     * 根据客户ID和数据库连接信息ID获取客户信息实体。
     *
     * @param customerId         客户ID，用于唯一标识客户。
     * @param dbConnectionInfoId 数据库连接信息ID，用于指定获取客户信息的数据库连接。
     * @return Response 包含客户信息实体的响应对象，成功时返回的Response对象中封装了客户信息实体。
     */
    @GetMapping("/customerInfo/{customerId}")
    public Response getCustomerInfoEntity(@PathVariable String customerId, @RequestParam String dbConnectionInfoId) {
        return Response.success(loanFileGenerateService.getCustomerInfoEntity(customerId, dbConnectionInfoId));
    }

    /**
     * 创建目标文件夹的接口方法。
     * 根据提供的模板目录和目标文件夹名称创建目标文件夹。
     *
     * @param templateDir    模板目录路径，用于指定模板文件所在的目录。
     * @param destFolderName 目标文件夹名称，用于指定需要创建的目标文件夹的名称。
     * @return 返回一个 Response 对象，表示操作结果。如果操作成功，则返回成功的响应。
     */
    @GetMapping("/createDestFolder")
    public Response createDestFolder(String templateDir, String destFolderName) {
        loanFileGenerateService.createDestFolder(templateDir, destFolderName);
        return Response.success();
    }

    /**
     * 写入CSV文件的接口方法。
     * 接收一个包含字符串键值对映射列表的请求对象，将数据写入CSV文件。
     *
     * @param commonReqtListDto 包含要写入CSV的数据列表的请求对象，每个元素是一个字符串键值对映射
     * @return Response 返回操作结果的响应对象，如果写入成功则返回成功响应
     */
    @PostMapping("/writeCsv")
    public Response writeCsv(@RequestBody CommonReqtListDto<Map<String, String>> commonReqtListDto) {
        loanFileGenerateService.writeCsv(commonReqtListDto);
        return Response.success();
    }

    /**
     * 下载文件的接口方法。
     * 接收目标文件夹名称作为参数，将文件下载到客户端。
     *
     * @param destFolderName 目标文件夹名称，用于指定需要下载的文件所在的文件夹
     * @param response       HttpServletResponse对象，用于设置响应头和输出流
     * @return 返回一个 Response 对象，表示操作结果。如果操作成功，则返回成功的响应。
     */
    @GetMapping("/download")
    public void download(String destFolderName, HttpServletResponse response) {
        response.addHeader("Content-Disposition", "attachment;filename=" + destFolderName.split("_")[0] + ".zip");
        response.setContentType("application/octet-stream");
        ServletOutputStream outputStream;
        InputStream inputStream = loanFileGenerateService.download(destFolderName);
        try {
            outputStream = response.getOutputStream();
            outputStream.write(IoUtil.readBytes(inputStream));
            outputStream.flush();
            outputStream.close();
            inputStream.close();
        } catch (Exception e) {
            throw new CustomizedException(e);
        }
    }

}
