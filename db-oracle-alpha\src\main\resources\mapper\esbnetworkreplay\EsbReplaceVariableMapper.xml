<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbReplaceVariableMapper">

    <resultMap id="esbReplaceVariable" type="com.bqd.model.esbnetworkreplay.EsbReplaceVariable">
        <id column="ID" property="id" javaType="java.lang.String"/>
        <result column="NAME" property="name" javaType="java.lang.String"/>
        <result column="VALUE" property="value" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectAll" resultMap="esbReplaceVariable">
        SELECT *
        FROM ESB_REPLACE_VARIABLE
    </select>

    <delete id="deleteById">
        DELETE
        FROM ESB_REPLACE_VARIABLE
        WHERE ID = #{id}
    </delete>

    <insert id="insert">
        INSERT INTO ESB_REPLACE_VARIABLE (ID, NAME, VALUE)
        VALUES (#{id}, #{name}, #{value})
    </insert>

</mapper>