<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.transactionchain.mapper.TcCssMenuMapper">

    <resultMap id="tcCssMenu" type="com.bqd.model.transactionchain.TcCssMenu">
        <id column="ID" property="id" javaType="java.lang.String" />
        <result column="T_ID" property="tId" javaType="java.lang.String" />
        <result column="MENU_PATH" property="menuPath" javaType="java.lang.String" />
        <result column="ITEM_TEXT" property="itemText" javaType="java.lang.String" />
        <result column="CODE_URL" property="codeUrl" javaType="java.lang.String" />
    </resultMap>

    <insert id="insert">
        insert into TC_CSS_MENU
            (ID, T_ID, MENU_PATH, ITEM_TEXT, CODE_URL)
        values (#{id}, #{tId}, #{menuPath}, #{itemText}, #{codeUrl})
    </insert>

    <select id="selectByCondition" resultMap="tcCssMenu">
        SELECT * FROM TC_CSS_MENU WHERE 1=1
        <if test="id != null">
            AND ID = #{id}
        </if>
        <if test="tId != null">
            AND T_ID = #{tId}
        </if>
        <if test="menuPath != null">
            AND MENU_PATH LIKE CONCAT('%', CONCAT(#{menuPath}, '%'))
        </if>
        <if test="itemText != null">
            AND ITEM_TEXT LIKE CONCAT('%', CONCAT(#{itemText}, '%'))
        </if>
        <if test="codeUrl != null">
           AND CODE_URL = #{codeUrl}
        </if>
    </select>

</mapper>