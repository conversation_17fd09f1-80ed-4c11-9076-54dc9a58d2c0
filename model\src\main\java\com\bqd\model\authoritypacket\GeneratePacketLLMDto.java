package com.bqd.model.authoritypacket;

import com.bqd.model.common.EntryDto;
import lombok.Data;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-19
 */
@Data
public class GeneratePacketLLMDto {
    private List<EntryDto<String, String>> attributeList;
    private String templateFolderName;
    private String templateXmlName;
    private String lastRandomDigits;
}
