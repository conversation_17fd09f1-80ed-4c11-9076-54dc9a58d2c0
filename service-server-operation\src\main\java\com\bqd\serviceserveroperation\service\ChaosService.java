package com.bqd.serviceserveroperation.service;

import com.bqd.model.chaos.ChaosExecutingInfoDto;
import com.bqd.model.chaos.ChaosInjectionDto;
import com.bqd.model.chaos.ChaosServerDto;
import com.bqd.model.common.CommonReqtDto;
import com.bqd.model.common.PageDto;

import java.io.InputStream;
import java.util.List;

public interface ChaosService {

    /**
     * 添加混沌目标服务器
     * @param serverInfoId
     */
    void addChaosServer(String serverInfoId);

    /**
     * 混沌目标服务器列表
     * @param pageNo
     * @param pageSize
     * @param chaosServerDto
     * @return
     */
    PageDto<ChaosServerDto> chaosServerList(Integer pageNo, Integer pageSize, ChaosServerDto chaosServerDto);

    /**
     * 下载混沌脚本
     * @return
     */
    InputStream downloadScript(String scriptName);

    /**
     * 部署混沌脚本（含下载）
     * @param serverInfoId
     */
    void setup(String serverInfoId);

    /**
     * 获取执行中的事件列表
     * @param type
     * @return
     */
    List<ChaosExecutingInfoDto> getExecutingList(String type);

    /**
     * 获取默认网卡名称
     * @param serverInfoId
     * @return
     */
    String getDefaultNicName(String serverInfoId);

    /**
     * 根据id销毁故障
     * @param id
     */
    void destroy(String id);

    /**
     * 执行linux命令
     * @param commonReqtDto
     */
    void httpOnewayLoss(CommonReqtDto commonReqtDto);

    /**
     * 混沌故障注入
     * @param chaosInjectionDto
     */
    void injection(ChaosInjectionDto chaosInjectionDto);

    /**
     * 检查探针状态
     * @param serverInfoId
     * @return
     */
    int checkStatus(String serverInfoId);

    /**
     * 根据ID删除
     * @param id
     */
    void deleteById(String id);
}
