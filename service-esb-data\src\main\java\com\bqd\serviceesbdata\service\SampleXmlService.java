package com.bqd.serviceesbdata.service;

import com.bqd.model.common.TreeDto;
import com.bqd.model.samplexml.dto.SampleXmlDto;
import com.bqd.model.samplexml.dto.SampleXmlRedisDto;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-27
 */
public interface SampleXmlService {
    SampleXmlRedisDto downloadSampleXmlFromRemote(String interfaceId);

    SampleXmlRedisDto getValueByCondition(SampleXmlDto sampleXmlDto);

    SampleXmlDto getSampleXml(String interfaceId, String name);

    void updateSampleXmlContent(SampleXmlDto sampleXmlDto);

    TreeDto getFieldTree(String interfaceId, String name);
}
