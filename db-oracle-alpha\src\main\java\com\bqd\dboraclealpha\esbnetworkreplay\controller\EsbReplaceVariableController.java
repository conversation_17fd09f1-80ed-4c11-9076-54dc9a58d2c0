package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbReplaceVariableMapper;
import com.bqd.model.esbnetworkreplay.EsbReplaceVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/enr/esbReplaceVariable")
public class EsbReplaceVariableController {

    @Autowired
    private EsbReplaceVariableMapper esbReplaceVariableMapper;

    @GetMapping("/selectAll")
    public List<EsbReplaceVariable> selectAll() {
        return esbReplaceVariableMapper.selectAll();
    }

    @GetMapping("/deleteById")
    void deleteById(@RequestParam String id) {
        esbReplaceVariableMapper.deleteById(id);
    }

    @PostMapping("/insert")
    void insert(@RequestBody EsbReplaceVariable esbReplaceVariable) {
        esbReplaceVariableMapper.insert(esbReplaceVariable);
    }

}
