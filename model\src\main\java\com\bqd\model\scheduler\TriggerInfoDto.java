package com.bqd.model.scheduler;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TriggerInfoDto {
    private String triggerName;
    private String triggerGroup;
    private String jobName;
    private String jobGroup;
    private String className;
    private String cronExpression;
    private String status;
    private String description;
    private String prevFireTime;
    private String nextFireTime;
}
