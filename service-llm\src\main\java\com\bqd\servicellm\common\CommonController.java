package com.bqd.servicellm.common;

import cn.hutool.core.util.StrUtil;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-24
 */
@RequestMapping("/common")
@RestController
public class CommonController {

    @PostMapping("/removeThinking")
    public String removeThinking(@RequestBody Map<String, String> map) {
        String text = map.get("text");
        return StrUtil.removeAllLineBreaks(StrUtil.subAfter(text, "</details>", true));
    }

}
