package com.bqd.dboraclealpha.projectmanagement.controller;

import com.bqd.dboraclealpha.projectmanagement.mapper.GitCredentialMapper;
import com.bqd.model.projectmanagement.GitCredential;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-09
 */
@RestController
@RequestMapping("/projectManagement/gitCredential")
public class GitCredentialController implements com.bqd.base.rpc.projectmanagement.GitCredentialMapper {

    @Autowired
    private GitCredentialMapper gitCredentialMapper;

    @Override
    @PostMapping("/selectByCondition")
    public List<GitCredential> selectByCondition(@RequestBody GitCredential gitCredential) {
        return gitCredentialMapper.selectByCondition(gitCredential);
    }

    @Override
    @PostMapping("/insert")
    public void insert(@RequestBody GitCredential gitCredential) {
        gitCredentialMapper.insert(gitCredential);
    }

    @Override
    public void deleteById(String id) {
        gitCredentialMapper.deleteById(id);
    }

    @Override
    public void updateById(GitCredential gitCredential) {
        gitCredentialMapper.updateById(gitCredential);
    }
}
