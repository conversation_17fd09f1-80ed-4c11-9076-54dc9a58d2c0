package com.bqd.dboraclealpha.esbnetworkreplay.mapper;

import com.bqd.model.esbnetworkreplay.EsbReplayDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EsbReplayDetailMapper {
    void insert(EsbReplayDetail esbReplayDetail);

    EsbReplayDetail selectByInfoId(String infoId);

    void deleteByInfoId(String infoId);

    EsbReplayDetail selectFieldValueByInfoId(@Param("infoId") String infoId, @Param("field") String field);
}
