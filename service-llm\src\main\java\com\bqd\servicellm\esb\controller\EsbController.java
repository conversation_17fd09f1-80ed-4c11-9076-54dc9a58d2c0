package com.bqd.servicellm.esb.controller;

import com.bqd.base.response.Response;
import com.bqd.model.esbdata.EsbServiceModel;
import com.bqd.servicellm.esb.service.EsbService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-21
 */
@RequestMapping("/esb")
@RestController
public class EsbController {

    @Autowired
    private EsbService esbService;

    @PostMapping("/serviceModel")
    public Response serviceModel(@RequestBody EsbServiceModel esbServiceModel) {
        return Response.success(esbService.serviceModel(esbServiceModel));
    }

    @GetMapping("/serviceModel")
    public Response serviceModel(@RequestParam String interfaceId) {
        return Response.success(esbService.serviceModel(new EsbServiceModel(interfaceId, null, null, null, null, null, null)));
    }

}
