package com.bqd.model.xhxrb;

import lombok.Data;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-09-30
 */
@Data
public class XhxRbQxztqk {
    private String systemName;
    private Integer openCount;
    private Integer assignedCount;
    private Integer repairingCount;
    private Integer repairedCount;
    private Integer reopenCount;
    private Integer dxqqrCount;
    private Integer dqrxhxCount;
    private Integer dxgCount;
    private Integer suspendCount;
    private Integer dzcCount;
    private Integer tqcszcCount;
    private Integer tqpmozcCount;
    private Integer newCount;
    private Integer rejectCount;
    private Integer dfcCount;
    private Integer closedCount;
    private Integer jjqrCount;
    private Integer totalCount;
}
