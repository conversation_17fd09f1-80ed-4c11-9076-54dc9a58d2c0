<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.transactionchain.mapper.ZtServiceBlMapper">

    <resultMap id="serviceBl" type="com.bqd.model.transactionchain.ZtServiceBl">
        <result property="beanRef" column="BEAN_REF"/>
        <result property="trxCode" column="TRX_CODE"/>
        <result property="actionDesc" column="ACTION_DESC"/>
        <result property="serviceName" column="SERVICE_NAME"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO SERVICE_BL (BEAN_REF, TRX_CODE, ACTION_DESC, SERVICE_NAME)
        VALUES (#{beanRef}, #{trxCode}, #{actionDesc}, #{serviceName})
    </insert>

    <delete id="truncateTable">
        TRUNCATE TABLE SERVICE_BL
    </delete>

    <select id="selectByBeanRefAndServiceName" resultMap="serviceBl">
        SELECT * FROM SERVICE_BL WHERE BEAN_REF = #{beanRef} AND SERVICE_NAME = #{serviceName}
    </select>

    <select id="selectByLikeTrxCode" resultMap="serviceBl">
        SELECT *
        FROM SERVICE_BL
        WHERE TRX_CODE LIKE CONCAT('%', CONCAT(#{trxCode}, '%'))
    </select>
</mapper>