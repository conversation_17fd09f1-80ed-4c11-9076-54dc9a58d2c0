package com.bqd.servicellm.demandDoc.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bqd.base.rpc.llm.DemandDocMapper;
import com.bqd.base.rpc.llm.FunctionPointsMapper;
import com.bqd.base.rpc.llm.TestCasesMapper;
import com.bqd.model.common.PageDto;
import com.bqd.model.llm.DemandDoc;
import com.bqd.model.llm.FunctionPoints;
import com.bqd.model.llm.TestCases;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;

@Service
@Log4j2
public class DemandDocService {


    @Autowired
    private DemandDocMapper demandDocMapper;

    @Autowired
    private FunctionPointsMapper functionPointsMapper;

    @Autowired
    private TestCasesMapper testCasesMapper;


    @Autowired
    private DifyService difyService;

    @Async("externalTaskExecutor") // 使用自定义线程池
    public void processCaseGenTask(String functionId) {
        log.info("开始异步处理功能点: {}", functionId);
        FunctionPoints functionPoints = functionPointsMapper.selectAllById(functionId);
        String content = String.valueOf(functionPoints.getStatusMeta());

        TestCases testCases = testCasesMapper.selectAllByFunctionId(functionId);
        String uuid = UUID.randomUUID().toString();
        if (testCases != null) {
            uuid = testCases.getId();
            testCasesMapper.updateSelective(new TestCases(uuid,functionId,new Date(),1,new JSONArray().toJSONString(0)));
        }else{
            testCasesMapper.insertSelective(new TestCases(uuid, functionId, new Date(), 1, new JSONArray().toJSONString(0)));
        }


        String result = difyService.sendRequest(content);
        if (!StrUtil.isEmpty(result)) {
            testCasesMapper.updateSelective(new TestCases(uuid, functionId, null, 0, result));
        }else {
            testCasesMapper.updateSelective(new TestCases(uuid, functionId, null, -1, new JSONArray().toJSONString(0)));
        }
        System.out.println(functionId+"处理完成");
    }

    public JSONObject getTestCasesByFunctionId(String FunctionId) {

        TestCases testCases = testCasesMapper.selectAllByFunctionId(FunctionId);
        JSONObject result = new JSONObject();
        if (testCases==null){
            result.set("status","无记录");
        }else {
            if (testCases.getIsProcessing() == -1) {
                result.set("status","存在记录，案例生成发生异常");
            }
            if (testCases.getIsProcessing() == 1) {
                result.set("status","存在记录，案例生成中");
            }
            if (testCases.getIsProcessing() == 0) {
                result.set("status","存在记录，案例生成完成");
                result.set("data",testCases.getStatusMeta());
            }
        }
        return result;
    }

    @Async
    public void processWordAsync(byte[] fileBytes, String fileName) {
        log.info("开始异步处理文件: {}", fileName);
        String uuid_doc = UUID.randomUUID().toString();

        demandDocMapper.insertSelective(new DemandDoc(uuid_doc, fileName, new Date(), "1", 1, new JSONArray().toJSONString(0)));

        try (InputStream inputStream = new ByteArrayInputStream(fileBytes)) {
            JSONArray simpleExtract = parseWordToJson(inputStream, fileName);
            JSONArray filteredContent = removeRedundantNodes(simpleExtract);
            JSONArray moreThanFourLevel = findMoreThanFourLevel(filteredContent);
            // TODO: 2025/5/8 拆分功能点，进行单独存储

            List<FunctionPoints> extractedList = new ArrayList<>();
            JSONArray structuredArray = extractFunctionPoints(moreThanFourLevel, 1, extractedList, uuid_doc);

            for (FunctionPoints functionPoints : extractedList) {
                System.out.println(functionPoints);
                functionPointsMapper.insertSelective(functionPoints);
            }


            demandDocMapper.updateSelective(new DemandDoc(uuid_doc, null, null, null, 0, structuredArray.toJSONString(0)));

            log.info("异步处理完成");

        } catch (Exception e) {
            log.error("处理文件失败: {}", fileName, e);
            demandDocMapper.updateSelective(new DemandDoc(uuid_doc, null, null, null, -1, new JSONArray().toJSONString(0)));
        }
    }


    private JSONArray extractFunctionPoints(JSONArray array, int depth, List<FunctionPoints> extractedList, String uuid_doc) {
        JSONArray resultArray = new JSONArray();
        for (Object obj : array) {
            JSONObject item = (JSONObject) obj;
            JSONObject newItem = new JSONObject();
            newItem.put("title", item.getStr("title"));

            if (item.containsKey("children")) {
                JSONArray children = item.getJSONArray("children");

                // 判断是否为第3层
                if (depth == 2) {
                    JSONArray newChildren = new JSONArray();
                    for (Object childObj : children) {
                        JSONObject child = (JSONObject) childObj;
                        if (child.containsKey("children")) {
                            String uuid = IdUtil.randomUUID();
                            // 存储抽取结构
                            FunctionPoints func = new FunctionPoints(uuid, uuid_doc, child.getStr("title"), new Date(), child.getJSONArray("children").toJSONString(0));
                            extractedList.add(func);

                            // 替换为function_id结构
                            JSONObject replaced = new JSONObject();
                            replaced.put("title", child.getStr("title"));
                            replaced.put("function_id", uuid);
                            newChildren.add(replaced);
                        } else {
                            newChildren.add(child);
                        }
                    }
                    newItem.put("children", newChildren);
                } else {
                    newItem.put("children", extractFunctionPoints(children, depth + 1, extractedList, uuid_doc));
                }
            }

            resultArray.add(newItem);
        }
        return resultArray;
    }


    public JSONArray parseWordToJson(InputStream inputStream, String fileName) throws Exception {
        // 禁用压缩比检查（将限制设置得非常低）
        ZipSecureFile.setMinInflateRatio(0.00001);
        if (!fileName.endsWith(".docx")) {
            throw new IllegalArgumentException("仅支持 .docx 格式的 Word 文件");
        }

        XWPFDocument document = new XWPFDocument(inputStream);

        List<ParagraphNode> nodeList = new ArrayList<>();
        Map<Integer, ParagraphNode> levelMap = new HashMap<>();

        for (IBodyElement element : document.getBodyElements()) {
            if (element instanceof XWPFParagraph) {
                XWPFParagraph para = (XWPFParagraph) element;
                String styleId = para.getStyle();
                String style = "";
                if (StrUtil.isEmpty(styleId)) {
                    style = "plainText";
                } else {
                    style = document.getStyles().getStyle(styleId).getName();
                }

                String text = para.getText().trim();

                if (style.toLowerCase().matches("heading\\s*[1-9]") || style.matches("标题\\s*[1-9]")) {
                    int level = Integer.parseInt(style.replaceAll("\\D", ""));
                    ParagraphNode node = new ParagraphNode(text, level); // text 是标题内容

                    // 清理当前节点以下的所有层级，防止内容挂错
                    for (int i = level + 1; i <= 9; i++) {
                        levelMap.remove(i);
                    }

                    if (level == 1) {
                        nodeList.add(node);
                    } else {
                        ParagraphNode parent = null;
                        for (int i = level - 1; i >= 1; i--) {
                            if (levelMap.containsKey(i)) {
                                parent = levelMap.get(i);
                                break;
                            }
                        }
                        if (parent != null) {
                            parent.children.add(node);
                        }
                    }
                    levelMap.put(level, node);

                } else {
                    // 处理普通段落，将正文附加到最近的标题节点
                    if (!text.isEmpty()) {
                        ParagraphNode last = null;
                        for (int i = 9; i >= 1; i--) {
                            if (levelMap.containsKey(i)) {
                                last = levelMap.get(i);
                                break;
                            }
                        }
                        if (last != null) {
                            last.content.append(text).append("\n");
                        }
                    }
                }

            }
        }

        JSONArray jsonArray = new JSONArray();
        for (ParagraphNode node : nodeList) {
            jsonArray.add(node.toJson());
        }

        return jsonArray;
    }

    public JSONArray removeRedundantNodes(JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if (jsonObject.getStr("title").contains("简介") || jsonObject.getStr("title").contains("非功能") ||
                    jsonObject.getStr("title").contains("安全")) {
                jsonArray.remove(i);
                i--;
            }
        }
        return jsonArray;
    }

    public JSONArray findMoreThanFourLevel(JSONArray jsonArray) {
        JSONArray filteredArray = new JSONArray();

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject filteredNode = filterDeepNodes(jsonArray.getJSONObject(i), 1);
            if (filteredNode != null) {
                filteredArray.add(filteredNode);
            }
        }

//        System.out.println("保留层级结构的超过四层节点：");
//        System.out.println(filteredArray.toStringPretty());

        return filteredArray;
    }

    /**
     * 递归查找超过4层结构的路径，并保留其祖先节点结构
     *
     * @param obj   当前节点
     * @param level 当前层级
     * @return 如果该节点及其子孙中有超过4层的节点，则返回剪裁后的对象；否则返回null
     */
    private static JSONObject filterDeepNodes(JSONObject obj, int level) {
        boolean isDeep = level > 3;
        boolean hasChildren = obj.containsKey("children");

        JSONArray newChildren = new JSONArray();

        if (hasChildren) {
            JSONArray children = obj.getJSONArray("children");
            for (int i = 0; i < children.size(); i++) {
                JSONObject filteredChild = filterDeepNodes(children.getJSONObject(i), level + 1);
                if (filteredChild != null) {
                    newChildren.add(filteredChild);
                }
            }
        }

        if (isDeep || newChildren.size() > 0) {
            JSONObject newObj = new JSONObject();
            newObj.set("title", obj.getStr("title"));
            if (obj.containsKey("content")) {
                newObj.set("content", obj.getStr("content"));
            }
            if (newChildren.size() > 0) {
                newObj.set("children", newChildren);
            }
            return newObj;
        }

        return null;
    }


    public PageDto<DemandDoc> getFileList(int page, int size) {
        int totalCount = demandDocMapper.count();
        int totalPage = PageUtil.totalPage(totalCount, size);

        int start = PageUtil.getStart(page, size);
        List<DemandDoc> records = demandDocMapper.selectPageLimit(start, size);

        PageDto<DemandDoc> demandDocPage = new PageDto<>(page, size, totalPage, totalCount, records);
        return demandDocPage;
    }

    public int deleteFileById(String id) {
        return demandDocMapper.deleteById(id);
    }

    public JSONArray getDetailById(String id) {
        DemandDoc demandDoc = demandDocMapper.selectAllById(id);
        if (demandDoc != null) {
            return JSONUtil.parseArray(demandDoc.getStatusMeta());
        }
        return null;
    }

    public JSONArray getFunctionDetailById(String id) {
        FunctionPoints functionPoints = functionPointsMapper.selectAllById(id);
        if (functionPoints != null) {
            return JSONUtil.parseArray(functionPoints.getStatusMeta());
        }
        return null;
    }

    public int updateFunctionPoint(String id, String content) {
        FunctionPoints functionPoints = new FunctionPoints(id, null, null, null, content);
        return functionPointsMapper.updateSelective(functionPoints);
    }

    // 内部类表示每一个目录节点
    static class ParagraphNode {
        String title;
        int level;
        List<ParagraphNode> children = new ArrayList<>();
        StringBuilder content = new StringBuilder();

        ParagraphNode(String title, int level) {
            this.title = title;
            this.level = level;
        }

        JSONObject toJson() {
            JSONObject json = new JSONObject();
            json.put("title", title);
            if (!children.isEmpty()) {
                JSONArray childrenArray = new JSONArray();
                for (ParagraphNode child : children) {
                    childrenArray.add(child.toJson());
                }
                json.put("children", childrenArray);
            } else {
                json.put("content", content.toString().trim());
            }
            return json;
        }
    }


}
