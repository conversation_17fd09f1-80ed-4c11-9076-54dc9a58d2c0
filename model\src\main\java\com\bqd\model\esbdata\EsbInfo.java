package com.bqd.model.esbdata;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-11-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsbInfo {
    private String id;
    private String interfaceId;
    private String svcCorrId;
    private String eventTypeNM;
    private String eventTime;
    private String url;
}
