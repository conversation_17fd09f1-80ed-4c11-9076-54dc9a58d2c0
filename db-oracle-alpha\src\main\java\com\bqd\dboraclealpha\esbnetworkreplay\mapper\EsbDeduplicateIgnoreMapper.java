package com.bqd.dboraclealpha.esbnetworkreplay.mapper;

import com.bqd.model.esbnetworkreplay.EsbDeduplicateIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EsbDeduplicateIgnoreMapper {
    EsbDeduplicateIgnore selectByInterfaceId(@Param("interfaceId") String interfaceId);

    void insert(EsbDeduplicateIgnore esbDeduplicateIgnore);

    void updateByInterfaceId(EsbDeduplicateIgnore esbDeduplicateIgnore);
}
