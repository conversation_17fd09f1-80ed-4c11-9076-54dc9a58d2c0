package com.bqd.integratedtester.sibstranscheck.controller;

import cn.hutool.core.util.BooleanUtil;
import com.bqd.base.response.Response;
import com.bqd.integratedtester.sibstranscheck.service.SibsTransCheckService;
import com.bqd.model.sibstranscheck.SibsTransCheckConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-07-05
 */
@RestController
@RequestMapping("/sibsTransCheck")
public class SibsTransCheckController {

    @Autowired
    private SibsTransCheckService sibsTransCheckService;

    /**
     * 上传zip文件
     *
     * @param multipartFile
     * @return
     */
    @PostMapping("/upload")
    public Response upload(@RequestParam("file") MultipartFile multipartFile) {
        if (BooleanUtil.or(multipartFile.getOriginalFilename().endsWith(".zip"), multipartFile.getOriginalFilename().endsWith(".json"))) {
            return Response.success(sibsTransCheckService.uploadAndSave(multipartFile));
        }
        return Response.fail();
    }

    /**
     * 删除已上传的文件
     *
     * @param id
     * @return
     */
    @GetMapping("/removeUploadFile")
    public Response removeUploadFile(@RequestParam("id") String id) {
        sibsTransCheckService.removeUploadFile(id);
        return Response.success();
    }

    /**
     * 获取已上传文件的列表
     *
     * @return
     */
    @GetMapping("/getUploadFileList")
    public Response getUploadFileList() {
        return Response.success(sibsTransCheckService.getUploadFileList());
    }

    /**
     * 解析上传文件到数据库
     *
     * @return
     */
    @GetMapping("/parseToDB")
    public Response parseToDB(@RequestParam("id") String id) {
        sibsTransCheckService.parseToDB(id);
        return Response.success();
    }

    @GetMapping("/clearHttpDataDB")
    public Response clearHttpDataDB() {
        sibsTransCheckService.clearHttpDataDB();
        return Response.success();
    }

    @GetMapping("/clearCheckResultDB")
    public Response clearCheckResultDB() {
        sibsTransCheckService.clearCheckResultDB();
        return Response.success();
    }

    /**
     * 清空数据库
     *
     * @return
     */
    @GetMapping("/clearDB")
    public Response clearDB() {
        sibsTransCheckService.clearHttpDataDB();
        sibsTransCheckService.clearCheckResultDB();
        return Response.success();
    }

    /**
     * xml json互转校验
     *
     * @return
     */
    @GetMapping("/sibsTransCheck")
    public Response sibsTransCheck() {
        sibsTransCheckService.sibsTransCheck();
        return Response.success();
    }

    @GetMapping("/getPagedHttpDataInfo")
    public Response getPagedHttpDataInfo(@RequestParam("pageNo") int pageNo,
                                         @RequestParam("pageSize") int pageSize,
                                         @RequestParam(value = "seqNo", required = false) String seqNo,
                                         @RequestParam(value = "transTimestamp", required = false) String transTimestamp) {
        return Response.success(sibsTransCheckService.getPagedHttpDataInfo(pageNo, pageSize, seqNo, transTimestamp));
    }

    @GetMapping("/getHttpDataByInfoId")
    public Response getHttpDataByInfoId(@RequestParam("infoId") String infoId) {
        return Response.success(sibsTransCheckService.getHttpDataByInfoId(infoId));
    }

    @GetMapping("/getPagedTransCheckResult")
    public Response getPagedTransCheckResult(@RequestParam("pageNo") int pageNo, @RequestParam("pageSize") int pageSize) {
        return Response.success(sibsTransCheckService.getPagedTransCheckResult(pageNo, pageSize));
    }

    @GetMapping("/getFailedInfoByResultId")
    public Response getFailedInfoByResultId(@RequestParam("resultId") String resultId) {
        return Response.success(sibsTransCheckService.getFailedInfoByResultId(resultId));
    }

    @GetMapping("/getCheckConfigListByType")
    public Response getCheckConfigListByType(@RequestParam("type") String type) {
        return Response.success(sibsTransCheckService.getCheckConfigListByType(type));
    }

    @PostMapping("/addCheckConfig")
    public Response addCheckConfig(@RequestBody SibsTransCheckConfig sibsTransCheckConfig) {
        sibsTransCheckService.addCheckConfig(sibsTransCheckConfig);
        return Response.success();
    }

    @GetMapping("/deleteCheckConfigById")
    public Response deleteCheckConfigById(@RequestParam("id") String id) {
        sibsTransCheckService.deleteCheckConfigById(id);
        return Response.success();
    }

}
