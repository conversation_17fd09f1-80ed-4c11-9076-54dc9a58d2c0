<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.projectmanagement.mapper.GitRepoInfoMapper">

    <resultMap id="gitRepoInfo" type="com.bqd.model.projectmanagement.GitRepoInfo">
        <id column="ID" property="id" javaType="String"/>
        <result column="REMOTE_URL" property="remoteUrl" javaType="String"/>
        <result column="LOCAL_PATH" property="localPath" javaType="String"/>
        <result column="REPOSITORY_NAME" property="repositoryName" javaType="String"/>
        <result column="BRANCH_NAME" property="branchName" javaType="String"/>
        <result column="SCHEDULED" property="scheduled" javaType="String"/>
        <result column="UPDATE_TIME" property="updateTime" javaType="String"/>
        <result column="REPOSITORY_COMMENT" property="repositoryComment" javaType="String"/>
        <result column="CREDENTIAL_ID" property="credentialId" javaType="java.lang.String"/>
        <result column="UPDATE_STATUS" property="updateStatus" javaType="java.lang.String"/>
    </resultMap>

    <insert id="insert">
        INSERT INTO GIT_REPO_INFO (ID, REMOTE_URL, LOCAL_PATH, REPOSITORY_NAME, BRANCH_NAME, SCHEDULED, UPDATE_TIME,
                                   REPOSITORY_COMMENT, CREDENTIAL_ID, UPDATE_STATUS)
        VALUES (#{id}, #{remoteUrl,jdbcType=VARCHAR}, #{localPath,jdbcType=VARCHAR}, #{repositoryName,jdbcType=VARCHAR},
                #{branchName,jdbcType=VARCHAR}, #{scheduled,jdbcType=VARCHAR}, #{updateTime,jdbcType=VARCHAR},
                #{repositoryComment,jdbcType=VARCHAR}, #{credentialId,jdbcType=VARCHAR},
                #{updateStatus,jdbcType=VARCHAR})
    </insert>

    <select id="selectByCondition" resultMap="gitRepoInfo">
        SELECT *
        FROM GIT_REPO_INFO
        WHERE 1=1
        <if test="id != null">
            AND ID = #{id}
        </if>
        <if test="remoteUrl != null">
            AND REMOTE_URL = #{remoteUrl,jdbcType=VARCHAR}
        </if>
        <if test="localPath != null">
            AND LOCAL_PATH = #{localPath,jdbcType=VARCHAR}
        </if>
        <if test="repositoryName != null">
            AND REPOSITORY_NAME = #{repositoryName,jdbcType=VARCHAR}
        </if>
        <if test="branchName != null">
            AND BRANCH_NAME = #{branchName,jdbcType=VARCHAR}
        </if>
        <if test="scheduled != null">
            AND SCHEDULED = #{scheduled,jdbcType=VARCHAR}
        </if>
        <if test="updateTime != null">
            AND UPDATE_TIME = #{updateTime}
        </if>
        <if test="repositoryComment != null">
            AND REPOSITORY_COMMENT = #{repositoryComment,jdbcType=VARCHAR}
        </if>
        <if test="credentialId != null">
            AND CREDENTIAL_ID = #{credentialId,jdbcType=VARCHAR}
        </if>
        <if test="updateStatus != null">
            AND UPDATE_STATUS = #{updateStatus,jdbcType=VARCHAR}
        </if>
        ORDER BY UPDATE_TIME DESC
    </select>

    <update id="updateById">
        UPDATE GIT_REPO_INFO
        SET SCHEDULED = #{scheduled},
        REPOSITORY_COMMENT = #{repositoryComment,jdbcType=VARCHAR},
        <if test="updateTime != null and updateTime.length != 0">
            UPDATE_TIME = #{updateTime},
        </if>
        CREDENTIAL_ID = #{credentialId,jdbcType=VARCHAR},
        UPDATE_STATUS = #{updateStatus,jdbcType=VARCHAR}
        WHERE ID = #{id}
    </update>

    <delete id="deleteById">
        DELETE
        FROM GIT_REPO_INFO
        WHERE ID = #{id}
    </delete>
</mapper>