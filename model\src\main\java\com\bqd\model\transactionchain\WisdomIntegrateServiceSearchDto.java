package com.bqd.model.transactionchain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WisdomIntegrateServiceSearchDto {
    private String mvcId;
    private String description;
    private List<TradeNameSearchDto> tradeNameSearchDtoList;
    private List<ServiceInfo> serviceInfoList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TradeNameSearchDto {
        private String tradeName;
        private String filePath;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceInfo{
        private String serviceName;
        private String serviceGroup;
        private String description;
        private String beanRef;
        private List<ESBInfo> esbInfoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ESBInfo {
        private String interfaceId;
        private String interfaceName;
    }
}
