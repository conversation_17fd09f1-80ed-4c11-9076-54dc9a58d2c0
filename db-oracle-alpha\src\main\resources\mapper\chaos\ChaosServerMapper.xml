<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclealpha.chaos.mapper.ChaosServerMapper">

    <resultMap id="chaosServer" type="com.bqd.model.chaos.ChaosServer">
        <result column="SERVER_INFO_ID" property="serverInfoId" javaType="java.lang.String"/>
        <result column="AGENT_STATUS" property="agentStatus" javaType="java.lang.Integer"/>
    </resultMap>

    <resultMap id="chaosServerDto" type="com.bqd.model.chaos.ChaosServerDto" extends="chaosServer">
        <result column="SERVER_NAME" property="serverName" javaType="java.lang.String"/>
        <result column="SERVER_IP" property="serverIp" javaType="java.lang.String"/>
        <result column="USERNAME" property="username" javaType="java.lang.String"/>
        <result column="PW" property="pw" javaType="java.lang.String"/>
        <result column="ENVIRONMENT" property="environment" javaType="java.lang.String"/>
        <result column="AUTH_TYPE" property="authType" javaType="java.lang.String"/>
        <result column="REMARK" property="remark" javaType="java.lang.String"/>
        <result column="ADD_TIME" property="addTime" javaType="java.lang.Long" jdbcType="NUMERIC"/>
    </resultMap>

    <select id="countDto" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM CHAOS_SERVER t1 INNER JOIN SERVER_INFO t2 ON t1.SERVER_INFO_ID = t2.ID WHERE 1=1
        <if test="serverInfoId != null and serverInfoId.length() != 0">
            AND SERVER_INFO_ID = #{serverInfoId}
        </if>
        <if test="serverName != null and serverName.length() != 0">
            AND SERVER_NAME LIKE CONCAT('%', CONCAT(#{serverName}, '%'))
        </if>
        <if test="environment != null and environment.length() != 0">
            AND ENVIRONMENT = #{environment}
        </if>
        <if test="serverIp != null and serverIp.length() != 0">
            AND SERVER_IP = #{serverIp}
        </if>
        <if test="username != null and username.length() != 0">
            AND USERNAME = #{username}
        </if>
        <if test="pw != null and pw.length() != 0">
            AND PW = #{pw}
        </if>
        <if test="authType != null and authType.length() != 0">
            AND AUTH_TYPE = #{authType}
        </if>

        <if test="remark != null and remark.length() != 0">
            AND REMARK = #{remark}
        </if>
    </select>

    <select id="selectDtoByCondition" resultMap="chaosServerDto">
        SELECT * FROM (
        SELECT ROWNUM AS ROW_NUM, t1.*, t2.* FROM CHAOS_SERVER t1 INNER JOIN SERVER_INFO t2 ON t1.SERVER_INFO_ID = t2.ID
        WHERE 1 = 1
        <if test="chaosServerDto.serverInfoId!= null and chaosServerDto.serverInfoId!= ''">
            AND SERVER_INFO_ID = #{chaosServerDto.serverInfoId}
        </if>
        <if test="chaosServerDto.agentStatus!= null">
            AND AGENT_STATUS = #{chaosServerDto.agentStatus}
        </if>
        <if test="chaosServerDto.serverName!= null and chaosServerDto.serverName!= ''">
            AND SERVER_NAME LIKE CONCAT('%', CONCAT(#{chaosServerDto.serverName}, '%'))
        </if>
        <if test="chaosServerDto.environment!= null and chaosServerDto.environment!= ''">
            AND ENVIRONMENT = #{chaosServerDto.environment}
        </if>
        <if test="chaosServerDto.serverIp!= null and chaosServerDto.serverIp!= ''">
            AND SERVER_IP = #{chaosServerDto.serverIp}
        </if>
        <if test="chaosServerDto.username!= null and chaosServerDto.username!= ''">
            AND USERNAME = #{chaosServerDto.username}
        </if>
        <if test="chaosServerDto.pw!= null and chaosServerDto.pw!= ''">
            AND PW = #{chaosServerDto.pw}
        </if>
        <if test="chaosServerDto.authType!= null and chaosServerDto.authType!= ''">
            AND AUTH_TYPE = #{chaosServerDto.authType}
        </if>

        <if test="chaosServerDto.remark!= null and chaosServerDto.remark!= ''">
            AND REMARK = #{chaosServerDto.remark}
        </if>) WHERE ROW_NUM >= #{startRow} and #{endRow} >= ROW_NUM
    </select>

    <insert id="insert" parameterType="com.bqd.model.chaos.ChaosServer">
        INSERT INTO CHAOS_SERVER (SERVER_INFO_ID, AGENT_STATUS)
        VALUES (#{serverInfoId}, #{agentStatus})
    </insert>

    <update id="updateById" parameterType="com.bqd.model.chaos.ChaosServer">
        UPDATE CHAOS_SERVER
        SET AGENT_STATUS = #{agentStatus, jdbcType=VARCHAR}
        WHERE SERVER_INFO_ID = #{serverInfoId, jdbcType=VARCHAR}
    </update>

    <delete id="deleteById">
        DELETE
        FROM CHAOS_SERVER
        WHERE SERVER_INFO_ID = #{serverInfoId}
    </delete>
</mapper>
