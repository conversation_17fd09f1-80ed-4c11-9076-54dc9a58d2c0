package com.bqd.dboraclebeta.xhx.controller;

import com.bqd.dboraclebeta.xhx.mapper.XhxRbGrzxqkMapper;
import com.bqd.model.xhxrb.XhxRbGrzxqk;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-25
 */
@RestController
@RequestMapping("/xhxrb/grzxqk")
public class GrzxqkController {

    @Autowired
    private XhxRbGrzxqkMapper xhxRbGrzxqkMapper;

    @GetMapping("/selectAll")
    public List<XhxRbGrzxqk> selectAll() {
        return xhxRbGrzxqkMapper.selectAll();
    }

    @GetMapping("/pcdInsertGrpTester")
    public void pcdInsertGrpTester() {
        xhxRbGrzxqkMapper.pcdInsertGrpTester();
    }

    @GetMapping("/pcdUpdateExecuteCount")
    public void pcdUpdateExecuteCount() {
        xhxRbGrzxqkMapper.pcdUpdateExecuteCount();
    }

    @GetMapping("/pcdUpdatePassedCount")
    public void pcdUpdatePassedCount() {
        xhxRbGrzxqkMapper.pcdUpdatePassedCount();
    }

    @GetMapping("/pcdUpdateFailedCount")
    public void pcdUpdateFailedCount() {
        xhxRbGrzxqkMapper.pcdUpdateFailedCount();
    }

    @GetMapping("/pcdUpdateBlockCount")
    public void pcdUpdateBlockCount() {
        xhxRbGrzxqkMapper.pcdUpdateBlockCount();
    }

    @GetMapping("/pcdUpdateExecutingCount")
    public void pcdUpdateExecutingCount() {
        xhxRbGrzxqkMapper.pcdUpdateExecutingCount();
    }

    @GetMapping("/pcdUpdateCancelCount")
    public void pcdUpdateCancelCount() {
        xhxRbGrzxqkMapper.pcdUpdateCancelCount();
    }

    @GetMapping("/pcdUpdateDayExecuteCount")
    public void pcdUpdateDayExecuteCount(@RequestParam String startTime, @RequestParam String endTime) {
        xhxRbGrzxqkMapper.pcdUpdateDayExecuteCount(startTime, endTime);
    }

    @GetMapping("/pcdUpdateDayPassedCount")
    public void pcdUpdateDayPassedCount(@RequestParam String startTime, @RequestParam String endTime) {
        xhxRbGrzxqkMapper.pcdUpdateDayPassedCount(startTime, endTime);
    }

    @GetMapping("/pcdUpdateDayFailedCount")
    public void pcdUpdateDayFailedCount(@RequestParam String startTime, @RequestParam String endTime) {
        xhxRbGrzxqkMapper.pcdUpdateDayFailedCount(startTime, endTime);
    }

}
