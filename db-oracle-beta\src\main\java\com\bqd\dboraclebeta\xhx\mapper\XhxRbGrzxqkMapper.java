package com.bqd.dboraclebeta.xhx.mapper;

import com.bqd.model.xhxrb.XhxRbGrzxqk;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface XhxRbGrzxqkMapper {
    List<XhxRbGrzxqk> selectAll();

    void pcdInsertGrpTester();

    void pcdUpdateExecuteCount();
    void pcdUpdatePassedCount();
    void pcdUpdateFailedCount();
    void pcdUpdateBlockCount();
    void pcdUpdateExecutingCount();
    void pcdUpdateCancelCount();

    void pcdUpdateDayExecuteCount(@Param("startTime") String startTime, @Param("endTime") String endTime);
    void pcdUpdateDayPassedCount(@Param("startTime") String startTime, @Param("endTime") String endTime);
    void pcdUpdateDayFailedCount(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
