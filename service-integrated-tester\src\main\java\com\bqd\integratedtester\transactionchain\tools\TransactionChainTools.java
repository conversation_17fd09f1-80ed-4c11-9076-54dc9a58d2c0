package com.bqd.integratedtester.transactionchain.tools;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;

import java.io.File;
import java.util.Map;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-07
 */
public class TransactionChainTools {

    public static void getBlFileInfo(File file, String pathId, Map<String, String> blFileInfo) {
        if (!FileUtil.isDirectory(file)) {
            blFileInfo.put(pathId, file.getAbsolutePath());
            return;
        }
        for (File f : file.listFiles()) {
            getBlFileInfo(f, pathId + (StrUtil.isBlank(pathId) ? "" : ".") + f.getName().replace(".bl", ""), blFileInfo);
        }
    }

}
