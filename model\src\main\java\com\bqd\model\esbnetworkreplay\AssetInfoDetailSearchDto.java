package com.bqd.model.esbnetworkreplay;

import com.bqd.model.common.OraclePagedCommonParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssetInfoDetailSearchDto extends OraclePagedCommonParam {
    private String interfaceId;
    private String channelId;
    private String transCde;
    private String csmrId;
    private String versionNumber;
    private String esbRespCode;
    private String esbRespMsg;
    private String startTime;
    private String endTime;
}
