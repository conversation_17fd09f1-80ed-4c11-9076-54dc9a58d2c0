package com.bqd.serviceenr.service;

import com.bqd.model.common.PageDto;
import com.bqd.model.esbnetworkreplay.*;

import java.util.List;

public interface AssetService {

    List<EsbAssetInfoDto> getAssetInfoFieldValue(List<String> fieldList);

    List<String> getInfoFieldValueByInterfaceId(String interfaceId, String field);

    PageDto<EsbAssetInfoDto> getPagedAssetInfo(AssetInfoSearchDto assetInfoSearchDto);

    PageDto<EsbAssetInfo> getPagedAssetInfoDetail(AssetInfoDetailSearchDto assetInfoDetailSearchDto);

    EsbAssetDetail getAssetDetailFieldValue(String id, List<String> fieldList);

    void deleteAsset(String id);

}
