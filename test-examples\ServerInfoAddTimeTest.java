package com.bqd.test.servermanagement;

import com.bqd.model.servermanagement.ServerInfo;
import com.bqd.serviceserveroperation.service.ServerManagementService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test cases for ServerInfo ADD_TIME column functionality
 * 
 * @Author: AI Assistant
 * @CreateTime: 2025-01-XX
 */
@SpringBootTest
@Transactional
public class ServerInfoAddTimeTest {

    @Autowired
    private ServerManagementService serverManagementService;

    @Test
    public void testAddServerWithAutoTimestamp() {
        // Arrange
        ServerInfo serverInfo = new ServerInfo();
        serverInfo.setServerName("Test Server");
        serverInfo.setServerIp("*************");
        serverInfo.setUsername("testuser");
        serverInfo.setPw("testpass");
        serverInfo.setEnvironment("TEST");
        serverInfo.setAuthType("PASSWORD");
        serverInfo.setRemark("Test server for ADD_TIME functionality");
        
        Date beforeAdd = new Date();
        
        // Act
        serverManagementService.addServer(serverInfo);
        
        Date afterAdd = new Date();
        
        // Assert
        assertNotNull(serverInfo.getId(), "Server ID should be generated");
        assertNotNull(serverInfo.getAddTime(), "ADD_TIME should be automatically set");
        assertTrue(serverInfo.getAddTime().getTime() >= beforeAdd.getTime(), 
                   "ADD_TIME should be after or equal to before-add time");
        assertTrue(serverInfo.getAddTime().getTime() <= afterAdd.getTime(), 
                   "ADD_TIME should be before or equal to after-add time");
    }

    @Test
    public void testQueryServerByAddTime() {
        // Arrange
        ServerInfo serverInfo = new ServerInfo();
        serverInfo.setServerName("Query Test Server");
        serverInfo.setServerIp("*************");
        serverInfo.setUsername("queryuser");
        serverInfo.setPw("querypass");
        serverInfo.setEnvironment("TEST");
        serverInfo.setAuthType("PASSWORD");
        serverInfo.setRemark("Test server for ADD_TIME query");
        
        // Act
        serverManagementService.addServer(serverInfo);
        
        // Query by ADD_TIME
        ServerInfo queryCondition = new ServerInfo();
        queryCondition.setAddTime(serverInfo.getAddTime());
        List<ServerInfo> results = serverManagementService.getByCondition(queryCondition);
        
        // Assert
        assertFalse(results.isEmpty(), "Should find servers with matching ADD_TIME");
        assertTrue(results.stream().anyMatch(s -> s.getId().equals(serverInfo.getId())), 
                   "Should find the created server");
    }

    @Test
    public void testUpdateServerWithAddTime() {
        // Arrange
        ServerInfo serverInfo = new ServerInfo();
        serverInfo.setServerName("Update Test Server");
        serverInfo.setServerIp("*************");
        serverInfo.setUsername("updateuser");
        serverInfo.setPw("updatepass");
        serverInfo.setEnvironment("TEST");
        serverInfo.setAuthType("PASSWORD");
        serverInfo.setRemark("Test server for ADD_TIME update");
        
        serverManagementService.addServer(serverInfo);
        Date originalAddTime = serverInfo.getAddTime();
        
        // Act
        serverInfo.setServerName("Updated Test Server");
        Date newAddTime = new Date(originalAddTime.getTime() + 1000); // 1 second later
        serverInfo.setAddTime(newAddTime);
        
        serverManagementService.updateById(serverInfo);
        
        // Assert
        ServerInfo queryCondition = new ServerInfo();
        queryCondition.setId(serverInfo.getId());
        List<ServerInfo> results = serverManagementService.getByCondition(queryCondition);
        
        assertEquals(1, results.size(), "Should find exactly one server");
        assertEquals("Updated Test Server", results.get(0).getServerName(), 
                     "Server name should be updated");
        assertEquals(newAddTime, results.get(0).getAddTime(), 
                     "ADD_TIME should be updated");
    }

    @Test
    public void testPagedQueryWithAddTime() {
        // Arrange
        Date testDate = new Date();
        
        // Create multiple servers with the same ADD_TIME for testing
        for (int i = 0; i < 3; i++) {
            ServerInfo serverInfo = new ServerInfo();
            serverInfo.setServerName("Paged Test Server " + i);
            serverInfo.setServerIp("192.168.1." + (110 + i));
            serverInfo.setUsername("pageduser" + i);
            serverInfo.setPw("pagedpass" + i);
            serverInfo.setEnvironment("TEST");
            serverInfo.setAuthType("PASSWORD");
            serverInfo.setRemark("Test server " + i + " for paged query");
            serverInfo.setAddTime(testDate);
            
            serverManagementService.addServer(serverInfo);
        }
        
        // Act
        ServerInfo queryCondition = new ServerInfo();
        queryCondition.setAddTime(testDate);
        var pagedResult = serverManagementService.getByPageInfo(1, 2, queryCondition);
        
        // Assert
        assertNotNull(pagedResult, "Paged result should not be null");
        assertTrue(pagedResult.getTotalCount() >= 3, "Should have at least 3 servers");
        assertEquals(2, pagedResult.getPageSize(), "Page size should be 2");
        assertTrue(pagedResult.getData().size() <= 2, "Should return at most 2 servers per page");
    }
}
