package com.bqd.integratedtester.authoritypacket.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.base.rpc.authoritypacket.AuthorityPacketCommonInfoMapper;
import com.bqd.base.tools.FtpTool;
import com.bqd.base.tools.LlmTool;
import com.bqd.base.tools.XmlTool;
import com.bqd.integratedtester.authoritypacket.service.AuthorityPacketService;
import com.bqd.model.authoritypacket.GeneratePacketDto;
import com.bqd.model.authoritypacket.GeneratePacketLLMDto;
import com.bqd.model.authoritypacket.PacketCommonInfo;
import com.bqd.model.authoritypacket.XmlStructureQueryDto;
import com.bqd.model.common.XmlTreeDto;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.function.Consumer;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-01-02
 */
@Service
public class AuthorityPacketServiceImpl implements AuthorityPacketService {

    @Value("${path.authority-packet}")
    private String authorityPacketDir;

    @Autowired
    private AuthorityPacketCommonInfoMapper authorityPacketCommonInfoMapper;

    /**
     * 根据模板文件名，获取并解析xml，返回xml树结构
     *
     * @param xmlStructureQueryDto
     * @return
     */
    @Override
    public XmlTreeDto getXmlStructureTree(XmlStructureQueryDto xmlStructureQueryDto) {
        String templateXmlPath = authorityPacketDir + File.separator + xmlStructureQueryDto.getTemplateFolderName() + File.separator + xmlStructureQueryDto.getTemplateXmlName();
        String xmlStr = FileUtil.readString(templateXmlPath, StandardCharsets.UTF_8);
        Document document = XmlTool.readXmlFromStr(xmlStr);
        return XmlTool.xmlToStructureTree(document.getRootElement(), "");
    }

    /**
     * 生成报文包，并返回文件名
     *
     * @param generatePacketDto
     * @return
     */
    @Override
    public String generatePacket(GeneratePacketDto generatePacketDto, Consumer<File> consumer) {
        //生成报文包的文件夹名称
        String packetFolderName = StrUtil.replaceByCodePoint(generatePacketDto.getTemplateFolderName(), generatePacketDto.getTemplateFolderName().length() - generatePacketDto.getLastRandomDigits().length(), generatePacketDto.getTemplateFolderName().length(), generatePacketDto.getLastRandomDigits());
        //报文包文件夹的路径
        String packetFolderPath = authorityPacketDir + File.separator + packetFolderName;
        //创建报文包文件夹
        FileUtil.mkdir(packetFolderPath);
        //将模板文件夹中的文件复制并重命名到临时文件夹中
        String templateFolderPath = authorityPacketDir + File.separator + generatePacketDto.getTemplateFolderName();
        for (File file : FileUtil.ls(templateFolderPath)) {
            //获取无扩展名的文件名
            String nameNoExtension = file.getName().split("\\.")[0];
            //文件名替换后面 随机字符长度 个字符，得到新文件名
            String newName = StrUtil.replaceByCodePoint(file.getName(), nameNoExtension.length() - generatePacketDto.getLastRandomDigits().length(), nameNoExtension.length(), generatePacketDto.getLastRandomDigits());
            //复制到报文包文件夹中
            File copiedFile = FileUtil.copyFile(file, new File(packetFolderPath + File.separator + newName), StandardCopyOption.REPLACE_EXISTING);
            //若为模板xml文件，则将xml树结构转换成字符串，并写入到报文包中的xml文件中
            if (StrUtil.equals(file.getName(), generatePacketDto.getTemplateXmlName())) {
                Element element = XmlTool.structureTreeToXml(generatePacketDto.getXmlTreeDto());
                FileUtil.writeString(XmlUtil.format(element.asXML()), new File(packetFolderPath + File.separator + newName), StandardCharsets.UTF_8);
            }
            //额外处理
            consumer.accept(copiedFile);
        }

        //报文包文件夹压缩为zip文件
        String packetZipName = packetFolderName + ".zip";
        ZipUtil.zip(packetFolderPath, authorityPacketDir + File.separator + packetZipName);

        //删除报文包文件夹
        FileUtil.del(packetFolderPath);

        return packetZipName;
    }

    /**
     * 生成报文包 - 高法
     * @param generatePacketDto
     * @return
     */
    @Override
    public String generatePacketGf(GeneratePacketDto generatePacketDto) {
        return generatePacket(generatePacketDto, file -> {
            if (file.getName().startsWith("QCF") || file.getName().startsWith("QIF")) {
                Document document = XmlTool.readXmlFromStr(FileUtil.readUtf8String(file));
                List<Element> zjinfoList = document.getRootElement().elements("zjinfo");
                List<Element> wsinfoList = document.getRootElement().elements("wsinfo");

                //控制类
                if (!zjinfoList.isEmpty()) {
                    for (Element zjinfo: zjinfoList) {
                        Attribute bdhm = zjinfo.attribute("BDHM");
                        if (ObjectUtil.isNotNull(bdhm) && StrUtil.isNotBlank(bdhm.getText())) {
                            zjinfo.addAttribute("BDHM", generatePacketDto.getLastRandomDigits().substring(8));
                            FileUtil.writeString(document.asXML(), file, StandardCharsets.UTF_8);
                        }
                    }
                }
                if (!wsinfoList.isEmpty()) {
                    for (Element wsinfo: wsinfoList) {
                        Attribute bdhm = wsinfo.attribute("BDHM");
                        if (ObjectUtil.isNotNull(bdhm) && StrUtil.isNotBlank(bdhm.getText())) {
                            wsinfo.addAttribute("BDHM", generatePacketDto.getLastRandomDigits().substring(8));
                            FileUtil.writeString(document.asXML(), file, StandardCharsets.UTF_8);
                        }
                    }
                }
                //查询类
                if (!wsinfoList.isEmpty()) {
                    for (Element wsinfo: wsinfoList) {
                        Attribute wsbh = wsinfo.attribute("WSBH");
                        if (ObjectUtil.isNotNull(wsbh) && StrUtil.isNotBlank(wsbh.getText())) {
                            wsinfo.addAttribute("WSBH", generatePacketDto.getLastRandomDigits().substring(4));
                            FileUtil.writeString(document.asXML(), file, StandardCharsets.UTF_8);
                        }
                    }
                }
            }
        });
    }

    /**
     * 获取下载报文包的输入流
     *
     * @param packetZipName
     * @return
     */
    @Override
    public InputStream downloadZipPacket(String packetZipName) {
        String packetZipFilePath = authorityPacketDir + File.separator + packetZipName;
        return FileUtil.getInputStream(packetZipFilePath);
    }

    /**
     * 通过ftp上传报文包
     *
     * @param packetZipName
     * @param remotePath
     */
    @Override
    public void ftpUpload(String packetZipName, String remotePath) {
        String host = "**********";
        int port = 21;
        String username = "administrator";
        String password = "@dJDl&yQjG#WLck";

        String packetZipFilePath = authorityPacketDir + File.separator + packetZipName;
        BufferedInputStream inputStream = FileUtil.getInputStream(packetZipFilePath);
        boolean result = FtpTool.uploadFile(host, port, username, password, inputStream, remotePath, packetZipName);
        if (!result) {
            throw new CustomizedException("上传报文包失败");
        }
        try {
            inputStream.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 保存常用信息
     * @param packetCommonInfo
     */
    @Override
    public void saveCommonInfo(PacketCommonInfo packetCommonInfo) {
        packetCommonInfo.setId(IdUtil.fastSimpleUUID());
        authorityPacketCommonInfoMapper.insert(packetCommonInfo);
    }

    /**
     * 获取常用信息
     * @param packetCommonInfo
     * @return
     */
    @Override
    public List<PacketCommonInfo> getCommonInfo(PacketCommonInfo packetCommonInfo) {
        return authorityPacketCommonInfoMapper.selectByCondition(packetCommonInfo);
    }

    /**
     * 修改常用信息
     * @param packetCommonInfo
     */
    @Override
    public void editCommonInfo(PacketCommonInfo packetCommonInfo) {
        authorityPacketCommonInfoMapper.updateById(packetCommonInfo);
    }

    /**
     * 删除常用信息
     * @param id
     */
    @Override
    public void deleteCommonInfo(String id) {
        authorityPacketCommonInfoMapper.deleteById(id);
    }

    /**
     * 由llm生成xml报文
     * @param generatePacketLLMDto
     * @return
     */
    @Override
    public String llmGenerate(GeneratePacketLLMDto generatePacketLLMDto) {
        System.out.println(generatePacketLLMDto);
        //生成报文包的文件夹名称
        String packetFolderName = StrUtil.replaceByCodePoint(generatePacketLLMDto.getTemplateFolderName(), generatePacketLLMDto.getTemplateFolderName().length() - generatePacketLLMDto.getLastRandomDigits().length(), generatePacketLLMDto.getTemplateFolderName().length(), generatePacketLLMDto.getLastRandomDigits());
        //报文包文件夹的路径
        String packetFolderPath = authorityPacketDir + File.separator + packetFolderName;
        //创建报文包文件夹
        FileUtil.mkdir(packetFolderPath);
        //将模板文件夹中的文件复制并重命名到临时文件夹中
        String templateFolderPath = authorityPacketDir + File.separator + generatePacketLLMDto.getTemplateFolderName();
        for (File file : FileUtil.ls(templateFolderPath)) {
            //获取无扩展名的文件名
            String nameNoExtension = file.getName().split("\\.")[0];
            //文件名替换后面 随机字符长度 个字符，得到新文件名
            String newName = StrUtil.replaceByCodePoint(file.getName(), nameNoExtension.length() - generatePacketLLMDto.getLastRandomDigits().length(), nameNoExtension.length(), generatePacketLLMDto.getLastRandomDigits());
            //复制到报文包文件夹中
            File copiedFile = FileUtil.copyFile(file, new File(packetFolderPath + File.separator + newName), StandardCopyOption.REPLACE_EXISTING);
            //若为模板xml文件，则将xml树结构转换成字符串，并写入到报文包中的xml文件中
            if (StrUtil.equals(file.getName(), generatePacketLLMDto.getTemplateXmlName())) {
                String content = "你是一位专门处理XML文档的助手，任务是根据提供的JSON数组自动识别并替换XML文档中的特定元素或属性值。\n" +
                        "### 任务：\n" +
                        "- 分析给出的XML文本，理解其结构。\n" +
                        "- 解析提供的JSON数组（格式如：[{key: \"exampleKey\", value: \"exampleValue\"}, ...]）。\n" +
                        "- 在XML文档中找到与JSON数组中\"key\"字段匹配的元素或属性名称，并用对应的\"value\"字段值进行替换。\n" +
                        "- 确保替换后的XML保持正确的语法和格式。\n" +
                        "- **仅输出最终修改后的XML文档，不包括任何额外的文字或说明。**\n" +
                        "\n" +
                        "### 输入数据：\n" +
                        "#### XML文档:\n" +
                        FileUtil.readUtf8String(authorityPacketDir + File.separator + generatePacketLLMDto.getTemplateFolderName() + File.separator + generatePacketLLMDto.getTemplateXmlName()) + "\n" +
                        "\n" +
                        "#### JSON数组:\n" +
                        JSONUtil.toJsonStr(generatePacketLLMDto.getAttributeList()) + "\n" +
                        "\n" +
                        "请根据上述指示处理XML文档，并仅提供替换完成后的XML文本。";
                String resp = LlmTool.chat(content);
                JSONObject respJson = JSONUtil.parseObj(resp);
                String xmlStr = new String(respJson.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getStr("content").getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
                Document document = XmlTool.readXmlFromStr(xmlStr);
                FileUtil.writeString(XmlUtil.format(document.asXML()), new File(packetFolderPath + File.separator + newName), StandardCharsets.UTF_8);
            }
        }

        //报文包文件夹压缩为zip文件
        String packetZipName = packetFolderName + ".zip";
        ZipUtil.zip(packetFolderPath, authorityPacketDir + File.separator + packetZipName);

        //删除报文包文件夹
        FileUtil.del(packetFolderPath);


        return packetZipName;
    }

}
