spring:
  cloud:
    nacos:
      discovery:
        namespace: prod

path:
  transaction-chain:
    css-code: /home/<USER>/CodeFromQDB/efs #柜面代码
    h5-wisdom-nets: /home/<USER>/CodeFromQDB/h5-wisdom-nets-master/src/pages #智慧网点前端
    wisdom-integrated-service: /home/<USER>/CodeFromQDB/ifp_integrated_service #智慧网点前置服务

  xhx-daily-report:
    template-path: "/home/<USER>/test-center/service-integrated-tester/xhx-daily-report/测试执行日报导出模板.xlsx"
    output-dir: "/home/<USER>/test-center/service-integrated-tester/xhx-daily-report"

  authority-packet: "/home/<USER>/test-center/service-integrated-tester/authority-packet"

  loan-file-generate: "/home/<USER>/test-center/service-integrated-tester/loan-file-generate"

git-repo:
  local-dir: /home/<USER>/CodeFromQDB