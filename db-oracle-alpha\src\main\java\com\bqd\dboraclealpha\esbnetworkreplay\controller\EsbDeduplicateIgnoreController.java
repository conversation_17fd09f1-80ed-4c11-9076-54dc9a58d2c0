package com.bqd.dboraclealpha.esbnetworkreplay.controller;

import com.bqd.dboraclealpha.esbnetworkreplay.mapper.EsbDeduplicateIgnoreMapper;
import com.bqd.model.esbnetworkreplay.EsbDeduplicateIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-12-16
 */
@RestController
@RequestMapping("/enr/esbDeduplicateIgnore")
public class EsbDeduplicateIgnoreController {

    @Autowired
    private EsbDeduplicateIgnoreMapper esbDeduplicateIgnoreMapper;

    @GetMapping("/selectByInterfaceId")
    public EsbDeduplicateIgnore selectByInterfaceId(@RequestParam String interfaceId) {
        return esbDeduplicateIgnoreMapper.selectByInterfaceId(interfaceId);
    }

    @PostMapping("/insert")
    public void insert(@RequestBody EsbDeduplicateIgnore esbDeduplicateIgnore) {
        esbDeduplicateIgnoreMapper.insert(esbDeduplicateIgnore);
    }

    @PostMapping("/updateByInterfaceId")
    public void updateByInterfaceId(@RequestBody EsbDeduplicateIgnore esbDeduplicateIgnore) {
        esbDeduplicateIgnoreMapper.updateByInterfaceId(esbDeduplicateIgnore);
    }

}
