package com.bqd.model.esbnetworkreplay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * @Author: wangzi<PERSON>i
 * @CreateTime: 2024-07-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsbRecordInfo {
    private String recordId;
    private String host;
    private String interfaceId;
    private Long requestTime;
    private Long responseTime;
    private String svcCorrId;
    private String channelId;
    private String transCde;
    private String csmrId;
    private String versionNumber;
    private String esbRespMsg;
    private String esbRespCode;
    private String providerId;
}
