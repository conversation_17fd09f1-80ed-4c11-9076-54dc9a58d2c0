package com.bqd.servicellm;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@ComponentScan(basePackages = {"com.bqd.servicellm", "com.bqd.base"})
@EnableFeignClients(basePackages = {"com.bqd.base.rpc"})
@EnableAsync
public class ServiceLlmApplication {

    public static void main(String[] args) {
        SpringApplication.run(ServiceLlmApplication.class, args);
    }

}
