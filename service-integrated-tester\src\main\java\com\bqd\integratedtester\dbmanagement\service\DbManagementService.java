package com.bqd.integratedtester.dbmanagement.service;

import com.bqd.model.dbmanagement.DbConnectionInfo;

import java.util.List;

public interface DbManagementService {
    List<DbConnectionInfo> list(DbConnectionInfo dbConnectionInfo);

    void addInfo(DbConnectionInfo dbConnectionInfo);

    void updateInfo(DbConnectionInfo dbConnectionInfo);

    void deleteInfo(String id);

    void testDBConnection(String id);

    void testDBConnection(String dbType, String url, String username, String password);
}
