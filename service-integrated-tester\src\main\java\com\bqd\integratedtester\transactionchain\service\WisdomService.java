package com.bqd.integratedtester.transactionchain.service;

import com.bqd.model.transactionchain.WisdomEsbSearchDto;
import com.bqd.model.transactionchain.WisdomIntegrateServiceSearchDto;
import com.bqd.model.transactionchain.WisdomServiceSearchDto;
import com.bqd.model.transactionchain.WisdomTradeNameSearchDto;

import java.util.List;

public interface WisdomService {
    void parseFront();

    void parseIfpIntegratedService();

    List<WisdomTradeNameSearchDto> searchTradeName(String tradeName);

    List<WisdomIntegrateServiceSearchDto> searchIntegrateService(String mvcId, String description);

    List<WisdomServiceSearchDto> searchService(String serviceGroup, String description);

    List<WisdomEsbSearchDto> searchEsb(String interfaceId);
}
