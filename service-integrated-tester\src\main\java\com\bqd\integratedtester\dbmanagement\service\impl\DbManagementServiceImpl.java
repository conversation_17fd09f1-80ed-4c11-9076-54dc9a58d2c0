package com.bqd.integratedtester.dbmanagement.service.impl;

import cn.hutool.core.util.IdUtil;
import com.bqd.base.exception.CustomizedException;
import com.bqd.base.rpc.dbinfo.DbConnectionInfoMapper;
import com.bqd.base.tools.DbTool;
import com.bqd.integratedtester.dbmanagement.service.DbManagementService;
import com.bqd.model.dbmanagement.DbConnectionInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-04-11
 */
@Service
public class DbManagementServiceImpl implements DbManagementService {

    @Autowired
    private DbConnectionInfoMapper dbConnectionInfoMapper;

    /**
     * 根据条件查询数据库连接信息
     *
     * @param dbConnectionInfo
     * @return
     */
    @Override
    public List<DbConnectionInfo> list(DbConnectionInfo dbConnectionInfo) {
        return dbConnectionInfoMapper.selectByCondition(dbConnectionInfo);
    }

    /**
     * 添加数据库连接信息
     *
     * @param dbConnectionInfo
     */
    @Override
    public void addInfo(DbConnectionInfo dbConnectionInfo) {
        dbConnectionInfo.setId(IdUtil.fastSimpleUUID());
        dbConnectionInfoMapper.insert(dbConnectionInfo);
    }

    /**
     * 修改数据库连接信息
     *
     * @param dbConnectionInfo
     */
    @Override
    public void updateInfo(DbConnectionInfo dbConnectionInfo) {
        dbConnectionInfoMapper.updateById(dbConnectionInfo);
    }

    /**
     * 删除数据库连接信息
     *
     * @param id
     */
    @Override
    public void deleteInfo(String id) {
        dbConnectionInfoMapper.deleteById(id);
    }

    /**
     * 测试数据库连接
     *
     * @param id
     */
    @Override
    public void testDBConnection(String id) {
        DbConnectionInfo dbConnectionInfo = new DbConnectionInfo();
        dbConnectionInfo.setId(id);
        dbConnectionInfo = dbConnectionInfoMapper.selectByCondition(dbConnectionInfo).get(0);
        testDBConnection(dbConnectionInfo.getDbType(), dbConnectionInfo.getUrl(), dbConnectionInfo.getUsername(), dbConnectionInfo.getPassword());
    }

    /**
     * 测试数据库连接
     *
     * @param dbType
     * @param url
     * @param username
     * @param password
     */
    @Override
    public void testDBConnection(String dbType, String url, String username, String password) {
        DbTool.loadDriver(dbType);
        try {
            Connection connection = DriverManager.getConnection(url, username, password);
            if (!connection.isValid(10)) {
                throw new RuntimeException("数据库连接失败");
            }
        } catch (Exception e) {
            throw new CustomizedException(e.getMessage());
        }
    }

}
