package com.bqd.model.esbnetworkreplay;

import lombok.Data;

/**
 * @Description: TODO
 * @Author: wang<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-02
 */
@Data
public class RecordDBEnv {
    /**
     * ID
     */
    private String id;

    /**
     * 环境名称
     */
    private String envName;

    /**
     * 数据库连接url
     */
    private String url;

    /**
     * 数据库用户名
     */
    private String userName;

    /**
     * 数据库密码
     */
    private String password;

    /**
     * 创建时间
     */
    private String createTime;
}
