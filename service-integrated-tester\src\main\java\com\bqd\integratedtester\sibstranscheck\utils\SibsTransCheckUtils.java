package com.bqd.integratedtester.sibstranscheck.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONNull;
import cn.hutool.json.JSONObject;
import cn.hutool.json.XML;
import com.bqd.integratedtester.sibstranscheck.service.impl.SibsXmlJsonCheckAbstract;
import net.sf.json.JSONArray;
import org.dom4j.Element;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-05-10
 */
public class SibsTransCheckUtils {

    /**
     * xml转json
     * @param xml
     * @param outTag
     * @return
     */
    public static JSONObject xmlToJsonObject(String xml, String outTag) {
        JSONObject jsonObject = XML.toJSONObject(xml, true);
        Object object = jsonObject.get(outTag);
        if (StrUtil.isEmpty(String.valueOf(object))) {
            return new JSONObject("{}");
        }
        return (JSONObject) object;
    }

    /**
     * 判断element的值是否等于val
     * @param element
     * @param val
     * @return
     */
    public static boolean valueIsEqual(Element element, String val) {
        if (ObjectUtil.isNull(element) || StrUtil.isBlank(element.getText())) {
            return StrUtil.isBlank(val);
        }
        return element.getText().equals(val);
    }

    /**
     * 判断两个json是否相等
     * @param convertedJson
     * @param json
     * @return
     */
    public static boolean jsonIsEqual(String convertedJson, String json, SibsXmlJsonCheckAbstract sibsXmlJsonCheck) {
        if ((StrUtil.isEmpty(convertedJson) || new JSONObject(convertedJson).isEmpty())
                && (StrUtil.isEmpty(json) || new JSONObject(json).isEmpty())) {
            return true;
        }
        convertedJson = convertedJson.replaceAll("\"null\"", "null");
        json = json.replaceAll("\"null\"", "null");
        net.sf.json.JSONObject convertedJsonObject = net.sf.json.JSONObject.fromObject(convertedJson);
        net.sf.json.JSONObject jsonObject = net.sf.json.JSONObject.fromObject(json);
        arrangeJson(convertedJsonObject);
        arrangeJson(jsonObject);
        if (!convertedJsonObject.equals(jsonObject)){
            sibsXmlJsonCheck.setErrMsg(sibsXmlJsonCheck.getErrMsg() + "<b>本地xml转json与ESB发出/得到json比对失败</b><br/>");
            sibsXmlJsonCheck.setErrMsg(sibsXmlJsonCheck.getErrMsg() + "<b>本地xml转json:</b><br/> " + convertedJsonObject.toString() + "<br/>");
            sibsXmlJsonCheck.setErrMsg(sibsXmlJsonCheck.getErrMsg() + "<b>ESB发出/得到json:</b><br/> " + jsonObject.toString() + "<br/>");
            return false;
        }
        return true;
    }

    /**
     * 自上而下将json对象的各个元素重新排序，采用将json对象排序后重新组装的方式
     * @param js
     */
    public static void arrangeJson(Object js) {
        if (js instanceof net.sf.json.JSONObject) {
            net.sf.json.JSONObject jsonObject = new net.sf.json.JSONObject();
            net.sf.json.JSONObject jsCopy = net.sf.json.JSONObject.fromObject(js.toString());    //将json对象复制一份，进行递归遍历取值
            Iterator i = jsCopy.entrySet().iterator();
            ArrayList<Object> arr = new ArrayList<Object>();
            while (i.hasNext()) {
                Map.Entry entry = (Map.Entry) i.next();
                arr.add(entry.getKey().toString());           //逐个取出子节点对象
                if (entry.getValue() instanceof JSONNull) {
                    entry.setValue("null");
                }
                //System.out.println(entry.getKey() + "    " + entry.getValue()+" "+jsCopy.get(entry.getKey()).getClass());
                ((net.sf.json.JSONObject) js).remove(entry.getKey().toString());         //清空旧的子元素
            }
            sortArr(arr);
            for (int n = 0; n < arr.size(); n++) {
                //System.out.println("arr="+arr.get(n));
                String key = arr.get(n).toString();
                if (jsCopy.get(key) instanceof net.sf.json.JSONObject || (jsCopy.get(key) instanceof JSONArray)) {
                    arrangeJson(jsCopy.get(key));         //递归调整json对象
                }
                ((net.sf.json.JSONObject) js).put(key, jsCopy.get(key));               //重新组装序列化的子元素
            }
        }

        if (js instanceof JSONArray) {
            JSONArray jsCopy = JSONArray.fromObject(js.toString());
            ArrayList<Object> arr = new ArrayList<Object>();
            for (int n = 0; n < jsCopy.size(); n++) {
                arr.add(jsCopy.get(n));
                ((JSONArray) js).remove(0);
            }
            sortArr(arr);
            for (int n = 0; n < arr.size(); n++) {
                //System.out.println("arr_"+n+arr.get(n));
                arrangeJson((Object) arr.get(n));
                ((JSONArray) js).add((Object) arr.get(n));
            }
        }
    }

    //将数组元素按照哈希码从小到大重新排序
    private static void sortArr(ArrayList<Object> arr) {
        int len = arr.size();
        int[] n = new int[len];
        ArrayList<Object> arrCopy = (ArrayList<Object>) arr.clone();
        Object[] obj = new Object[len];
        for (int i = 0; i < len; i++) {
            n[i] = arrCopy.get(i).hashCode();
            obj[i] = arrCopy.get(i);
            arr.remove(0);
        }
        for (int i = 0; i < len; i++) {
            for (int y = i + 1; y < len; y++) {
                if (n[i] < n[y]) {
                    int x = n[y];
                    n[y] = n[i];
                    n[i] = x;
                    Object s = obj[y];
                    obj[y] = obj[i];
                    obj[i] = s;
                }
            }
        }
        for (int i = 0; i < len; i++) {
            arr.add(obj[i]);
        }
    }

    /**
     * json对象转数组
     * @param jsonObject
     * @param keysToConvert
     * @return
     */
    public static void jsonObjectToArray(JSONObject jsonObject, Set<String> keysToConvert) {
        jsonObject.forEach((key, value) -> {
            // 检查键是否匹配正则表达式集合中的任何一个
            if (matchesAnyRegex(key, keysToConvert)) {
                if (value instanceof JSONObject) {
                    JSONArray jsonArray = new JSONArray();
                    jsonArray.add(value);
                    jsonObject.set(key, jsonArray); // 将值转换为 JSONArray
                }
            } else if (value instanceof JSONObject) {
                jsonObjectToArray((JSONObject) value, keysToConvert); // 递归处理嵌套的 JSONObject
            } else if (value instanceof JSONArray) {
                JSONArray array = (JSONArray) value;
                for (int i = 0; i < array.size(); i++) {
                    Object element = array.get(i);
                    if (element instanceof JSONObject) {
                        jsonObjectToArray((JSONObject) element, keysToConvert); // 递归处理嵌套的 JSONObject
                    }
                }
            }
        });
    }

    private static boolean matchesAnyRegex(String key, Set<String> regexSet) {
        for (String regex : regexSet) {
            if (Pattern.matches(regex, key)) {
                return true;
            }
        }
        return false;
    }

    public static void xmlRemoveEmptyTag(Element rootElement) {
        if (rootElement.elements().isEmpty()) {
            return;
        }
        for (Element element : rootElement.elements()) {
            if (element.elements().isEmpty()) {
                if (StrUtil.isBlank(element.getText())) {
                    if (element.getName().equals("svcHdr") || element.getName().equals("appHdr") || element.getName().equals("appBody")){
                        continue;
                    }
                    rootElement.remove(element);
                }
            }
            xmlRemoveEmptyTag(element);
        }
    }

    //public static void jsonRemoveEmptyTag(JSONObject jsonObject) {
    //    List<String> keysToRemove = new ArrayList<>();
    //
    //    jsonObject.forEach((key, value) -> {
    //        if (value instanceof JSONObject) {
    //            jsonRemoveEmptyTag((JSONObject) value); // 递归处理嵌套的 JSONObject
    //            if (((JSONObject) value).isEmpty()) {
    //                keysToRemove.add(key); // 添加需要删除的键到临时列表
    //            }
    //        } else if (value instanceof JSONArray) {
    //            JSONArray array = (JSONArray) value;
    //            for (int i = array.size() - 1; i >= 0; i--) {
    //                Object element = array.get(i);
    //                if (element instanceof JSONObject) {
    //                    jsonRemoveEmptyTag((JSONObject) element); // 递归处理嵌套的 JSONObject
    //                    if (((JSONObject) element).isEmpty()) {
    //                        array.remove(i); // 如果子 JSONObject 为空，移除当前元素
    //                    }
    //                } else if (element instanceof String && ((String) element).isEmpty()) {
    //                    array.remove(i); // 如果数组中的字符串为空，移除当前元素
    //                }
    //            }
    //            if (array.isEmpty()) {
    //                keysToRemove.add(key); // 添加需要删除的键到临时列表
    //            }
    //        } else if (value instanceof String && ((String) value).isEmpty()) {
    //            keysToRemove.add(key); // 添加需要删除的键到临时列表
    //        }
    //    });
    //
    //    keysToRemove.forEach(jsonObject::remove); // 批量删除空键值对
    //}

    //public static void jsonRemoveKeys(JSONObject jsonObject, Set<String> keysToRemove) {
    //    jsonObject.forEach((key, value) -> {
    //        if (value instanceof JSONObject) {
    //            jsonRemoveKeys((JSONObject) value, keysToRemove); // 递归处理嵌套的 JSONObject
    //        } else if (value instanceof JSONArray) {
    //            JSONArray array = (JSONArray) value;
    //            for (Object element : array) {
    //                if (element instanceof JSONObject) {
    //                    jsonRemoveKeys((JSONObject) element, keysToRemove); // 递归处理嵌套的 JSONObject
    //                }
    //            }
    //        }
    //    });
    //
    //    // 移除当前 JSONObject 中包含在 localKeysToRemove 中的键
    //    keysToRemove.forEach(jsonObject::remove);
    //}

    public static void removeFields(JSONObject jsonObject, Set<String> fieldsToRemove) {
        Iterator<String> keys = jsonObject.keySet().iterator();

        while (keys.hasNext()) {
            String key = keys.next();

            if (fieldsToRemove.contains(key)) {
                keys.remove();
            } else {
                Object value = jsonObject.get(key);

                if (value instanceof JSONObject) {
                    removeFields((JSONObject) value, fieldsToRemove);
                } else if (value instanceof JSONArray) {
                    removeFields((JSONArray) value, fieldsToRemove);
                }
            }
        }
    }

    public static void removeFields(JSONArray jsonArray, Set<String> fieldsToRemove) {
        for (int i = 0; i < jsonArray.size(); i++) {
            Object value = jsonArray.get(i);

            if (value instanceof JSONObject) {
                removeFields((JSONObject) value, fieldsToRemove);
            } else if (value instanceof JSONArray) {
                removeFields((JSONArray) value, fieldsToRemove);
            }
        }
    }

    public static JSONObject cleanJson(JSONObject jsonObject) {
        JSONObject result = new JSONObject();

        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);

            if (value instanceof JSONObject) {
                JSONObject cleanedObject = cleanJson((JSONObject) value);
                if (!cleanedObject.isEmpty()) {
                    result.put(key, cleanedObject);
                }
            } else if (value instanceof cn.hutool.json.JSONArray) {
                cn.hutool.json.JSONArray cleanedArray = cleanJsonArray((cn.hutool.json.JSONArray) value);
                if (!cleanedArray.isEmpty()) {
                    result.put(key, cleanedArray);
                }
            } else if (value != null && !value.equals("") && !value.equals("{}") && !value.equals("[]")) {
                result.put(key, value);
            }
        }

        return result;
    }

    public static cn.hutool.json.JSONArray cleanJsonArray(cn.hutool.json.JSONArray jsonArray) {
        cn.hutool.json.JSONArray result = new cn.hutool.json.JSONArray();

        for (Object item : jsonArray) {
            if (item instanceof JSONObject) {
                JSONObject cleanedObject = cleanJson((JSONObject) item);
                if (!cleanedObject.isEmpty()) {
                    result.add(cleanedObject);
                }
            } else if (item instanceof cn.hutool.json.JSONArray) {
                cn.hutool.json.JSONArray cleanedArray = cleanJsonArray((cn.hutool.json.JSONArray) item);
                if (!cleanedArray.isEmpty()) {
                    result.add(cleanedArray);
                }
            } else if (item != null && !item.equals("") && !item.equals("{}") && !item.equals("[]")) {
                result.add(item);
            }
        }

        return result;
    }
}
