package com.bqd.integratedtester.transactionchain.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.bqd.base.rpc.common.OracleAlphaUtilMapper;
import com.bqd.base.rpc.transactionchain.TcCssEsbMapper;
import com.bqd.base.rpc.transactionchain.TcCssMenuMapper;
import com.bqd.base.tools.EsbTool;
import com.bqd.base.tools.XmlTool;
import com.bqd.integratedtester.transactionchain.service.CustomerServiceSystemService;
import com.bqd.model.transactionchain.SearchEsbResultDto;
import com.bqd.model.transactionchain.SearchMenuResultDto;
import com.bqd.model.transactionchain.TcCssEsb;
import com.bqd.model.transactionchain.TcCssMenu;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.Node;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-13
 */
@Service
public class CustomerServiceSystemServiceImpl implements CustomerServiceSystemService {

    @Value("${path.transaction-chain.css-code}")
    private String efsPath;

    @Autowired
    private OracleAlphaUtilMapper oracleAlphaUtilMapper;
    @Autowired
    private TcCssMenuMapper tcCssMenuMapper;
    @Autowired
    private TcCssEsbMapper tcCssEsbMapper;

    /**
     * 解析客户服务系统
     */
    @Override
    public void parse() {
        oracleAlphaUtilMapper.truncateTable("TC_CSS_MENU");
        oracleAlphaUtilMapper.truncateTable("TC_CSS_ESB");

        //三个xml文件路径，xml用来关联交易名、交易代码、代码路径（ESB接口）
        String xmlDir = efsPath + File.separator + "platform" + File.separator + "menu";
        String custmenuXml = xmlDir + File.separator + "custmenu.xml";
        String menuXml = xmlDir + File.separator + "menu.xml";
        String taskXml = xmlDir + File.separator + "task.xml";
        String[] xmlFilePathArr = new String[]{custmenuXml, menuXml, taskXml};

        //遍历3个xml文件
        for (String xmlFilePath : xmlFilePathArr) {
            //读取xml文件
            String currXml = FileUtil.readString(xmlFilePath, StandardCharsets.UTF_8);
            Document document = XmlTool.readXmlFromStr(currXml);
            //选择所有item节点
            List<Node> itemNodeList = document.selectNodes("//item");
            //遍历item节点
            for (Node itemNode : itemNodeList) {
                //定义id
                String id = IdUtil.fastSimpleUUID();

                Element itemElement = (Element) itemNode;
                //获取tid，为交易代码
                String tid = itemElement.attributeValue("tid");
                //获取text，为交易名称
                String itemText = itemElement.attributeValue("text");
                //获取url，为代码路径
                String url = itemElement.attributeValue("url");

                //获取当前item的menu路径
                String menuPath = "";
                Element parentElement = itemElement;
                while (!parentElement.getParent().isRootElement()) {
                    parentElement = parentElement.getParent();
                    String menuText = parentElement.attributeValue("text");
                    menuPath = StrUtil.isBlank(menuPath) ? menuText : menuText + "-" + menuPath;
                }

                if (url.startsWith("efs/")) {
                    url = url.substring(4);
                }
                //解析完xml插入到数据库
                tcCssMenuMapper.insert(new TcCssMenu(id, tid, menuPath, itemText, url));

                //根据url找到代码路径，解析调用的ESB
                String codePath = efsPath + File.separator + url + File.separator + "page";
                if (!FileUtil.exist(codePath) || !FileUtil.isDirectory(codePath)) {
                    continue;
                }
                //遍历所有.java文件
                for (File file : FileUtil.ls(codePath)) {
                    if (!file.getName().endsWith(".java")) {
                        continue;
                    }
                    String fileContent = FileUtil.readString(file, StandardCharsets.UTF_8);
                    String[] callServiceArr = StrUtil.subBetweenAll(fileContent, "callService", ";");
                    for (String callService : callServiceArr) {
                        String intfId = EsbTool.extractInterfaceIdFromUrl(callService);
                        tcCssEsbMapper.insert(new TcCssEsb(id, intfId));
                    }
                }
            }
        }
    }

    /**
     * 搜索menuPath/itemText
     *
     * @param tcCssMenu
     * @return
     */
    @Override
    public List<SearchMenuResultDto> searchMenu(TcCssMenu tcCssMenu) {
        List<SearchMenuResultDto> searchMenuResultDtoList = new ArrayList<>();
        List<TcCssMenu> tcCssMenuList = tcCssMenuMapper.selectByCondition(tcCssMenu);
        for (TcCssMenu each : tcCssMenuList) {
            SearchMenuResultDto searchMenuResultDto = BeanUtil.copyProperties(each, SearchMenuResultDto.class);
            List<TcCssEsb> tcCssEsbList = tcCssEsbMapper.selectByCondition(new TcCssEsb(each.getId(), null));
            searchMenuResultDto.setTcCssEsbList(tcCssEsbList);
            searchMenuResultDtoList.add(searchMenuResultDto);
        }
        return searchMenuResultDtoList;
    }

    @Override
    public List<SearchEsbResultDto> searchEsb(String interfaceId) {
        List<SearchEsbResultDto> searchEsbResultDtoList = new ArrayList<>();
        List<TcCssEsb> tcCssEsbList = tcCssEsbMapper.selectByCondition(new TcCssEsb(null, interfaceId));
        for (TcCssEsb each : tcCssEsbList) {
            SearchEsbResultDto searchEsbResultDto = BeanUtil.copyProperties(each, SearchEsbResultDto.class);
            List<TcCssMenu> tcCssMenuList = tcCssMenuMapper.selectByCondition(new TcCssMenu(each.getId(), null, null, null, null));
            searchEsbResultDto.setTcCssMenuList(tcCssMenuList);
           searchEsbResultDtoList.add(searchEsbResultDto);
        }
        return searchEsbResultDtoList;
    }
}
