package com.bqd.dboraclealpha.esbnetworkreplay.mapper;

import com.bqd.model.esbnetworkreplay.EsbDeduplicateIgnoreGlobal;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface EsbDeduplicateIgnoreGlobalMapper {
    void insert(EsbDeduplicateIgnoreGlobal esbDeduplicateIgnoreGlobal);

    List<EsbDeduplicateIgnoreGlobal> selectByCondition(EsbDeduplicateIgnoreGlobal esbDeduplicateIgnoreGlobal);

    void deleteByCondition(EsbDeduplicateIgnoreGlobal esbDeduplicateIgnoreGlobal);
}
