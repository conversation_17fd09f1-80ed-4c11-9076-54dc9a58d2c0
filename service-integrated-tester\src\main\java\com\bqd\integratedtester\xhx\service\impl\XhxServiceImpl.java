package com.bqd.integratedtester.xhx.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.bqd.base.rpc.common.OracleBetaUtilMapper;
import com.bqd.base.rpc.xhx.XhxRbGrzxqkMapper;
import com.bqd.base.rpc.xhx.XhxRbQxztqkMapper;
import com.bqd.base.rpc.xhx.XhxRbZxztqkMapper;
import com.bqd.integratedtester.xhx.service.XhxService;
import com.bqd.model.xhxrb.XhxRbGrzxqk;
import com.bqd.model.xhxrb.XhxRbQxztqk;
import com.bqd.model.xhxrb.XhxRbZxztqk;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-11-25
 */
@Service
public class XhxServiceImpl implements XhxService {

    @Value("${path.xhx-daily-report.template-path}")
    private String templatePath;
    @Value("${path.xhx-daily-report.output-dir}")
    private String outputDir;

    @Autowired
    private XhxRbZxztqkMapper xhxRbZxztqkMapper;
    @Autowired
    private XhxRbGrzxqkMapper xhxRbGrzxqkMapper;
    @Autowired
    private XhxRbQxztqkMapper xhxRbQxztqkMapper;
    @Autowired
    private OracleBetaUtilMapper oracleBetaUtilMapper;

    @Override
    public void runProcedure(String startTime, String endTime, String roundName, String systemPrefix) {
        oracleBetaUtilMapper.truncateTable("T_B_XHX_RB_ZXZTQK");
        updateZxztqkTable(startTime, endTime, roundName, systemPrefix);

        oracleBetaUtilMapper.truncateTable("T_B_XHX_RB_GRZXQK");
        updateGrzxqkTable(startTime, endTime);

        oracleBetaUtilMapper.truncateTable("T_B_XHX_RB_QXZTQK");
        updateQxztqkTable(roundName);
    }

    private void updateZxztqkTable(String startTime, String endTime, String roundName, String systemPrefix) {
        xhxRbZxztqkMapper.pcdInsertNames();
        xhxRbZxztqkMapper.pcdInsertTestsetTotal();

        xhxRbZxztqkMapper.pcdUpdateCaseCount();
        xhxRbZxztqkMapper.pcdUpdateExecuteCount();
        xhxRbZxztqkMapper.pcdUpdatePassedCount();
        xhxRbZxztqkMapper.pcdUpdateFailedCount();
        xhxRbZxztqkMapper.pcdUpdateBlockCount();
        xhxRbZxztqkMapper.pcdUpdateExecutingCount();
        xhxRbZxztqkMapper.pcdUpdateCancelCount();
        xhxRbZxztqkMapper.pcdUpdateTodoCount();

        xhxRbZxztqkMapper.pcdUpdateExecuteRate();
        xhxRbZxztqkMapper.pcdUpdatePlanProgress(roundName);
        xhxRbZxztqkMapper.pcdUpdateProgressBias();
        xhxRbZxztqkMapper.pcdUpdatePassRate();
        xhxRbZxztqkMapper.pcdUpdateValidBugCount(roundName, systemPrefix);

        xhxRbZxztqkMapper.pcdUpdateDayExecuteCount(startTime, endTime);
        xhxRbZxztqkMapper.pcdUpdateDayPassedCount(startTime, endTime);
        xhxRbZxztqkMapper.pcdUpdateDayFailedCount(startTime, endTime);

        xhxRbZxztqkMapper.pcdUpdateTotalBugRate();
    }

    private void updateGrzxqkTable(String startTime, String endTime) {
        xhxRbGrzxqkMapper.pcdInsertGrpTester();

        xhxRbGrzxqkMapper.pcdUpdateExecuteCount();
        xhxRbGrzxqkMapper.pcdUpdatePassedCount();
        xhxRbGrzxqkMapper.pcdUpdateFailedCount();
        xhxRbGrzxqkMapper.pcdUpdateBlockCount();
        xhxRbGrzxqkMapper.pcdUpdateExecutingCount();
        xhxRbGrzxqkMapper.pcdUpdateCancelCount();

        xhxRbGrzxqkMapper.pcdUpdateDayExecuteCount(startTime, endTime);
        xhxRbGrzxqkMapper.pcdUpdateDayPassedCount(startTime, endTime);
        xhxRbGrzxqkMapper.pcdUpdateDayFailedCount(startTime, endTime);
    }

    private void updateQxztqkTable(String roundName) {
        xhxRbQxztqkMapper.pcdInsertSystemName(roundName);

        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "打开", "OPEN_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "已分派", "ASSIGNED_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "修复中", "REPAIRING_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "已修复", "REPAIRED_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "重新打开", "REOPEN_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "待需求确认", "DXQQR_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "待确认-新核心", "DQRXHX_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "待修改", "DXG_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "挂起", "SUSPEND_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "待仲裁", "DZC_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "提起测试仲裁", "TQCSZC_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "提起PMO仲裁", "TQPMOZC_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "新建", "NEW_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "拒绝", "REJECT_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "待复测", "DFC_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "关闭", "CLOSED_COUNT");
        xhxRbQxztqkMapper.pcdUpdateCount(roundName, "拒绝确认", "JJQR_COUNT");

        xhxRbQxztqkMapper.pcdUpdateTotalCount(roundName);
    }

    @Override
    public Workbook dailyReport() {
        String outputFile = outputDir + File.separator + DateUtil.now().split(" ")[0] + ".xlsx";
        FileUtil.copy(templatePath, outputFile, true);
        try {
            XSSFWorkbook workbook = new XSSFWorkbook(outputFile);
            //获取【测试执行整体情况】sheet
            XSSFSheet sheet0 = workbook.getSheetAt(0);
            //从数据库中查询数据
            List<XhxRbZxztqk> xhxRbZxztqkList = xhxRbZxztqkMapper.selectAllDesc();
            //设置开始行
            int startRow = 3;
            //插入行
            sheet0.shiftRows(startRow, sheet0.getLastRowNum(), xhxRbZxztqkList.size());
            //遍历数据
            for (int i = 0; i < xhxRbZxztqkList.size(); i++) {
                XhxRbZxztqk xhxRbZxztqk = xhxRbZxztqkList.get(i);
                //创建行
                XSSFRow row = sheet0.createRow(startRow + i);
                //单元格赋值
                insertValueZxztqk(row, xhxRbZxztqk, workbook);
            }

            //获取【测试人员个人执行情况】sheet
            XSSFSheet sheet1 = workbook.getSheetAt(1);
            //从数据库中查数据
            List<XhxRbGrzxqk> xhxRbGrzxqkList = xhxRbGrzxqkMapper.selectAll();
            //设置开始行
            startRow = 2;
            //遍历数据
            for (int i = 0; i < xhxRbGrzxqkList.size(); i++) {
                XhxRbGrzxqk xhxRbGrzxqk = xhxRbGrzxqkList.get(i);
                XSSFRow row = sheet1.createRow(startRow + i);
                insertValueGrzxqk(row, xhxRbGrzxqk, workbook);
            }

            //获取【缺陷整体情况】的sheet
            XSSFSheet sheet2 = workbook.getSheetAt(2);
            //从数据库中查询数据
            List<XhxRbQxztqk> xhxRbQxztqkList = xhxRbQxztqkMapper.selectAll();
            //设置开始行
            startRow = 1;
            //插入行
            sheet2.shiftRows(startRow, sheet2.getLastRowNum(), xhxRbQxztqkList.size());
            //遍历数据
            for (int i = 0; i < xhxRbQxztqkList.size(); i++) {
                XhxRbQxztqk xhxRbQxztqk = xhxRbQxztqkList.get(i);
                //创建行
                XSSFRow row = sheet2.createRow(startRow + i);
                //单元格赋值
                insertValueQxztqk(row, xhxRbQxztqk, workbook);
            }
            return workbook;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void insertValueZxztqk(XSSFRow row, XhxRbZxztqk xhxRbZxztqk, Workbook workbook) {
        CellStyle cellStyle1 = workbook.createCellStyle();
        cellStyle1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle1.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle1.setBorderTop(BorderStyle.THIN);      // 上边框
        cellStyle1.setBorderBottom(BorderStyle.THIN);   // 下边框
        cellStyle1.setBorderLeft(BorderStyle.THIN);     // 左边框
        cellStyle1.setBorderRight(BorderStyle.THIN);

        CellStyle cellStyle2 = workbook.createCellStyle();
        cellStyle2.setBorderTop(BorderStyle.THIN);      // 上边框
        cellStyle2.setBorderBottom(BorderStyle.THIN);   // 下边框
        cellStyle2.setBorderLeft(BorderStyle.THIN);     // 左边框
        cellStyle2.setBorderRight(BorderStyle.THIN);

        CellStyle cellStyle3 = workbook.createCellStyle();
        DataFormat dataFormat = workbook.createDataFormat();
        cellStyle3.setBorderTop(BorderStyle.THIN);      // 上边框
        cellStyle3.setBorderBottom(BorderStyle.THIN);   // 下边框
        cellStyle3.setBorderLeft(BorderStyle.THIN);     // 左边框
        cellStyle3.setBorderRight(BorderStyle.THIN);
        cellStyle3.setDataFormat(dataFormat.getFormat("0.00%"));
        //系统
        XSSFCell cell1 = row.createCell(1);
        cell1.setCellValue(xhxRbZxztqk.getSystemName());
        cell1.setCellStyle(cellStyle1);
        //一级功能
        XSSFCell cell2 = row.createCell(2);
        cell2.setCellValue(xhxRbZxztqk.getTestsetName());
        cell2.setCellStyle(cellStyle2);
        //当前轮次计划执行案例数
        XSSFCell cell3 = row.createCell(3);
        cell3.setCellValue(xhxRbZxztqk.getCaseCount());
        cell3.setCellStyle(cellStyle2);
        //已执行
        XSSFCell cell4 = row.createCell(4);
        cell4.setCellValue(xhxRbZxztqk.getExecuteCount());
        cell4.setCellStyle(cellStyle2);
        //通过
        XSSFCell cell5 = row.createCell(5);
        cell5.setCellValue(xhxRbZxztqk.getPassedCount());
        cell5.setCellStyle(cellStyle2);
        //失败
        XSSFCell cell6 = row.createCell(6);
        cell6.setCellValue(xhxRbZxztqk.getFailedCount());
        cell6.setCellStyle(cellStyle2);
        //阻塞
        XSSFCell cell7 = row.createCell(7);
        cell7.setCellValue(xhxRbZxztqk.getBlockCount());
        cell7.setCellStyle(cellStyle2);
        //执行中
        XSSFCell cell8 = row.createCell(8);
        cell8.setCellValue(xhxRbZxztqk.getExecutingCount());
        cell8.setCellStyle(cellStyle2);
        //已取消
        XSSFCell cell9 = row.createCell(9);
        cell9.setCellValue(xhxRbZxztqk.getCancelCount());
        cell9.setCellStyle(cellStyle2);
        //未开始
        XSSFCell cell10 = row.createCell(10);
        cell10.setCellValue(xhxRbZxztqk.getTodoCount());
        cell10.setCellStyle(cellStyle2);
        //总体执行率
        XSSFCell cell11 = row.createCell(11);
        cell11.setCellValue(xhxRbZxztqk.getExecuteRate());
        cell11.setCellStyle(cellStyle3);
        //计划进度
        XSSFCell cell12 = row.createCell(12);
        cell12.setCellValue(xhxRbZxztqk.getPlanProgress());
        cell12.setCellStyle(cellStyle3);
        //总体进度偏差
        XSSFCell cell13 = row.createCell(13);
        cell13.setCellValue(xhxRbZxztqk.getProgressBias());
        cell13.setCellStyle(cellStyle3);
        //总体通过率
        XSSFCell cell14 = row.createCell(14);
        cell14.setCellValue(xhxRbZxztqk.getPassRate());
        cell14.setCellStyle(cellStyle3);
        //有效缺陷数
        XSSFCell cell15 = row.createCell(15);
        cell15.setCellValue(ObjectUtil.isNull(xhxRbZxztqk.getValidBugCount()) ? 0 : xhxRbZxztqk.getValidBugCount());
        cell15.setCellStyle(cellStyle2);
        //总体缺陷率
        XSSFCell cell16 = row.createCell(16);
        cell16.setCellValue(ObjectUtil.isNull(xhxRbZxztqk.getTotalBugRate()) ? 0 : xhxRbZxztqk.getTotalBugRate());
        cell16.setCellStyle(cellStyle3);
        //当日已执行
        XSSFCell cell17 = row.createCell(17);
        cell17.setCellValue(xhxRbZxztqk.getDayExecuteCount());
        cell17.setCellStyle(cellStyle2);
        //当日通过
        XSSFCell cell18 = row.createCell(18);
        cell18.setCellValue(xhxRbZxztqk.getDayPassedCount());
        cell18.setCellStyle(cellStyle2);
        //当日失败
        XSSFCell cell19 = row.createCell(19);
        cell19.setCellValue(xhxRbZxztqk.getDayFailedCount());
        cell19.setCellStyle(cellStyle2);
    }

    private void insertValueGrzxqk(Row row, XhxRbGrzxqk xhxRbGrzxqk, Workbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderTop(BorderStyle.THIN);      // 上边框
        cellStyle.setBorderBottom(BorderStyle.THIN);   // 下边框
        cellStyle.setBorderLeft(BorderStyle.THIN);     // 左边框
        cellStyle.setBorderRight(BorderStyle.THIN);
        //组别
        Cell cell0 = row.createCell(0);
        cell0.setCellValue(xhxRbGrzxqk.getGrp());
        cell0.setCellStyle(cellStyle);
        //测试人员
        Cell cell1 = row.createCell(1);
        cell1.setCellValue(xhxRbGrzxqk.getTester());
        cell1.setCellStyle(cellStyle);
        //已执行
        Cell cell2 = row.createCell(2);
        cell2.setCellValue(xhxRbGrzxqk.getExecuteCount());
        cell2.setCellStyle(cellStyle);
        //通过
        Cell cell3 = row.createCell(3);
        cell3.setCellValue(xhxRbGrzxqk.getPassedCount());
        cell3.setCellStyle(cellStyle);
        //失败
        Cell cell4 = row.createCell(4);
        cell4.setCellValue(xhxRbGrzxqk.getFailedCount());
        cell4.setCellStyle(cellStyle);
        //阻塞
        Cell cell5 = row.createCell(5);
        cell5.setCellValue(xhxRbGrzxqk.getBlockCount());
        cell5.setCellStyle(cellStyle);
        //执行中
        Cell cell6 = row.createCell(6);
        cell6.setCellValue(xhxRbGrzxqk.getExecutingCount());
        cell6.setCellStyle(cellStyle);
        //已取消
        Cell cell7 = row.createCell(7);
        cell7.setCellValue(xhxRbGrzxqk.getCancelCount());
        cell7.setCellStyle(cellStyle);
        //当日已执行
        Cell cell8 = row.createCell(8);
        cell8.setCellValue(xhxRbGrzxqk.getDayExecuteCount());
        cell8.setCellStyle(cellStyle);
        //当日通过
        Cell cell9 = row.createCell(9);
        cell9.setCellValue(xhxRbGrzxqk.getDayPassedCount());
        cell9.setCellStyle(cellStyle);
        //当日失败
        Cell cell10 = row.createCell(10);
        cell10.setCellValue(xhxRbGrzxqk.getDayFailedCount());
        cell10.setCellStyle(cellStyle);
    }

    private void insertValueQxztqk(Row row, XhxRbQxztqk xhxRbQxztqk, Workbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderTop(BorderStyle.THIN);      // 上边框
        cellStyle.setBorderBottom(BorderStyle.THIN);   // 下边框
        cellStyle.setBorderLeft(BorderStyle.THIN);     // 左边框
        cellStyle.setBorderRight(BorderStyle.THIN);    // 右边框

        //系统名称
        Cell cell1 = row.createCell(1);
        cell1.setCellValue(xhxRbQxztqk.getSystemName());
        cell1.setCellStyle(cellStyle);
        //打开
        Cell cell2 = row.createCell(2);
        cell2.setCellValue(xhxRbQxztqk.getOpenCount());
        cell2.setCellStyle(cellStyle);
        //已分派
        Cell cell3 = row.createCell(3);
        cell3.setCellValue(xhxRbQxztqk.getAssignedCount());
        cell3.setCellStyle(cellStyle);
        //修复中
        Cell cell4 = row.createCell(4);
        cell4.setCellValue(xhxRbQxztqk.getRepairingCount());
        cell4.setCellStyle(cellStyle);
        //已修复
        Cell cell5 = row.createCell(5);
        cell5.setCellValue(xhxRbQxztqk.getRepairedCount());
        cell5.setCellStyle(cellStyle);
        //重新打开
        Cell cell6 = row.createCell(6);
        cell6.setCellValue(xhxRbQxztqk.getReopenCount());
        cell6.setCellStyle(cellStyle);
        //带需求确认
        Cell cell7 = row.createCell(7);
        cell7.setCellValue(xhxRbQxztqk.getDxqqrCount());
        cell7.setCellStyle(cellStyle);
        //待确认-新核心
        Cell cell8 = row.createCell(8);
        cell8.setCellValue(xhxRbQxztqk.getDqrxhxCount());
        cell8.setCellStyle(cellStyle);
        //待修改
        Cell cell9 = row.createCell(9);
        cell9.setCellValue(xhxRbQxztqk.getDxgCount());
        cell9.setCellStyle(cellStyle);
        //挂起
        Cell cell10 = row.createCell(10);
        cell10.setCellValue(xhxRbQxztqk.getSuspendCount());
        cell10.setCellStyle(cellStyle);
        //待仲裁
        Cell cell11 = row.createCell(11);
        cell11.setCellValue(xhxRbQxztqk.getDzcCount());
        cell11.setCellStyle(cellStyle);
        //提起测试仲裁
        Cell cell12 = row.createCell(12);
        cell12.setCellValue(xhxRbQxztqk.getTqcszcCount());
        cell12.setCellStyle(cellStyle);
        //提起PMO仲裁
        Cell cell13 = row.createCell(13);
        cell13.setCellValue(xhxRbQxztqk.getTqpmozcCount());
        cell13.setCellStyle(cellStyle);
        //新建
        Cell cell14 = row.createCell(14);
        cell14.setCellValue(xhxRbQxztqk.getNewCount());
        cell14.setCellStyle(cellStyle);
        //拒绝
        Cell cell15 = row.createCell(15);
        cell15.setCellValue(xhxRbQxztqk.getRejectCount());
        cell15.setCellStyle(cellStyle);
        //待复测
        Cell cell16 = row.createCell(16);
        cell16.setCellValue(xhxRbQxztqk.getDfcCount());
        cell16.setCellStyle(cellStyle);
        //关闭
        Cell cell17 = row.createCell(17);
        cell17.setCellValue(xhxRbQxztqk.getClosedCount());
        cell17.setCellStyle(cellStyle);
        //拒绝确认
        Cell cell18 = row.createCell(18);
        cell18.setCellValue(xhxRbQxztqk.getJjqrCount());
        cell18.setCellStyle(cellStyle);
        //总数
        Cell cell19 = row.createCell(19);
        cell19.setCellValue(xhxRbQxztqk.getTotalCount());
        cell19.setCellStyle(cellStyle);
    }

    @Override
    public void prodSampleEsbXml() {
        //DBConnectionDto dbConnectionDto = new DBConnectionDto("***********************************", "db2inst2", "123456");
        //HikariDataSource dataSource = DBUtil.createDataSource(dbConnectionDto, 2, 4);
        ////String[] intfIdArr = {"101010", "101014", "101015", "101016", "101021", "101028", "101030", "101035", "101036", "101037", "101044", "101045", "101046", "101050", "101051"};
        //String[] intfIdArr = {"101014"};
        //try {
        //    for (String intfId : intfIdArr) {
        //        HashMap<String, String> reqtMap = new HashMap<>();
        //        HashMap<String, String> respMap = new HashMap<>();
        //        String sql = "SELECT * FROM (SELECT * FROM SVCGOV.T_SVC_TRACE_1 WHERE INTF_ID = '" + intfId + "' UNION ALL SELECT * FROM SVCGOV.T_SVC_TRACE_2 WHERE INTF_ID = '" + intfId + "') WHERE EVENT_TYPE_NM = '接收HTTP请求'";
        //        Connection connection0 = dataSource.getConnection();
        //        PreparedStatement preparedStatement = StatementUtil.prepareStatement(connection0, sql);
        //        ResultSet resultSet = preparedStatement.executeQuery();
        //        while (resultSet.next()) {
        //            String svcMsgChar = resultSet.getString("SVC_MSG_CHAR");
        //            String requestBody = ESBUtil.getRequestBody(svcMsgChar);
        //            if (!FormatCheckUtil.isValidXml(requestBody)) {
        //                System.out.println("invalid xml");
        //                continue;
        //            }
        //            Element rootElement = new SAXReader().read(new StringReader(requestBody)).getRootElement();
        //            String csmrId = rootElement.element("svcHdr").element("csmrId").getText();
        //            String chl = rootElement.element("appHdr").element("chl").getText();
        //            if (reqtMap.containsKey(chl)) {
        //                continue;
        //            }
        //            String svcCorrId = resultSet.getString("SVC_CORR_ID");
        //            String respSql = "SELECT * FROM (SELECT * FROM SVCGOV.T_SVC_TRACE_1 UNION ALL SELECT * FROM SVCGOV.T_SVC_TRACE_2) WHERE SVC_CORR_ID = '" + svcCorrId + "' AND EVENT_TYPE_NM = '返回HTTP响应'";
        //            Connection connection = dataSource.getConnection();
        //            PreparedStatement preparedStatement1 = StatementUtil.prepareStatement(connection, respSql);
        //            ResultSet resultSet1 = preparedStatement1.executeQuery();
        //            resultSet1.next();
        //            String respBody = resultSet1.getString("SVC_MSG_CHAR");
        //            reqtMap.put(chl, requestBody);
        //            respMap.put(chl, respBody);
        //            resultSet1.close();
        //            preparedStatement1.close();
        //            connection.close();
        //        }
        //        System.out.println(reqtMap.keySet());
        //        System.out.println(reqtMap.get("CCPS"));
        //        System.out.println(respMap.get("CCPS"));
        //        //reqtMap.forEach((k, v) -> {
        //        //    FileUtil.writeString(v, "C:\\Users\\<USER>\\Desktop\\" + intfId + "\\" + k + "-请求.xml", "UTF-8");
        //        //    FileUtil.writeString(respMap.get(k), "C:\\Users\\<USER>\\Desktop\\" + intfId + "\\" + k + "-响应.xml", "UTF-8");
        //        //});
        //        resultSet.close();
        //        preparedStatement.close();
        //        connection0.close();
        //    }
        //} catch (Exception e) {
        //    throw new RuntimeException(e);
        //}
    }

}
