package com.bqd.serviceenr.service;

import com.bqd.model.esbnetworkreplay.RequestResendFormDto;
import com.bqd.model.esbnetworkreplay.RecordDBEnv;
import com.bqd.model.esbnetworkreplay.RequestResendDBInfo;

import java.util.List;

public interface ConfigService {
    List<RecordDBEnv> getRecordDBEnv();

    void addRecordDBEnv(RecordDBEnv recordDBEnv);

    void updateRecordDBEnv(RecordDBEnv recordDBEnv);

    void deleteRecordDBEnv(String id);

    List<RequestResendDBInfo> getRequestResendDBList();

    void addRequestResendDBInfo(RequestResendDBInfo requestResendDBInfo);

    void updateRequestResendDBInfo(RequestResendDBInfo requestResendDBInfo);

    void deleteRequestResendDBEnv(String id);

    boolean testDBConnection(String id);

    void requestResend(RequestResendFormDto requestResendFormDto);
}
