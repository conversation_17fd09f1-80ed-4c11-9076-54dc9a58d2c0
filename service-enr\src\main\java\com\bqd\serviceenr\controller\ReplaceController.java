package com.bqd.serviceenr.controller;

import com.bqd.base.response.Response;
import com.bqd.model.esbnetworkreplay.EsbReplaceField;
import com.bqd.model.esbnetworkreplay.EsbReplaceVariable;
import com.bqd.serviceenr.service.ReplaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: TODO
 * @Author: wangzirui
 * @CreateTime: 2024-08-27
 */
@RestController
@RequestMapping("/replace")
public class ReplaceController {

    @Autowired
    private ReplaceService replaceService;

    @PostMapping("/addReplaceField")
    public Response addReplaceField(@RequestBody EsbReplaceField esbReplaceField) {
        replaceService.addReplaceField(esbReplaceField);
        return Response.success();
    }

    @GetMapping("/getReplaceField")
    public Response getReplaceField(@RequestParam("interfaceId") String interfaceId) {
        return Response.success(replaceService.getReplaceField(interfaceId));
    }

    @GetMapping("/deleteReplaceField")
    public Response deleteReplaceField(@RequestParam("id") String id) {
        replaceService.deleteReplaceField(id);
        return Response.success();
    }

    @PostMapping("/addReplaceVariable")
    public Response addReplaceVaribale(@RequestBody EsbReplaceVariable esbReplaceVariable) {
        replaceService.addReplaceVaribale(esbReplaceVariable);
        return Response.success();
    }

    @GetMapping("/getReplaceVariable")
    public Response getReplaceVariable() {
        return Response.success(replaceService.getReplaceVariable());
    }

    @GetMapping("/deleteReplaceVariable")
    public Response deleteReplaceVariable(@RequestParam("id") String id) {
        replaceService.deleteReplaceVariable(id);
        return Response.success();
    }

}
