<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bqd.dboraclebeta.xhx.mapper.XhxRbZxztqkMapper">
    <resultMap id="xhxRbZxztqk" type="com.bqd.model.xhxrb.XhxRbZxztqk">
        <result column="WORKSPACE_NAME" property="workspaceName" javaType="java.lang.String"/>
        <result column="SYSTEM_NAME" property="systemName" javaType="java.lang.String"/>
        <result column="TESTSET_NAME" property="testsetName" javaType="java.lang.String"/>
        <result column="CASE_COUNT" property="caseCount" javaType="java.lang.Integer"/>
        <result column="EXECUTE_COUNT" property="executeCount" javaType="java.lang.Integer"/>
        <result column="PASSED_COUNT" property="passedCount" javaType="java.lang.Integer"/>
        <result column="FAILED_COUNT" property="failedCount" javaType="java.lang.Integer"/>
        <result column="BLOCK_COUNT" property="blockCount" javaType="java.lang.Integer"/>
        <result column="EXECUTING_COUNT" property="executingCount" javaType="java.lang.Integer"/>
        <result column="CANCEL_COUNT" property="cancelCount" javaType="java.lang.Integer"/>
        <result column="TODO_COUNT" property="todoCount" javaType="java.lang.Integer"/>
        <result column="EXECUTE_RATE" property="executeRate" javaType="java.lang.Double"/>
        <result column="PLAN_PROGRESS" property="planProgress" javaType="java.lang.Double"/>
        <result column="PROGRESS_BIAS" property="progressBias" javaType="java.lang.Double"/>
        <result column="PASS_RATE" property="passRate" javaType="java.lang.Double"/>
        <result column="VALID_BUG_COUNT" property="validBugCount" javaType="java.lang.Integer"/>
        <result column="TOTAL_BUG_RATE" property="totalBugRate" javaType="java.lang.Double"/>
        <result column="DAY_EXECUTE_COUNT" property="dayExecuteCount" javaType="java.lang.Integer"/>
        <result column="DAY_PASSED_COUNT" property="dayPassedCount" javaType="java.lang.Integer"/>
        <result column="DAY_FAILED_COUNT" property="dayFailedCount" javaType="java.lang.Integer"/>
    </resultMap>

    <select id="selectAllDesc" resultMap="xhxRbZxztqk">
        SELECT *
        FROM T_B_XHX_RB_ZXZTQK
        ORDER BY SYSTEM_NAME, TESTSET_NAME DESC
    </select>

    <update id="pcdInsertNames">
        CALL ZXZTQK_INSERT_NAMES()
    </update>

    <update id="pcdInsertTestsetTotal">
        CALL ZXZTQK_INSERT_TESTSET_TOTAL()
    </update>

    <update id="pcdUpdateCaseCount">
        CALL ZXZTQK_CASE_COUNT()
    </update>

    <update id="pcdUpdateExecuteCount">
        CALL ZXZTQK_EXECUTE_COUNT()
    </update>

    <update id="pcdUpdatePassedCount">
        CALL ZXZTQK_PASSED_COUNT()
    </update>

    <update id="pcdUpdateFailedCount">
        CALL ZXZTQK_FAILED_COUNT()
    </update>

    <update id="pcdUpdateBlockCount">
        CALL ZXZTQK_BLOCK_COUNT()
    </update>

    <update id="pcdUpdateExecutingCount">
        CALL ZXZTQK_EXECUTING_COUNT()
    </update>

    <update id="pcdUpdateCancelCount">
        CALL ZXZTQK_CANCEL_COUNT()
    </update>

    <update id="pcdUpdateTodoCount">
        CALL ZXZTQK_TODO_COUNT()
    </update>

    <update id="pcdUpdateExecuteRate">
        CALL ZXZTQK_EXECUTE_RATE()
    </update>

    <update id="pcdUpdatePlanProgress">
        CALL ZXZTQK_PLAN_PROGRESS(#{roundName})
    </update>

    <update id="pcdUpdateProgressBias">
        CALL ZXZTQK_PROGRESS_BIAS()
    </update>

    <update id="pcdUpdatePassRate">
        CALL ZXZTQK_PASS_RATE()
    </update>

    <update id="pcdUpdateValidBugCount">
        CALL ZXZTQK_VALID_BUG_COUNT(#{roundName}, #{systemPrefix})
    </update>

    <update id="pcdUpdateDayExecuteCount">
        CALL ZXZTQK_DAY_EXECUTE_COUNT(TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS'), TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS'))
    </update>

    <update id="pcdUpdateDayPassedCount">
        CALL ZXZTQK_DAY_PASSED_COUNT(TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS'), TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS'))
    </update>

    <update id="pcdUpdateDayFailedCount">
        CALL ZXZTQK_DAY_FAILED_COUNT(TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS'), TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS'))
    </update>

    <update id="pcdUpdateTotalBugRate">
        CALL ZXZTQK_TOTAL_BUG_RATE()
    </update>
</mapper>
