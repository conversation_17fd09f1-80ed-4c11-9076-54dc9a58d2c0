package com.bqd.model.esbnetworkreplay;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 录制搜索下拉框数据dto
 * @Author: wang<PERSON><PERSON>i
 * @CreateTime: 2024-08-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordSearchFilterDto extends ENRCommonRespDto {
    private String channelId;
    private String transCde;
    private String csmrId;
    private String versionNumber;
    private String esbRespMsg;
    private String esbRespCode;
    private String providerId;
}
